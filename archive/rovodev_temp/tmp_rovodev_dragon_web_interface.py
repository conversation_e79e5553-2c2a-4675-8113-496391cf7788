#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十二龙子心易射覆系统 - Web界面
为客户提供在线射覆服务
"""

from flask import Flask, render_template, request, jsonify
import json
from datetime import datetime
from tmp_rovodev_dragon_divination_system import DragonDivinationSystem

app = Flask(__name__)
divination_system = DragonDivinationSystem()

@app.route('/')
def index():
    """主页"""
    return render_template('dragon_divination.html')

@app.route('/api/divine', methods=['POST'])
def divine():
    """射覆API"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        questioner_info = data.get('questioner_info', {})
        
        if not question.strip():
            return jsonify({'error': '请输入您的问题'}), 400
        
        # 执行射覆
        result = divination_system.divine(question, questioner_info)
        
        # 转换为JSON可序列化的格式
        response = {
            'primary_dragon': {
                'number': result.primary_dragon.number,
                'name': result.primary_dragon.name,
                'mother': result.primary_dragon.mother,
                'function': result.primary_dragon.function,
                'month': result.primary_dragon.month
            },
            'secondary_dragon': {
                'number': result.secondary_dragon.number,
                'name': result.secondary_dragon.name,
                'mother': result.secondary_dragon.mother,
                'function': result.secondary_dragon.function,
                'month': result.secondary_dragon.month
            },
            'interpretation': result.interpretation,
            'confidence': result.confidence,
            'timestamp': result.context.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'environmental_factors': result.context.environmental_factors
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': f'射覆过程出错: {str(e)}'}), 500

@app.route('/api/dragon_info/<int:dragon_number>')
def dragon_info(dragon_number):
    """获取龙子详细信息"""
    try:
        dragon = divination_system.get_dragon_by_number(dragon_number)
        
        # 龙子详细信息（基于您的《心易雕龙歌》）
        dragon_details = {
            1: {
                "poem": "囚牛低首似伏牛，乐律声声自心流。万物谐和礼乐起，千载道衍戎祀求。",
                "meaning": "建立秩序，以礼乐化人，是系统创始阶段的核心力量。",
                "advice": "当前宜以德服人，建立规范，用文化凝聚人心。"
            },
            2: {
                "poem": "睚眦在地豺狼形，复仇女神召必赢。相报冤冤何时停，血债笔笔今日平。",
                "meaning": "执法者的化身，确保规则不被破坏，维护系统的底线。",
                "advice": "面对违规行为需要果断处理，但要避免过度严厉。"
            },
            3: {
                "poem": "狻猊端坐享香火，三宝大殿绕梵音。讲经说法灌顶礼，脱胎换骨洗髓经。",
                "meaning": "智慧的象征，代表学习、思考和精神升华。",
                "advice": "当前适合学习新知识，寻求智慧指导，提升内在修养。"
            },
            4: {
                "poem": "寒山古寺千年钟，蒲牢为罩声如洪。北冥海深鲲化鹏，南山池浅鱼跃龙。",
                "meaning": "宣告与融合的力量，将内在成果转化为外在影响。",
                "advice": "时机成熟，可以公开宣告理念，扩大影响力。"
            },
            5: {
                "poem": "凤鸣岐山天机变，牧野之战血玄黄。怒起关中狂风起，雷至朝歌骤雨滂。",
                "meaning": "预警系统，能够感知潜在的危机和变化。",
                "advice": "需要保持高度警觉，关注环境变化，提前做好应对准备。"
            },
            6: {
                "poem": "狴犴升堂天地覆，回避肃静惊堂木。厉兵武昌满江红，秣马贺兰千里路。",
                "meaning": "司法与公正的象征，但也可能面临内部矛盾。",
                "advice": "面临重大决策时要坚持公正原则，避免被私情影响。"
            },
            7: {
                "poem": "神龟献图定九州，功德无量岁月偷。燕王扫北万户空，建文出海一叶舟。",
                "meaning": "文化重建者，用文明来疗愈创伤，积累底蕴。",
                "advice": "适合进行文化建设，积累知识财富，为长远发展打基础。"
            },
            8: {
                "poem": "负屃戏珠碑上客，赑屃负重欲昂头。张骞出使怀远柔，班超从戎解近忧。",
                "meaning": "对外开拓的先锋，以成熟的策略拓展新领域。",
                "advice": "适合对外发展，寻求新的合作机会和发展空间。"
            },
            9: {
                "poem": "木屋畏火水来克，螭吻枕脊免灾祸。东瀛金阁涅槃生，敦煌莫高平野阔。",
                "meaning": "内部净化与风险管理，保持系统健康运行。",
                "advice": "需要进行内部调整，清除不良因素，优化运行机制。"
            },
            10: {
                "poem": "八部天龙小白虬，驮经得果今再出。禹皇息壤洪灾平，蚣蝮镇河水自疏。",
                "meaning": "守成智慧，以不争的方式维护稳定。",
                "advice": "宜采取守势，以稳为主，通过深沉的智慧化解问题。"
            },
            11: {
                "poem": "一言九鼎饕餮纹，桀骜枭鹰翩入魂。八神星罗盘遁甲，六仪棋布弄奇门。",
                "meaning": "战略储备者，为未来的挑战积累资源。",
                "advice": "应该积极积累各种资源，为即将到来的机遇做准备。"
            },
            12: {
                "poem": "貔貅本为罴熊误，只进不出冬眠故。不知有汉天子出，无论魏晋小民入。",
                "meaning": "终极成果，但也可能导致与世隔绝。",
                "advice": "可以享受成果，但要避免过度保守，保持与外界的联系。"
            }
        }
        
        detail = dragon_details.get(dragon_number, {
            "poem": "龙子神秘，待解其意。",
            "meaning": "此龙子的深层含义有待进一步探索。",
            "advice": "请结合具体情况灵活理解。"
        })
        
        response = {
            'number': dragon.number,
            'name': dragon.name,
            'mother': dragon.mother,
            'function': dragon.function,
            'month': dragon.month,
            'poem': detail['poem'],
            'meaning': detail['meaning'],
            'advice': detail['advice']
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': f'获取龙子信息出错: {str(e)}'}), 500

if __name__ == '__main__':
    print("🐉 启动十二龙子心易射覆系统...")
    print("🌐 访问地址: http://localhost:10000")
    app.run(debug=True, host='0.0.0.0', port=10000)