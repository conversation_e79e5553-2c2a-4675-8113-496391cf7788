#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十二龙子系统与现有项目集成演示
展示如何将龙子射覆系统集成到您的六行星知识图谱项目中
"""

import json
import sys
from datetime import datetime
from tmp_rovodev_dragon_divination_system import DragonDivinationSystem

def integrate_with_existing_project():
    """与现有项目集成演示"""
    print("🐉 十二龙子系统集成演示")
    print("="*60)
    
    # 初始化龙子系统
    dragon_system = DragonDivinationSystem()
    
    # 模拟从现有项目获取数据
    print("📚 从现有项目加载数据...")
    
    # 1. 从史记数据中获取天象信息
    try:
        with open('tianwen_wuxing_data.json', 'r', encoding='utf-8') as f:
            tianwen_data = json.load(f)
        print(f"✅ 成功加载天象数据：{tianwen_data['metadata']['total_tianxiang_records']}条记录")
    except FileNotFoundError:
        print("⚠️ 未找到天象数据文件，使用模拟数据")
        tianwen_data = {"tianxiang_records": []}
    
    # 2. 模拟客户咨询场景
    client_scenarios = [
        {
            "client": "科技公司CEO",
            "question": "我们的AI项目是否应该继续投入？",
            "context": "公司正在开发大语言模型，但成本很高，市场竞争激烈"
        },
        {
            "client": "投资人",
            "question": "现在是否适合投资区块链项目？",
            "context": "看到很多区块链项目，但不确定时机是否合适"
        },
        {
            "client": "创业者",
            "question": "团队内部出现分歧，如何处理？",
            "context": "技术团队和市场团队对产品方向有不同看法"
        }
    ]
    
    print("\n🎯 客户咨询演示：")
    print("="*60)
    
    for i, scenario in enumerate(client_scenarios, 1):
        print(f"\n【案例 {i}】客户：{scenario['client']}")
        print(f"问题：{scenario['question']}")
        print(f"背景：{scenario['context']}")
        print("-" * 40)
        
        # 执行射覆
        result = dragon_system.divine(
            scenario['question'], 
            {"name": scenario['client'], "context": scenario['context']}
        )
        
        print(f"🐉 主卦：{result.primary_dragon.name}（{result.primary_dragon.function}）")
        print(f"🔄 变卦：{result.secondary_dragon.name}（{result.secondary_dragon.function}）")
        print(f"📊 置信度：{result.confidence:.2f}")
        
        # 简化版解释
        interpretation_lines = result.interpretation.split('\n')
        key_lines = [line for line in interpretation_lines if line.strip() and ('建议' in line or '分析' in line)]
        if key_lines:
            print("💡 核心建议：")
            for line in key_lines[:2]:  # 只显示前两条关键建议
                print(f"   {line.strip()}")
        
        print("="*60)

def demonstrate_dragon_起卦_methods():
    """演示不同的起卦方法"""
    print("\n🔮 龙子起卦方法演示")
    print("="*60)
    
    dragon_system = DragonDivinationSystem()
    
    # 演示不同起卦方法的差异
    test_question = "项目发展方向如何选择？"
    
    print(f"测试问题：{test_question}")
    print("-" * 40)
    
    # 方法1：时间起卦（当前时间）
    result1 = dragon_system.divine(test_question)
    print(f"⏰ 时间起卦：{result1.primary_dragon.name} → {result1.secondary_dragon.name}")
    
    # 方法2：模拟不同时间起卦
    import time
    time.sleep(1)  # 等待1秒，改变时间
    result2 = dragon_system.divine(test_question)
    print(f"⏰ 延时起卦：{result2.primary_dragon.name} → {result2.secondary_dragon.name}")
    
    # 方法3：不同问题长度的影响
    long_question = test_question + "，需要考虑市场环境、技术可行性、团队能力、资金状况等多个因素。"
    result3 = dragon_system.divine(long_question)
    print(f"📝 详细问题：{result3.primary_dragon.name} → {result3.secondary_dragon.name}")
    
    print("\n💡 观察：不同的起卦条件会产生不同的卦象组合")

def show_dragon_life_cycle():
    """展示十二龙子的生命周期理论"""
    print("\n🌟 十二龙子生命周期理论")
    print("="*60)
    
    life_cycle_stages = [
        {"stage": "生", "dragons": [1,2,3,4], "description": "理想的开创阶段"},
        {"stage": "住", "dragons": [5,6,7,8], "description": "现实的磨砺阶段"}, 
        {"stage": "坏", "dragons": [9,10,11], "description": "成熟的守成阶段"},
        {"stage": "灭", "dragons": [12], "description": "终极的隔绝阶段"}
    ]
    
    dragon_system = DragonDivinationSystem()
    
    for stage_info in life_cycle_stages:
        print(f"\n【{stage_info['stage']}】{stage_info['description']}")
        for dragon_num in stage_info['dragons']:
            dragon = dragon_system.get_dragon_by_number(dragon_num)
            print(f"  {dragon_num:2d}. {dragon.name}（{dragon.mother}）- {dragon.function}")
    
    print(f"\n🔄 这个周期体现了从创生到终结的完整过程")
    print(f"💡 您的《心易雕龙歌》将此理论诗化，形成了独特的决策框架")

def create_api_integration_example():
    """创建API集成示例"""
    print("\n🔌 API集成示例")
    print("="*60)
    
    api_example = '''
# 集成到现有Flask应用的示例代码：

from tmp_rovodev_dragon_divination_system import DragonDivinationSystem

# 在您的主应用中初始化
dragon_system = DragonDivinationSystem()

@app.route('/api/dragon_consult', methods=['POST'])
def dragon_consult():
    """龙子咨询API"""
    data = request.get_json()
    question = data.get('question')
    user_info = data.get('user_info', {})
    
    # 执行射覆
    result = dragon_system.divine(question, user_info)
    
    # 返回结果
    return jsonify({
        'primary_dragon': result.primary_dragon.name,
        'secondary_dragon': result.secondary_dragon.name,
        'interpretation': result.interpretation,
        'confidence': result.confidence,
        'timestamp': result.context.timestamp.isoformat()
    })

# 与Neo4j知识图谱集成
@app.route('/api/dragon_knowledge', methods=['GET'])
def dragon_knowledge():
    """获取龙子相关的历史知识"""
    dragon_name = request.args.get('dragon_name')
    
    # 从Neo4j查询相关历史记录
    with driver.session() as session:
        result = session.run("""
            MATCH (p:Person)-[r]->(e:Event)
            WHERE e.description CONTAINS $dragon_name
            RETURN p.name, e.description, r.type
            LIMIT 10
        """, dragon_name=dragon_name)
        
        records = [dict(record) for record in result]
    
    return jsonify(records)
    '''
    
    print(api_example)

def main():
    """主演示函数"""
    try:
        print("🚀 十二龙子心易射覆系统 - 项目集成演示")
        print("基于《心易雕龙歌》的智慧决策系统")
        print("="*80)
        
        # 1. 集成演示
        integrate_with_existing_project()
        
        # 2. 起卦方法演示
        demonstrate_dragon_起卦_methods()
        
        # 3. 生命周期理论
        show_dragon_life_cycle()
        
        # 4. API集成示例
        create_api_integration_example()
        
        print("\n" + "="*80)
        print("🎉 演示完成！")
        print("\n📋 下一步建议：")
        print("1. 将龙子系统集成到您的主Flask应用中")
        print("2. 在Neo4j中创建龙子相关的节点和关系")
        print("3. 结合天象数据优化起卦算法")
        print("4. 为不同类型的客户定制解释模板")
        print("5. 添加历史验证功能，追踪预测准确性")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()