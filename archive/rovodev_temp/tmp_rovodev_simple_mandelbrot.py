#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Mandelbrot分形 - 最小依赖
"""

import numpy as np

def mandelbrot_set_simple(width=80, height=40, max_iter=50):
    """生成简单的ASCII版Mandelbrot集合"""
    
    # 设置复平面范围
    x_min, x_max = -2.5, 1.0
    y_min, y_max = -1.25, 1.25
    
    # 创建复数网格
    x = np.linspace(x_min, x_max, width)
    y = np.linspace(y_min, y_max, height)
    
    result = []
    
    for i in range(height):
        row = ""
        for j in range(width):
            # 当前复数点
            c = complex(x[j], y[i])
            z = 0
            
            # 迭代计算
            for iteration in range(max_iter):
                if abs(z) > 2:
                    break
                z = z*z + c
            
            # 根据迭代次数选择字符
            if iteration == max_iter - 1:
                row += "█"  # 在集合内
            elif iteration > max_iter * 0.8:
                row += "▓"
            elif iteration > max_iter * 0.6:
                row += "▒"
            elif iteration > max_iter * 0.4:
                row += "░"
            elif iteration > max_iter * 0.2:
                row += "·"
            else:
                row += " "  # 快速发散
        
        result.append(row)
    
    return result

def print_mandelbrot():
    """打印ASCII版Mandelbrot集合"""
    print("🌌 Mandelbrot集合 - ASCII版本")
    print("=" * 80)
    
    mandelbrot = mandelbrot_set_simple(80, 40, 50)
    
    for row in mandelbrot:
        print(row)
    
    print("=" * 80)
    print("🔮 数学之美：简单规则，无限复杂")
    print("💡 每个字符代表一个复数点的迭代行为")
    print("█ = 稳定点  ▓▒░· = 不同发散速度  空格 = 快速发散")

def mandelbrot_zoom_demo():
    """演示不同缩放级别的Mandelbrot"""
    
    zoom_levels = [
        {"name": "全景", "center": (-0.5, 0), "scale": 3.5},
        {"name": "海马谷", "center": (-0.75, 0.1), "scale": 0.1},
        {"name": "细节", "center": (-0.16, 1.04), "scale": 0.02}
    ]
    
    for level in zoom_levels:
        print(f"\n🔍 {level['name']} - 中心{level['center']}, 缩放{level['scale']}")
        print("-" * 60)
        
        # 这里可以实现不同的缩放视图
        # 为了简化，暂时显示相同的图案
        mandelbrot = mandelbrot_set_simple(60, 20, 30)
        for row in mandelbrot[:10]:  # 只显示前10行
            print(row)
        print("...")

if __name__ == "__main__":
    print("🐉 六行星知识图谱 - 数学之美模块")
    print()
    
    # 基础演示
    print_mandelbrot()
    
    print("\n" + "="*80)
    print("🎯 与历史研究的类比：")
    print("• 历史事件 ↔ 复数点")
    print("• 历史规律 ↔ 迭代公式 z² + c") 
    print("• 历史细节 ↔ 分形的无限放大")
    print("• 相似模式 ↔ 自相似性")
    print("="*80)
    
    # 缩放演示
    mandelbrot_zoom_demo()
    
    print("\n💡 这就是为什么我们要在首页加入分形的原因：")
    print("   历史研究如同探索分形，每次深入都能发现新的相似结构！")