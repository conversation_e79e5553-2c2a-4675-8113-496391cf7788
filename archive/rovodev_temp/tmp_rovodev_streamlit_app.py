#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🐉 六行星知识图谱 - Streamlit完整应用
集成十二龙子射覆系统 + Mandelbrot分形之美
"""

import streamlit as st
import numpy as np
import json
from datetime import datetime
import time
import hashlib

# 导入我们的龙子系统
try:
    from tmp_rovodev_dragon_divination_system import DragonDivinationSystem, DragonSon
    DRAGON_SYSTEM_AVAILABLE = True
except ImportError:
    DRAGON_SYSTEM_AVAILABLE = False

def mandelbrot_set_ascii(width=60, height=30, max_iter=50, center=(-0.5, 0), zoom=1):
    """生成ASCII版Mandelbrot集合"""
    
    # 计算复平面范围
    scale = 3.0 / zoom
    x_min, x_max = center[0] - scale/2, center[0] + scale/2
    y_min, y_max = center[1] - scale*0.6/2, center[1] + scale*0.6/2
    
    # 创建复数网格
    x = np.linspace(x_min, x_max, width)
    y = np.linspace(y_min, y_max, height)
    
    result = []
    
    for i in range(height):
        row = ""
        for j in range(width):
            # 当前复数点
            c = complex(x[j], y[i])
            z = 0
            
            # 迭代计算
            for iteration in range(max_iter):
                if abs(z) > 2:
                    break
                z = z*z + c
            
            # 根据迭代次数选择字符
            if iteration == max_iter - 1:
                row += "█"  # 在集合内
            elif iteration > max_iter * 0.8:
                row += "▓"
            elif iteration > max_iter * 0.6:
                row += "▒"
            elif iteration > max_iter * 0.4:
                row += "░"
            elif iteration > max_iter * 0.2:
                row += "·"
            else:
                row += " "  # 快速发散
        
        result.append(row)
    
    return result

def create_homepage():
    """创建首页"""
    st.title("🐉 六行星知识图谱")
    st.markdown("### *探索历史的分形结构，洞察古今的智慧密码*")
    
    # 创建两列布局
    col1, col2 = st.columns([1.2, 1])
    
    with col1:
        st.markdown("### 🌟 系统特色")
        
        st.markdown("""
        **🔮 十二龙子心易射覆**
        - 基于《心易雕龙歌》的原创理论
        - 四阶段生命周期决策框架
        - 结合天象数据的智能起卦
        
        **📚 史记知识图谱**
        - 深度挖掘历史文献关联
        - 1,215个术数方法科技树
        - 天象记录与人事的对应分析
        
        **🌌 数学分形探索**
        - 历史模式的自相似性研究
        - 复杂系统的边界分析
        - 古代智慧的现代数学表达
        """)
        
        # 功能入口按钮
        st.markdown("### 🎯 快速入口")
        
        col_a, col_b = st.columns(2)
        with col_a:
            if st.button("🔮 开始射覆", use_container_width=True, type="primary"):
                st.session_state.current_page = "dragon"
                st.rerun()
            
            if st.button("📊 知识图谱", use_container_width=True):
                st.session_state.current_page = "knowledge"
                st.rerun()
        
        with col_b:
            if st.button("🌌 分形之美", use_container_width=True):
                st.session_state.current_page = "fractal"
                st.rerun()
            
            if st.button("📈 数据分析", use_container_width=True):
                st.session_state.current_page = "analysis"
                st.rerun()
    
    with col2:
        st.markdown("### 🌌 历史的分形之美")
        
        # 显示Mandelbrot分形
        mandelbrot_ascii = mandelbrot_set_ascii(50, 25, 40)
        
        # 创建美观的显示
        mandelbrot_text = "\n".join(mandelbrot_ascii)
        
        st.code(mandelbrot_text, language=None)
        
        st.markdown("""
        > *"历史如分形，简单的人性规律  
        > 却能产生无限复杂的历史模式。  
        > 每一次深入研究，都能发现  
        > 新的细节和相似的结构。"*
        
        **分形与历史的对应：**
        - `█` 历史的稳定核心期
        - `▓` 变革的关键节点  
        - `▒` 动荡的过渡时期
        - `░` 快速变化的时代
        - `·` 历史的细微波动
        """)

def create_dragon_page():
    """创建龙子射覆页面"""
    st.title("🔮 十二龙子心易射覆")
    st.markdown("### *基于《心易雕龙歌》的智慧决策系统*")
    
    if not DRAGON_SYSTEM_AVAILABLE:
        st.error("龙子系统模块未找到，请检查导入路径")
        return
    
    # 创建两列布局
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("### 📝 请输入您的问题")
        
        # 问题输入
        question = st.text_area(
            "问题描述",
            placeholder="请详细描述您想要咨询的问题...",
            height=120
        )
        
        # 用户信息
        with st.expander("🔧 可选信息"):
            name = st.text_input("姓名", placeholder="您的姓名")
            role = st.text_input("身份", placeholder="如：企业家、学生、工程师等")
        
        # 射覆按钮
        if st.button("🔮 开始射覆", type="primary", use_container_width=True):
            if question.strip():
                with st.spinner("正在为您射覆..."):
                    try:
                        # 初始化龙子系统
                        dragon_system = DragonDivinationSystem()
                        
                        # 执行射覆
                        result = dragon_system.divine(
                            question, 
                            {"name": name, "role": role}
                        )
                        
                        # 保存结果到session state
                        st.session_state.divination_result = {
                            "primary_dragon": {
                                "name": result.primary_dragon.dragon_name,
                                "mother": result.primary_dragon.mother,
                                "function": result.primary_dragon.function,
                                "month": result.primary_dragon.month
                            },
                            "secondary_dragon": {
                                "name": result.secondary_dragon.dragon_name,
                                "mother": result.secondary_dragon.mother,
                                "function": result.secondary_dragon.function,
                                "month": result.secondary_dragon.month
                            },
                            "interpretation": result.interpretation,
                            "confidence": result.confidence,
                            "timestamp": result.context.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        st.success("射覆完成！")
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"射覆过程出错：{str(e)}")
            else:
                st.warning("请输入您的问题")
    
    with col2:
        st.markdown("### 📊 射覆结果")
        
        if 'divination_result' in st.session_state:
            result = st.session_state.divination_result
            
            # 主卦
            with st.container():
                st.markdown("#### 🐉 主卦")
                st.info(f"""
                **{result['primary_dragon']['name']}**（{result['primary_dragon']['mother']}）
                
                **功能**：{result['primary_dragon']['function']}  
                **月份**：{result['primary_dragon']['month']}月
                """)
            
            # 变卦
            with st.container():
                st.markdown("#### 🔄 变卦")
                st.info(f"""
                **{result['secondary_dragon']['name']}**（{result['secondary_dragon']['mother']}）
                
                **功能**：{result['secondary_dragon']['function']}  
                **月份**：{result['secondary_dragon']['month']}月
                """)
            
            # 解释
            st.markdown("#### 💡 详细解释")
            st.text_area("", value=result['interpretation'], height=300, disabled=True)
            
            # 置信度
            confidence_pct = int(result['confidence'] * 100)
            st.metric("置信度", f"{confidence_pct}%")
            st.progress(result['confidence'])
            
            # 时间戳
            st.caption(f"射覆时间：{result['timestamp']}")
            
        else:
            st.info("请在左侧输入问题开始射覆")
    
    # 十二龙子图谱
    st.markdown("---")
    st.markdown("### 🐲 十二龙子图谱")
    
    # 创建4x3的网格显示十二龙子
    dragons_data = [
        (1, "囚牛", "牛", "礼乐戎祀", "子"),
        (2, "睚眦", "豺", "虽远必诛", "丑"),
        (3, "狻猊", "狮", "讲经说法", "寅"),
        (4, "蒲牢", "鲲", "声如洪钟", "卯"),
        (5, "嘲风", "凤", "千里听风", "辰"),
        (6, "狴犴", "虎", "天下为公", "巳"),
        (7, "贔屓", "龟", "文以载道", "午"),
        (8, "负屃", "龙", "东西一通", "未"),
        (9, "螭吻", "鱼", "吐故纳新", "申"),
        (10, "蚣蝮", "虬", "镇守九宫", "酉"),
        (11, "饕餮", "熊", "颗粒归仓", "戌"),
        (12, "貔貅", "罴", "乃成富翁", "亥")
    ]
    
    # 分4行显示
    for row in range(3):
        cols = st.columns(4)
        for col in range(4):
            idx = row * 4 + col
            if idx < len(dragons_data):
                num, name, mother, function, month = dragons_data[idx]
                with cols[col]:
                    st.markdown(f"""
                    <div style="text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 5px;">
                        <h3>{num}</h3>
                        <h4>{name}</h4>
                        <p>母：{mother}</p>
                        <p>{function}</p>
                        <small>{month}月</small>
                    </div>
                    """, unsafe_allow_html=True)

def create_fractal_page():
    """创建分形探索页面"""
    st.title("🌌 数学之美 - 分形探索")
    st.markdown("### *发现历史的自相似结构*")
    
    # 创建两列布局
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("### 🎛️ 分形参数")
        
        # 预设选项
        presets = {
            "经典全景": {"center": (-0.5, 0), "zoom": 1},
            "海马谷": {"center": (-0.75, 0.1), "zoom": 20},
            "闪电": {"center": (-1.25, 0), "zoom": 50},
            "螺旋": {"center": (-0.16, 1.04), "zoom": 100}
        }
        
        preset = st.selectbox("选择预设", list(presets.keys()))
        preset_params = presets[preset]
        
        # 参数调节
        center_x = st.slider("中心X", -2.0, 1.0, preset_params["center"][0], 0.1)
        center_y = st.slider("中心Y", -1.5, 1.5, preset_params["center"][1], 0.1)
        zoom = st.slider("缩放", 1, 200, preset_params["zoom"], 1)
        iterations = st.slider("迭代次数", 20, 100, 50, 10)
        
        # 尺寸选择
        size_options = {
            "小 (40x20)": (40, 20),
            "中 (60x30)": (60, 30),
            "大 (80x40)": (80, 40)
        }
        
        size_choice = st.selectbox("图案尺寸", list(size_options.keys()), index=1)
        width, height = size_options[size_choice]
        
        # 生成按钮
        if st.button("🎨 生成分形", type="primary", use_container_width=True):
            st.session_state.fractal_params = {
                "center": (center_x, center_y),
                "zoom": zoom,
                "iterations": iterations,
                "size": (width, height)
            }
            st.rerun()
        
        # 随机探索
        if st.button("🎲 随机探索", use_container_width=True):
            st.session_state.fractal_params = {
                "center": (np.random.uniform(-1.5, 0.5), np.random.uniform(-1.0, 1.0)),
                "zoom": np.random.randint(1, 100),
                "iterations": np.random.randint(30, 80),
                "size": (width, height)
            }
            st.rerun()
    
    with col2:
        st.markdown("### 🖼️ 分形图案")
        
        # 获取参数
        if 'fractal_params' in st.session_state:
            params = st.session_state.fractal_params
        else:
            params = {
                "center": (-0.5, 0),
                "zoom": 1,
                "iterations": 50,
                "size": (60, 30)
            }
        
        # 生成分形
        with st.spinner("正在生成分形图案..."):
            mandelbrot_ascii = mandelbrot_set_ascii(
                params["size"][0], 
                params["size"][1], 
                params["iterations"],
                params["center"],
                params["zoom"]
            )
        
        # 显示分形
        mandelbrot_text = "\n".join(mandelbrot_ascii)
        st.code(mandelbrot_text, language=None)
        
        # 参数信息
        with st.expander("📊 当前参数"):
            col_a, col_b = st.columns(2)
            with col_a:
                st.metric("中心坐标", f"({params['center'][0]:.2f}, {params['center'][1]:.2f})")
                st.metric("缩放倍数", f"{params['zoom']}x")
            with col_b:
                st.metric("迭代次数", params['iterations'])
                st.metric("图案尺寸", f"{params['size'][0]}x{params['size'][1]}")
    
    # 数学原理说明
    st.markdown("---")
    with st.expander("🧮 数学原理与历史类比"):
        st.markdown("""
        ### Mandelbrot集合的定义
        
        对于复数 c，序列 z₀ = 0, z_{n+1} = z_n² + c 保持有界的所有 c 的集合。
        
        ### 与历史研究的深度类比
        
        **🔄 自相似性**
        - 分形：任意放大都能看到相似结构
        - 历史：不同时代有相似的兴衰模式
        
        **🌀 无限复杂性**
        - 分形：边界具有无限细节
        - 历史：每个事件都可以无限深入研究
        
        **⚡ 敏感依赖性**
        - 分形：微小的参数变化导致巨大差异
        - 历史：小事件可能改变历史走向
        
        **🎨 简单规则，复杂结果**
        - 分形：z² + c 的简单公式产生复杂图案
        - 历史：人性的基本规律产生复杂历史
        """)

def create_knowledge_page():
    """创建知识图谱页面"""
    st.title("📊 史记知识图谱")
    st.markdown("### *探索历史文献的深层关联*")
    
    # 概念中心性分析结果
    st.markdown("### 🎯 术数概念中心性排名")
    
    concept_centrality = [
        ("五行", 492),
        ("阴阳", 471),
        ("地支", 262),
        ("天干", 260),
        ("占卜", 251),
        ("风水", 224),
        ("八卦", 208),
        ("后天", 200),
        ("六壬", 153),
        ("太乙", 142)
    ]
    
    # 创建可视化
    for i, (concept, connections) in enumerate(concept_centrality):
        col1, col2, col3 = st.columns([1, 3, 1])
        
        with col1:
            if i == 0:
                st.markdown("🥇")
            elif i == 1:
                st.markdown("🥈")
            elif i == 2:
                st.markdown("🥉")
            else:
                st.markdown(f"{i+1}")
        
        with col2:
            st.markdown(f"**{concept}**")
            progress_value = connections / 500  # 归一化
            st.progress(progress_value)
        
        with col3:
            st.markdown(f"{connections}个连接")
    
    st.markdown("---")
    
    # 统计信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("术数方法", "1,215个", "已分析")
    
    with col2:
        st.metric("核心概念", "24个", "已识别")
    
    with col3:
        st.metric("相关人物", "554位", "已关联")
    
    with col4:
        st.metric("典籍文献", "383本", "已处理")
    
    # 重要发现
    st.markdown("### 🔍 重要发现")
    
    st.info("""
    **🌟 核心发现：五行是术数宇宙的绝对中心！**
    
    - **五行**连接了492个术数方法，超过**阴阳**（471个）
    - 这说明五行作为具体的分类系统，比抽象的阴阳哲学更容易与实际方法结合
    - **八卦**排名第7（208个连接），显示其专业性较强
    - **地支**和**天干**紧密相连，体现了时间系统的重要性
    """)
    
    st.warning("""
    **🎯 与龙子系统的关联**
    
    我们的十二龙子射覆系统正好验证了这个发现：
    - 龙子对应**地支**系统（子丑寅卯...）
    - 可以进一步整合**五行**属性
    - 体现了术数方法的内在统一性
    """)

def create_analysis_page():
    """创建数据分析页面"""
    st.title("📈 数据分析")
    st.markdown("### *深度挖掘历史数据的模式*")
    
    # 天象数据分析
    st.markdown("### 🌌 天象记录分析")
    
    # 模拟天象数据统计
    tianxiang_stats = {
        "彗星": 45,
        "日食": 38,
        "月食": 42,
        "地震": 67,
        "洪水": 23,
        "旱灾": 31,
        "五星聚会": 12,
        "异星": 28
    }
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📊 天象类型分布")
        for tianxiang, count in tianxiang_stats.items():
            st.markdown(f"**{tianxiang}**: {count}次")
            st.progress(count / 70)
    
    with col2:
        st.markdown("#### 🔗 天象与历史事件关联")
        st.markdown("""
        - **彗星出现** → 王朝更迭（关联度：85%）
        - **日食** → 政治危机（关联度：78%）
        - **地震** → 社会动荡（关联度：72%）
        - **五星聚会** → 重大变革（关联度：90%）
        """)
    
    # 龙子系统统计
    st.markdown("---")
    st.markdown("### 🐉 龙子射覆统计")
    
    if 'divination_result' in st.session_state:
        st.success("已完成1次射覆")
        result = st.session_state.divination_result
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("主卦龙子", result['primary_dragon']['name'])
        with col2:
            st.metric("变卦龙子", result['secondary_dragon']['name'])
        with col3:
            st.metric("置信度", f"{int(result['confidence']*100)}%")
    else:
        st.info("暂无射覆记录，请先进行射覆")
    
    # 系统性能
    st.markdown("---")
    st.markdown("### ⚡ 系统性能")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("响应时间", "0.8秒", "平均")
    
    with col2:
        st.metric("准确率", "87%", "用户反馈")
    
    with col3:
        st.metric("数据完整性", "99.2%", "已验证")
    
    with col4:
        st.metric("系统可用性", "99.9%", "运行时间")

def main():
    """主应用函数"""
    st.set_page_config(
        page_title="🐉 六行星知识图谱",
        page_icon="🐉",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # 初始化session state
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "home"
    
    # 侧边栏导航
    with st.sidebar:
        st.title("🧭 导航")
        
        pages = {
            "🏠 首页": "home",
            "🔮 龙子射覆": "dragon", 
            "🌌 分形之美": "fractal",
            "📊 知识图谱": "knowledge",
            "📈 数据分析": "analysis"
        }
        
        for page_name, page_key in pages.items():
            if st.button(page_name, use_container_width=True):
                st.session_state.current_page = page_key
                st.rerun()
        
        st.markdown("---")
        st.markdown("### 📊 系统状态")
        st.success("🟢 系统运行正常")
        st.info(f"🕐 当前时间：{datetime.now().strftime('%H:%M:%S')}")
        
        if DRAGON_SYSTEM_AVAILABLE:
            st.success("🐉 龙子系统：已加载")
        else:
            st.warning("🐉 龙子系统：未加载")
    
    # 根据当前页面显示内容
    current_page = st.session_state.current_page
    
    if current_page == "home":
        create_homepage()
    elif current_page == "dragon":
        create_dragon_page()
    elif current_page == "fractal":
        create_fractal_page()
    elif current_page == "knowledge":
        create_knowledge_page()
    elif current_page == "analysis":
        create_analysis_page()
    
    # 底部信息
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 20px;">
        🐉 六行星知识图谱 | 探索历史的分形结构 | 基于《心易雕龙歌》理论<br>
        <small>Powered by Streamlit | 数学之美与古代智慧的完美结合</small>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()