#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十二龙子心易射覆系统
基于您的《心易雕龙歌》理论，结合梅花心易射覆方法
"""

import json
import time
import random
from datetime import datetime
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class DragonSon(Enum):
    """十二龙子枚举"""
    QIUNIU = (1, "囚牛", "牛", "礼乐戎祀", "子")      # 1
    YAIZI = (2, "睚眦", "豺", "虽远必诛", "丑")       # 2
    SUANNI = (3, "狻猊", "狮", "讲经说法", "寅")      # 3
    PULAO = (4, "蒲牢", "鲲", "声如洪钟", "卯")       # 4
    CHAOFENG = (5, "嘲风", "凤", "千里听风", "辰")    # 5
    BIAN = (6, "狴犴", "虎", "天下为公", "巳")        # 6
    BIXI = (7, "贔屓", "龟", "文以载道", "午")        # 7
    FUXI = (8, "负屃", "龙", "东西一通", "未")        # 8
    CHIWEN = (9, "螭吻", "鱼", "吐故纳新", "申")      # 9
    GONGFU = (10, "蚣蝮", "虬", "镇守九宫", "酉")     # 10
    TAOTIE = (11, "饕餮", "熊", "颗粒归仓", "戌")     # 11
    PIXIU = (12, "貔貅", "罴", "乃成富翁", "亥")      # 12
    
    def __init__(self, number, dragon_name, mother, function, month):
        self.number = number
        self.dragon_name = dragon_name
        self.mother = mother
        self.function = function
        self.month = month

@dataclass
class DivinationContext:
    """起卦上下文"""
    timestamp: datetime
    question: str
    questioner_info: Dict[str, Any]
    environmental_factors: Dict[str, Any]

@dataclass
class DragonDivination:
    """龙子卦象"""
    primary_dragon: DragonSon
    secondary_dragon: DragonSon
    context: DivinationContext
    interpretation: str
    confidence: float
    
class DragonDivinationSystem:
    """十二龙子射覆系统"""
    
    def __init__(self):
        self.dragons = list(DragonSon)
        self.load_tianwen_data()
        
    def load_tianwen_data(self):
        """加载天文数据作为起卦参考"""
        try:
            with open('tianwen_wuxing_data.json', 'r', encoding='utf-8') as f:
                self.tianwen_data = json.load(f)
        except FileNotFoundError:
            self.tianwen_data = {"tianxiang_records": []}
    
    def get_dragon_by_time(self, dt: datetime) -> DragonSon:
        """根据时间获取对应龙子（地支对应）"""
        # 地支对应：子丑寅卯辰巳午未申酉戌亥
        hour_to_dragon = {
            23: DragonSon.QIUNIU, 0: DragonSon.QIUNIU, 1: DragonSon.QIUNIU,    # 子时 23-1
            2: DragonSon.YAIZI, 3: DragonSon.YAIZI,                             # 丑时 1-3
            4: DragonSon.SUANNI, 5: DragonSon.SUANNI,                           # 寅时 3-5
            6: DragonSon.PULAO, 7: DragonSon.PULAO,                             # 卯时 5-7
            8: DragonSon.CHAOFENG, 9: DragonSon.CHAOFENG,                       # 辰时 7-9
            10: DragonSon.BIAN, 11: DragonSon.BIAN,                             # 巳时 9-11
            12: DragonSon.BIXI, 13: DragonSon.BIXI,                             # 午时 11-13
            14: DragonSon.FUXI, 15: DragonSon.FUXI,                             # 未时 13-15
            16: DragonSon.CHIWEN, 17: DragonSon.CHIWEN,                         # 申时 15-17
            18: DragonSon.GONGFU, 19: DragonSon.GONGFU,                         # 酉时 17-19
            20: DragonSon.TAOTIE, 21: DragonSon.TAOTIE,                         # 戌时 19-21
            22: DragonSon.PIXIU,                                                # 亥时 21-23
        }
        return hour_to_dragon.get(dt.hour, DragonSon.QIUNIU)
    
    def get_dragon_by_number(self, num: int) -> DragonSon:
        """根据数字获取龙子"""
        index = (num - 1) % 12
        return self.dragons[index]
    
    def calculate_plum_blossom_numbers(self, context: DivinationContext) -> Tuple[int, int]:
        """梅花心易起卦法 - 计算上下卦数"""
        dt = context.timestamp
        
        # 方法1：时间起卦
        year_num = dt.year % 12 if dt.year % 12 != 0 else 12
        month_num = dt.month
        day_num = dt.day
        hour_num = ((dt.hour + 1) // 2) % 12 if ((dt.hour + 1) // 2) % 12 != 0 else 12
        
        # 上卦 = (年 + 月 + 日) % 12
        upper = ((year_num + month_num + day_num) % 12)
        if upper == 0:
            upper = 12
            
        # 下卦 = (年 + 月 + 日 + 时) % 12  
        lower = ((year_num + month_num + day_num + hour_num) % 12)
        if lower == 0:
            lower = 12
        
        return upper, lower
    
    def calculate_question_numbers(self, question: str) -> Tuple[int, int]:
        """根据问题内容计算卦数"""
        # 计算问题字符的总和
        char_sum = sum(ord(char) for char in question)
        
        # 上卦：字符总和 % 12
        upper = (char_sum % 12)
        if upper == 0:
            upper = 12
            
        # 下卦：字符总和 + 问题长度 % 12
        lower = ((char_sum + len(question)) % 12)
        if lower == 0:
            lower = 12
            
        return upper, lower
    
    def get_tianxiang_influence(self, dt: datetime) -> float:
        """获取天象影响因子"""
        # 检查当前时间附近是否有天象记录
        current_month = dt.month
        tianxiang_count = 0
        
        for record in self.tianwen_data.get("tianxiang_records", []):
            # 简化处理：统计包含特定天象关键词的记录
            tianxiang_keywords = ["彗星", "日食", "月食", "地震", "五星"]
            for keyword in tianxiang_keywords:
                if keyword in record.get("record", ""):
                    tianxiang_count += 1
                    break
        
        # 天象影响因子：0.5-1.5之间
        influence = 0.5 + (tianxiang_count % 10) * 0.1
        return min(influence, 1.5)
    
    def divine(self, question: str, questioner_info: Dict[str, Any] = None) -> DragonDivination:
        """执行龙子射覆"""
        now = datetime.now()
        
        # 构建起卦上下文
        context = DivinationContext(
            timestamp=now,
            question=question,
            questioner_info=questioner_info or {},
            environmental_factors={
                "tianxiang_influence": self.get_tianxiang_influence(now),
                "season": self.get_season(now),
                "lunar_phase": self.estimate_lunar_phase(now)
            }
        )
        
        # 多种起卦方法综合
        time_upper, time_lower = self.calculate_plum_blossom_numbers(context)
        question_upper, question_lower = self.calculate_question_numbers(question)
        
        # 综合计算主次龙子
        primary_num = (time_upper + question_upper) % 12
        if primary_num == 0:
            primary_num = 12
            
        secondary_num = (time_lower + question_lower) % 12  
        if secondary_num == 0:
            secondary_num = 12
        
        primary_dragon = self.get_dragon_by_number(primary_num)
        secondary_dragon = self.get_dragon_by_number(secondary_num)
        
        # 生成解释
        interpretation = self.generate_interpretation(primary_dragon, secondary_dragon, context)
        
        # 计算置信度
        confidence = self.calculate_confidence(context)
        
        return DragonDivination(
            primary_dragon=primary_dragon,
            secondary_dragon=secondary_dragon,
            context=context,
            interpretation=interpretation,
            confidence=confidence
        )
    
    def generate_interpretation(self, primary: DragonSon, secondary: DragonSon, context: DivinationContext) -> str:
        """生成卦象解释"""
        
        # 基础解释模板
        base_interpretation = f"""
【主卦】{primary.dragon_name}（{primary.mother}）- {primary.function}
【变卦】{secondary.dragon_name}（{secondary.mother}）- {secondary.function}

【时机分析】
当前处于{primary.month}月之象，{primary.dragon_name}当值。
{self.get_dragon_period_analysis(primary)}

【变化趋势】  
由{primary.dragon_name}向{secondary.dragon_name}转化，
{self.get_transformation_analysis(primary, secondary)}

【决策建议】
{self.get_decision_advice(primary, secondary, context)}

【天象参考】
{self.get_tianxiang_reference(context)}
        """
        
        return base_interpretation.strip()
    
    def get_dragon_period_analysis(self, dragon: DragonSon) -> str:
        """获取龙子时期分析"""
        period_analysis = {
            DragonSon.QIUNIU: "正值创始建制之时，宜以礼乐化人，建立秩序。",
            DragonSon.YAIZI: "当下需要明确法度，对违规者严厉处置。",
            DragonSon.SUANNI: "适合学习思考，寻求智慧指导。",
            DragonSon.PULAO: "时机成熟，可以宣告理念，扩大影响。",
            DragonSon.CHAOFENG: "需要保持警觉，预防潜在危机。",
            DragonSon.BIAN: "面临重大决断，需要公正处理。",
            DragonSon.BIXI: "宜以文化建设为重，积累底蕴。",
            DragonSon.FUXI: "适合对外开拓，寻求新的发展空间。",
            DragonSon.CHIWEN: "需要内部调整，吐故纳新。",
            DragonSon.GONGFU: "宜守成待时，以不变应万变。",
            DragonSon.TAOTIE: "应该积累资源，为未来做准备。",
            DragonSon.PIXIU: "可以享受成果，但要避免过度保守。"
        }
        return period_analysis.get(dragon, "时机特殊，需要具体分析。")
    
    def get_transformation_analysis(self, primary: DragonSon, secondary: DragonSon) -> str:
        """获取变化分析"""
        if primary.number < secondary.number:
            return f"事态正在向前发展，从{primary.function}转向{secondary.function}，属于进步之象。"
        elif primary.number > secondary.number:
            return f"出现回调或反思，从{primary.function}回到{secondary.function}，需要重新审视。"
        else:
            return f"保持{primary.function}的状态，变化不大，宜坚持现有方向。"
    
    def get_decision_advice(self, primary: DragonSon, secondary: DragonSon, context: DivinationContext) -> str:
        """获取决策建议"""
        tianxiang_influence = context.environmental_factors.get("tianxiang_influence", 1.0)
        
        if tianxiang_influence > 1.2:
            advice_prefix = "天象异常，宜谨慎行事。"
        elif tianxiang_influence < 0.8:
            advice_prefix = "天时平和，可以积极行动。"
        else:
            advice_prefix = "天时正常，按计划进行。"
        
        # 根据龙子组合给出具体建议
        combination_advice = self.get_combination_advice(primary, secondary)
        
        return f"{advice_prefix}{combination_advice}"
    
    def get_combination_advice(self, primary: DragonSon, secondary: DragonSon) -> str:
        """根据龙子组合给出建议"""
        # 这里可以根据您的《心易雕龙歌》理论，为不同的龙子组合设计具体的建议
        if primary == DragonSon.QIUNIU and secondary == DragonSon.YAIZI:
            return "先礼后兵，以德服人，必要时严明法度。"
        elif primary == DragonSon.CHAOFENG and secondary == DragonSon.BIAN:
            return "预警已现，需要果断决策，维护公正。"
        elif primary == DragonSon.TAOTIE and secondary == DragonSon.PIXIU:
            return "积累已足，可以享受成果，但勿过度保守。"
        else:
            return f"以{primary.function}为主，兼顾{secondary.function}，平衡发展。"
    
    def get_tianxiang_reference(self, context: DivinationContext) -> str:
        """获取天象参考"""
        influence = context.environmental_factors.get("tianxiang_influence", 1.0)
        season = context.environmental_factors.get("season", "未知")
        
        if influence > 1.2:
            return f"当前{season}，天象频繁，古有'天垂象，见吉凶'之说，宜观天时而动。"
        else:
            return f"当前{season}，天象平稳，正是人事用功之时。"
    
    def get_season(self, dt: datetime) -> str:
        """获取季节"""
        month = dt.month
        if month in [12, 1, 2]:
            return "冬季"
        elif month in [3, 4, 5]:
            return "春季"
        elif month in [6, 7, 8]:
            return "夏季"
        else:
            return "秋季"
    
    def estimate_lunar_phase(self, dt: datetime) -> str:
        """估算月相（简化版）"""
        day = dt.day
        if day <= 7:
            return "新月"
        elif day <= 14:
            return "上弦月"
        elif day <= 21:
            return "满月"
        else:
            return "下弦月"
    
    def calculate_confidence(self, context: DivinationContext) -> float:
        """计算置信度"""
        base_confidence = 0.7
        
        # 天象影响
        tianxiang_influence = context.environmental_factors.get("tianxiang_influence", 1.0)
        if tianxiang_influence > 1.0:
            base_confidence += 0.1
        
        # 问题明确度
        question_clarity = min(len(context.question) / 20.0, 1.0)
        base_confidence += question_clarity * 0.2
        
        return min(base_confidence, 1.0)

def test_dragon_divination():
    """测试龙子射覆系统"""
    system = DragonDivinationSystem()
    
    # 测试问题
    test_questions = [
        "项目是否应该继续推进？",
        "当前的技术路线是否正确？", 
        "团队合作中遇到的问题如何解决？",
        "投资决策的最佳时机是什么时候？"
    ]
    
    print("=== 十二龙子心易射覆系统测试 ===\n")
    
    for i, question in enumerate(test_questions, 1):
        print(f"【测试 {i}】问题：{question}")
        
        divination = system.divine(question, {"name": "测试用户", "role": "开发者"})
        
        print(f"起卦时间：{divination.context.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"置信度：{divination.confidence:.2f}")
        print("解释：")
        print(divination.interpretation)
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    test_dragon_divination()