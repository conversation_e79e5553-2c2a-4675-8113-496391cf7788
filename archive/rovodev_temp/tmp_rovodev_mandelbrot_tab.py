#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mandelbrot分形Tab页面 - 集成到Streamlit应用
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import time

def mandelbrot_set(h, w, max_iter=100, x_range=(-2, 1), y_range=(-1.5, 1.5)):
    """生成Mandelbrot集合"""
    x = np.linspace(x_range[0], x_range[1], w)
    y = np.linspace(y_range[0], y_range[1], h)
    X, Y = np.meshgrid(x, y)
    C = X + 1j * Y
    
    Z = np.zeros_like(C)
    iterations = np.zeros(C.shape, dtype=int)
    
    for i in range(max_iter):
        mask = np.abs(Z) <= 2
        Z[mask] = Z[mask]**2 + C[mask]
        iterations[mask] = i
    
    return iterations

def create_mandelbrot_tab():
    """创建Mandelbrot分形tab页面"""
    
    st.header("🌌 数学之美 - Mandelbrot分形")
    
    # 创建两列布局
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("### 🎛️ 分形参数")
        
        # 预设的精美参数组合
        preset_options = {
            "经典全景": {"center": (-0.5, 0), "zoom": 1, "iter": 100},
            "海马谷": {"center": (-0.75, 0.1), "zoom": 50, "iter": 150},
            "闪电": {"center": (-1.25, 0), "zoom": 100, "iter": 200},
            "螺旋": {"center": (-0.16, 1.04), "zoom": 200, "iter": 150},
            "龙脊": {"center": (-0.7269, 0.1889), "zoom": 300, "iter": 180}
        }
        
        selected_preset = st.selectbox("选择预设", list(preset_options.keys()))
        preset = preset_options[selected_preset]
        
        st.markdown("---")
        
        # 手动调节参数
        st.markdown("### 🔧 手动调节")
        center_x = st.slider("中心X", -2.0, 1.0, preset["center"][0], 0.01)
        center_y = st.slider("中心Y", -1.5, 1.5, preset["center"][1], 0.01)
        zoom = st.slider("缩放", 1, 500, preset["zoom"], 1)
        max_iter = st.slider("迭代次数", 50, 300, preset["iter"], 10)
        
        # 配色方案
        color_schemes = {
            "热力图": "hot",
            "等离子": "plasma", 
            "深海": "viridis",
            "岩浆": "magma",
            "冷色": "cool",
            "自定义": "custom"
        }
        
        color_choice = st.selectbox("配色方案", list(color_schemes.keys()))
        
        # 图像尺寸
        resolution = st.selectbox("分辨率", ["标准 (600x400)", "高清 (900x600)", "超清 (1200x800)"])
        
        if resolution == "标准 (600x400)":
            width, height = 600, 400
        elif resolution == "高清 (900x600)":
            width, height = 900, 600
        else:
            width, height = 1200, 800
        
        # 生成按钮
        generate_btn = st.button("🎨 生成分形", type="primary", use_container_width=True)
        
        # 随机探索
        if st.button("🎲 随机探索", use_container_width=True):
            st.session_state.random_params = {
                "center_x": np.random.uniform(-1.5, 0.5),
                "center_y": np.random.uniform(-1.0, 1.0),
                "zoom": np.random.randint(1, 100),
                "max_iter": np.random.randint(80, 200)
            }
            st.rerun()
    
    with col2:
        st.markdown("### 🖼️ 分形图案")
        
        # 使用随机参数（如果有）
        if hasattr(st.session_state, 'random_params'):
            params = st.session_state.random_params
            center_x = params["center_x"]
            center_y = params["center_y"] 
            zoom = params["zoom"]
            max_iter = params["max_iter"]
            del st.session_state.random_params
        
        # 计算分形范围
        zoom_factor = 3.0 / zoom
        x_range = (center_x - zoom_factor/2, center_x + zoom_factor/2)
        y_range = (center_y - zoom_factor*0.75/2, center_y + zoom_factor*0.75/2)
        
        # 生成分形（带进度条）
        if generate_btn or 'mandelbrot_data' not in st.session_state:
            with st.spinner(f"正在生成 {width}x{height} 分形图案..."):
                progress_bar = st.progress(0)
                
                # 分块计算以显示进度
                mandelbrot_data = np.zeros((height, width))
                chunk_size = height // 10
                
                for i in range(0, height, chunk_size):
                    end_i = min(i + chunk_size, height)
                    chunk_height = end_i - i
                    
                    # 计算当前块
                    chunk = mandelbrot_set(chunk_height, width, max_iter, x_range, 
                                         (y_range[0] + i/height * (y_range[1] - y_range[0]),
                                          y_range[0] + end_i/height * (y_range[1] - y_range[0])))
                    
                    mandelbrot_data[i:end_i] = chunk
                    progress_bar.progress((end_i) / height)
                
                st.session_state.mandelbrot_data = mandelbrot_data
                st.session_state.mandelbrot_params = {
                    "center": (center_x, center_y),
                    "zoom": zoom,
                    "iter": max_iter,
                    "color": color_choice
                }
                progress_bar.empty()
        
        # 显示分形图案
        if 'mandelbrot_data' in st.session_state:
            mandelbrot_data = st.session_state.mandelbrot_data
            
            # 创建图形
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 选择配色
            if color_choice == "自定义":
                # 创建自定义配色（蓝紫渐变）
                colors = ['#000428', '#004e92', '#009ffd', '#00d2ff', '#ffffff']
                n_bins = 256
                cmap = LinearSegmentedColormap.from_list('custom', colors, N=n_bins)
            else:
                cmap = color_schemes[color_choice]
            
            # 绘制分形
            im = ax.imshow(mandelbrot_data, extent=[x_range[0], x_range[1], y_range[0], y_range[1]], 
                          cmap=cmap, origin='lower')
            
            # 设置标题和样式
            params = st.session_state.mandelbrot_params
            title = f"Mandelbrot集合 - 中心({params['center'][0]:.3f}, {params['center'][1]:.3f}) 缩放{params['zoom']}x"
            ax.set_title(title, fontsize=14, color='white', pad=20)
            ax.set_xlabel("实部", color='white')
            ax.set_ylabel("虚部", color='white')
            
            # 设置背景色
            fig.patch.set_facecolor('black')
            ax.set_facecolor('black')
            ax.tick_params(colors='white')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('迭代次数', color='white')
            cbar.ax.tick_params(colors='white')
            
            st.pyplot(fig)
            
            # 显示参数信息
            with st.expander("📊 当前参数信息"):
                col_a, col_b, col_c = st.columns(3)
                with col_a:
                    st.metric("中心坐标", f"({params['center'][0]:.3f}, {params['center'][1]:.3f})")
                    st.metric("缩放倍数", f"{params['zoom']}x")
                with col_b:
                    st.metric("迭代次数", params['iter'])
                    st.metric("配色方案", params['color'])
                with col_c:
                    st.metric("分辨率", f"{width}x{height}")
                    st.metric("计算复杂度", f"{width*height*params['iter']:,}")
        
        # 数学说明
        with st.expander("🧮 数学原理"):
            st.markdown("""
            **Mandelbrot集合**是复平面上的一个分形集合，定义为：
            
            对于复数 c，序列 z₀ = 0, z_{n+1} = z_n² + c 保持有界的所有 c 的集合。
            
            **特点：**
            - 🔄 **自相似性**：任意放大都能看到相似的结构
            - 🌀 **无限复杂**：边界具有无限的细节
            - 🎨 **视觉美感**：数学与艺术的完美结合
            
            **与历史研究的类比：**
            - 历史事件如同复数点，看似简单的规则（人性、政治）
            - 却能产生无限复杂的历史模式
            - 每次"放大"（深入研究）都能发现新的相似结构
            """)

def create_streamlit_app_with_mandelbrot():
    """集成Mandelbrot的完整Streamlit应用"""
    
    st.set_page_config(
        page_title="🐉 六行星知识图谱",
        page_icon="🐉",
        layout="wide"
    )
    
    # 创建tab页面
    tab1, tab2, tab3, tab4 = st.tabs(["🏠 首页", "🔮 龙子射覆", "🌌 数学之美", "📊 数据分析"])
    
    with tab1:
        st.title("🐉 六行星知识图谱")
        st.markdown("""
        ### 探索历史的深层结构
        
        本系统结合古代智慧与现代技术，为您提供：
        - 🔮 **十二龙子心易射覆** - 基于《心易雕龙歌》的决策支持
        - 📚 **史记知识图谱** - 深度挖掘历史文献中的关联
        - 🌌 **数学分形探索** - 发现历史的自相似模式
        - 🎯 **智慧决策系统** - 古代术数的现代应用
        """)
        
        # 快速入口
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("🔮 开始射覆", use_container_width=True):
                st.switch_page("tab2")
        with col2:
            if st.button("🌌 探索分形", use_container_width=True):
                st.switch_page("tab3")
        with col3:
            if st.button("📊 查看数据", use_container_width=True):
                st.switch_page("tab4")
    
    with tab2:
        st.header("🔮 十二龙子心易射覆")
        st.markdown("*射覆功能开发中...*")
        # 这里集成您的龙子射覆系统
    
    with tab3:
        # Mandelbrot分形页面
        create_mandelbrot_tab()
    
    with tab4:
        st.header("📊 数据分析")
        st.markdown("*数据分析功能开发中...*")
        # 这里集成您的数据分析功能

if __name__ == "__main__":
    create_streamlit_app_with_mandelbrot()