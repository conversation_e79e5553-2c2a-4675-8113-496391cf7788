version: '3.8'

services:
  # 兜率宫主服务
  doushuai-api:
    build: .
    container_name: doushuai-api
    ports:
      - "5000:5000"
    environment:
      - DOUSHUAI_ENV=production
      - DOUSHUAI_HOST=0.0.0.0
      - DOUSHUAI_PORT=5000
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=doushuai123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=doushuai
      - POSTGRES_USER=doushuai
      - POSTGRES_PASSWORD=doushuai123
    depends_on:
      - redis
      - neo4j
      - postgres
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - doushuai-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: doushuai-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - doushuai-network

  # Neo4j知识图谱数据库
  neo4j:
    image: neo4j:5.0
    container_name: doushuai-neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/doushuai123
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
    volumes:
      - ./neo4j/data:/data
      - ./neo4j/logs:/logs
      - ./neo4j/import:/var/lib/neo4j/import
      - ./neo4j/plugins:/plugins
    restart: unless-stopped
    networks:
      - doushuai-network

  # PostgreSQL关系数据库
  postgres:
    image: postgres:15
    container_name: doushuai-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=doushuai
      - POSTGRES_USER=doushuai
      - POSTGRES_PASSWORD=doushuai123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/sql:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - doushuai-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: doushuai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deploy/nginx/conf.d:/etc/nginx/conf.d
      - ./deploy/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - doushuai-api
    restart: unless-stopped
    networks:
      - doushuai-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: doushuai-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deploy/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - doushuai-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: doushuai-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=doushuai123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deploy/grafana/provisioning:/etc/grafana/provisioning
      - ./deploy/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - doushuai-network

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:
  nginx_logs:

networks:
  doushuai-network:
    driver: bridge
