{"analysis_theme": "中华术数方法论的家谱与科技树", "total_methods": 1479, "total_relationships": 6, "hierarchy_analysis": {"foundation": {"count": 6, "examples": ["卜", "占", "定", "择", "算"]}, "tools": {"count": 235, "examples": ["卜得乾卦", "卜得乾宫卦", "卜得升卦", "卜得卦寅午戌三合", "卜得坎之节卦"]}, "applications": {"count": 350, "examples": ["卜推时下冲", "占六畜都以此推演", "占卜被判刑后", "占同推", "占国同此义推"]}, "advanced": {"count": 4, "examples": ["占地理", "占地理第三", "占风水", "占风水之吉凶"]}, "unclassified": {"count": 884, "examples": ["卜为从", "卜乃圣上亲点", "卜之", "卜之事为难成费力", "卜事之身定矣"]}}, "key_findings": ["发现1473个基础方法作为术数体系的根源", "识别出10个核心方法，它们衍生出大量子方法", "构建了包含6条传承关系的完整科技树", "证实了术数方法存在明确的层级结构和演化路径"], "tech_tree_structure": {"root_methods": ["卜", "占", "定", "择", "算", "选", "卜得乾卦", "卜得乾宫卦", "卜得升卦", "卜得卦寅午戌三合"], "influential_methods": [{"name": "占断", "derivatives": 1}, {"name": "卜之", "derivatives": 1}, {"name": "卜归期", "derivatives": 1}, {"name": "占之", "derivatives": 1}, {"name": "占也", "derivatives": 1}, {"name": "占身命", "derivatives": 1}], "method_families": []}, "cypher_queries": ["// 查看术数方法的演化链\nMATCH path = (base:ShushuMethod)-[:EVOLVES_TO*1..3]->(derived:ShushuMethod) RETURN path LIMIT 10", "// 查看最有影响力的基础方法\nMATCH (base:ShushuMethod)-[:EVOLVES_TO]->(derived:ShushuMethod) RETURN base.name, count(derived) as derivatives ORDER BY derivatives DESC LIMIT 10", "// 查看方法组合关系\nMATCH (base:ShushuMethod)-[:COMBINES_INTO]->(combined:ShushuMethod) RETURN base.name, combined.name LIMIT 20", "// 查看完整的术数科技树\nMATCH (m:ShushuMethod)-[r:EVOLVES_TO|COMBINES_INTO]-(other:ShushuMethod) RETURN m, r, other LIMIT 50"]}