# 六行星知识图谱项目依赖

# 核心依赖
neo4j>=5.0.0
networkx>=3.0
numpy>=1.24.0
pandas>=2.0.0

# 机器学习
scikit-learn>=1.3.0
scipy>=1.10.0

# Web框架
flask>=2.3.0
flask-cors>=4.0.0

# 数据处理
beautifulsoup4>=4.12.0
requests>=2.31.0
lxml>=4.9.0

# 文本处理
jieba>=0.42.1
pypinyin>=0.49.0

# 可视化
matplotlib>=3.7.0
plotly>=5.15.0

# 开发工具
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 三式占卜系统依赖
streamlit>=1.47.0
pendulum>=3.0.0
pytz>=2022.4
sxtwl>=2.0.6
ephem>=4.2
cn2an>=0.5.17
bidict>=0.23.1
opencc-python-reimplemented>=0.1.7
altair>=4.0
drawsvg>=2.3.0
eacal>=0.0.3

# 占卜系统核心包（可选）
# kinliuren>=*******
# kinqimen>=*******
# kintaiyi

# FastAPI扩展
uvicorn[standard]>=0.24.0
pydantic>=2.11.0
