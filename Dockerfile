# 太公心易兜率宫 - 生产环境镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DOUSHUAI_ENV=production

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY shushu/ ./shushu/
COPY data/ ./data/
COPY templates/ ./templates/
COPY main.py .
COPY .env.example .env

# 创建必要的目录
RUN mkdir -p /app/logs /app/data /app/temp

# 设置权限
RUN chmod +x main.py

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/v1/health || exit 1

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "main.py", "--mode", "web", "--host", "0.0.0.0", "--port", "5000"]
