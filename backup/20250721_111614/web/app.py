#!/usr/bin/env python3
"""
六行星知识图谱Web应用
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入占卜API蓝图
from .divination_api import divination_bp

def create_app():
    """创建Flask应用"""
    app = Flask(__name__, 
                template_folder=str(project_root / "templates"),
                static_folder=str(project_root / "static"))
    
    # 启用CORS
    CORS(app)

    # 注册蓝图
    app.register_blueprint(divination_bp)

    # 配置
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key')
    app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')
    
    @app.route('/dragon-divination')
    def dragon_divination():
        """十二龙子占卜页面"""
        try:
            return render_template('dragon_divination.html')
        except Exception as e:
            return f"模板加载错误: {e}"

    @app.route('/sanshi-divination')
    def sanshi_divination():
        """三式占卜系统页面"""
        try:
            return render_template('sanshi_divination.html')
        except Exception as e:
            return f"模板加载错误: {e}"
    
    @app.route('/api/v1/status')
    def api_status():
        """API状态检查"""
        return jsonify({
            "status": "ok",
            "version": "0.1.0",
            "message": "六行星知识图谱API运行正常"
        })
    
    @app.route('/api/v1/entities')
    def api_entities():
        """获取实体列表"""
        # 这里应该连接到Neo4j数据库获取实际数据
        return jsonify({
            "entities": [
                {"id": "person_001", "name": "司马迁", "type": "person"},
                {"id": "event_001", "name": "涿鹿之战", "type": "event"},
                {"id": "place_001", "name": "长安", "type": "place"}
            ],
            "total": 3
        })
    
    @app.route('/api/v1/analyze', methods=['POST'])
    def api_analyze():
        """历史分析API"""
        data = request.get_json()
        query = data.get('query', '')
        
        # 这里应该调用分析器进行实际分析
        return jsonify({
            "query": query,
            "result": "分析功能正在开发中...",
            "confidence": 0.8
        })
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({"error": "页面未找到"}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({"error": "服务器内部错误"}), 500
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
