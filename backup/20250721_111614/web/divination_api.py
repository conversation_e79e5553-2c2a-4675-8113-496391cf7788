#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六行星知识图谱 - 占卜API接口
整合十二龙子和三式占卜系统的统一API
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
import traceback
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.divination.dragon_system import DragonDivinationSystem
from src.divination.sanshi_integration import SanshiDivinationSystem

# 创建蓝图
divination_bp = Blueprint('divination', __name__, url_prefix='/api/v1/divination')

# 初始化占卜系统
dragon_system = DragonDivinationSystem()
sanshi_system = SanshiDivinationSystem()

@divination_bp.route('/status', methods=['GET'])
def get_divination_status():
    """获取占卜系统状态"""
    try:
        return jsonify({
            "status": "success",
            "dragon_system": {
                "available": True,
                "description": "十二龙子心易射覆系统"
            },
            "sanshi_system": sanshi_system.get_system_status(),
            "integration": "六行星知识图谱深度集成"
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@divination_bp.route('/dragon', methods=['POST'])
def dragon_divination():
    """十二龙子占卜"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        questioner_info = data.get('questioner_info', {})
        
        if not question:
            return jsonify({
                "status": "error",
                "message": "问题不能为空"
            }), 400
        
        # 执行龙子占卜
        result = dragon_system.divine(question, questioner_info)
        
        return jsonify({
            "status": "success",
            "system": "十二龙子心易射覆",
            "question": question,
            "result": {
                "primary_dragon": {
                    "name": result.primary_dragon.dragon_name,
                    "number": result.primary_dragon.number,
                    "mother": result.primary_dragon.mother,
                    "function": result.primary_dragon.function,
                    "month": result.primary_dragon.month
                },
                "secondary_dragon": {
                    "name": result.secondary_dragon.dragon_name,
                    "number": result.secondary_dragon.number,
                    "mother": result.secondary_dragon.mother,
                    "function": result.secondary_dragon.function,
                    "month": result.secondary_dragon.month
                },
                "interpretation": result.interpretation,
                "confidence": result.confidence,
                "timestamp": result.context.timestamp.isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        }), 500

@divination_bp.route('/liuren', methods=['POST'])
def liuren_divination():
    """六壬占卜"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        datetime_str = data.get('datetime')
        
        if not question:
            return jsonify({
                "status": "error",
                "message": "问题不能为空"
            }), 400
        
        # 解析时间
        datetime_obj = None
        if datetime_str:
            try:
                datetime_obj = datetime.fromisoformat(datetime_str)
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "时间格式错误，请使用ISO格式"
                }), 400
        
        # 执行六壬占卜
        result = sanshi_system.divine_liuren(question, datetime_obj)
        
        return jsonify({
            "status": "success",
            **result
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        }), 500

@divination_bp.route('/qimen', methods=['POST'])
def qimen_divination():
    """奇门遁甲占卜"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        datetime_str = data.get('datetime')
        
        if not question:
            return jsonify({
                "status": "error",
                "message": "问题不能为空"
            }), 400
        
        # 解析时间
        datetime_obj = None
        if datetime_str:
            try:
                datetime_obj = datetime.fromisoformat(datetime_str)
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "时间格式错误，请使用ISO格式"
                }), 400
        
        # 执行奇门占卜
        result = sanshi_system.divine_qimen(question, datetime_obj)
        
        return jsonify({
            "status": "success",
            **result
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        }), 500

@divination_bp.route('/taiyi', methods=['POST'])
def taiyi_divination():
    """太乙神数占卜"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        datetime_str = data.get('datetime')
        
        if not question:
            return jsonify({
                "status": "error",
                "message": "问题不能为空"
            }), 400
        
        # 解析时间
        datetime_obj = None
        if datetime_str:
            try:
                datetime_obj = datetime.fromisoformat(datetime_str)
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "时间格式错误，请使用ISO格式"
                }), 400
        
        # 执行太乙占卜
        result = sanshi_system.divine_taiyi(question, datetime_obj)
        
        return jsonify({
            "status": "success",
            **result
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        }), 500

@divination_bp.route('/comprehensive', methods=['POST'])
def comprehensive_divination():
    """综合占卜（三式+龙子）"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        questioner_info = data.get('questioner_info', {})
        datetime_str = data.get('datetime')
        
        if not question:
            return jsonify({
                "status": "error",
                "message": "问题不能为空"
            }), 400
        
        # 解析时间
        datetime_obj = None
        if datetime_str:
            try:
                datetime_obj = datetime.fromisoformat(datetime_str)
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "时间格式错误，请使用ISO格式"
                }), 400
        
        # 执行综合占卜
        sanshi_result = sanshi_system.divine_comprehensive(question, datetime_obj)
        dragon_result = dragon_system.divine(question, questioner_info)
        
        return jsonify({
            "status": "success",
            "question": question,
            "comprehensive_analysis": {
                "sanshi_systems": sanshi_result,
                "dragon_system": {
                    "primary_dragon": dragon_result.primary_dragon.dragon_name,
                    "secondary_dragon": dragon_result.secondary_dragon.dragon_name,
                    "interpretation": dragon_result.interpretation,
                    "confidence": dragon_result.confidence
                },
                "six_stars_integration": "正在进行六行星知识图谱深度分析...",
                "final_conclusion": "综合四种占卜系统的结果，需要结合具体情况进行深入分析。"
            }
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        }), 500

@divination_bp.route('/systems', methods=['GET'])
def get_available_systems():
    """获取可用的占卜系统列表"""
    try:
        return jsonify({
            "status": "success",
            "available_systems": {
                "dragon": {
                    "name": "十二龙子心易射覆",
                    "available": True,
                    "description": "基于十二龙子的心易射覆系统"
                },
                "liuren": {
                    "name": "大六壬",
                    "available": sanshi_system.available_systems.get('liuren', False),
                    "description": "中国古代三式之一，以天地盘推演吉凶"
                },
                "qimen": {
                    "name": "奇门遁甲",
                    "available": sanshi_system.available_systems.get('qimen', False),
                    "description": "中国古代三式之一，以九宫八卦推演时空"
                },
                "taiyi": {
                    "name": "太乙神数",
                    "available": sanshi_system.available_systems.get('taiyi', False),
                    "description": "中国古代三式之一，以太乙神数推演国运"
                }
            },
            "integration_level": "深度集成到六行星知识图谱系统"
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500
