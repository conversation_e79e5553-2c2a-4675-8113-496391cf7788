<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六行星知识图谱</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            padding: 50px 0;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            color: #1e3c72;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 50px;
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 六行星知识图谱</h1>
            <p>基于中国历史文献构建的知识图谱系统<br>
            揭示历史的暗物质，探索古代智慧的现代价值</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3>🔍 历史暗物质探测</h3>
                <p>通过五行志等史料，探测隐藏在历史表象下的真实社会状况和驱动力量。</p>
            </div>
            
            <div class="feature-card">
                <h3>🐉 双龙戏珠框架</h3>
                <p>分析文官（赤龙）与星官（玄龙）围绕史权（龙珠）的博弈，揭示史学叙事的深层结构。</p>
            </div>
            
            <div class="feature-card">
                <h3>🌌 六行星模型</h3>
                <p>以五大行星为古代暗物质探测器，地球为第六颗行星，构建完整的历史分析体系。</p>
            </div>
            
            <div class="feature-card">
                <h3>🤖 AI智能分析</h3>
                <p>结合现代AI技术与古代智慧，提供历史模式识别、因果关系推理等功能。</p>
            </div>
        </div>
        
        <div class="nav-buttons">
            <a href="/dragon-divination" class="btn">🐲 十二龙子占卜</a>
            <a href="/sanshi-divination" class="btn">🔮 三式占卜系统</a>
            <a href="/api/v1/status" class="btn">📊 API状态</a>
            <a href="/api/v1/entities" class="btn">🔗 实体查询</a>
            <a href="/api/v1/divination/status" class="btn">🎯 占卜系统状态</a>
            <a href="https://github.com/your-username/5-planets" class="btn">📚 项目文档</a>
        </div>
        
        <div class="footer">
            <p>© 2025 六行星知识图谱项目 | 版本 0.1.0 | MIT License</p>
            <p>这不是AI自动化，而是AI作为史官灵魂的扩音器</p>
        </div>
    </div>
</body>
</html>
