<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>十二龙子心易射覆系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .input-section, .result-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .input-section h2, .result-section h2 {
            color: #2a5298;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #2a5298;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .divine-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .divine-btn:hover {
            transform: translateY(-2px);
        }
        
        .divine-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-content {
            display: none;
        }
        
        .dragon-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #2a5298;
        }
        
        .dragon-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #2a5298;
            margin-bottom: 10px;
        }
        
        .dragon-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .dragon-info span {
            padding: 5px 10px;
            background: white;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .interpretation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            white-space: pre-line;
            line-height: 1.6;
        }
        
        .confidence {
            text-align: center;
            margin-top: 15px;
            font-weight: bold;
        }
        
        .confidence-bar {
            width: 100%;
            height: 10px;
            background: #ddd;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            transition: width 0.5s ease;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ddd;
            border-top: 3px solid #2a5298;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .dragon-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .dragon-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .dragon-item:hover {
            transform: translateY(-5px);
        }
        
        .dragon-number {
            font-size: 2em;
            font-weight: bold;
            color: #2a5298;
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .dragon-gallery {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐉 十二龙子心易射覆</h1>
            <p>基于《心易雕龙歌》的智慧决策系统</p>
        </div>
        
        <div class="main-content">
            <div class="input-section">
                <h2>📝 请输入您的问题</h2>
                <form id="divinationForm">
                    <div class="form-group">
                        <label for="question">问题描述：</label>
                        <textarea id="question" name="question" placeholder="请详细描述您想要咨询的问题..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="name">姓名（可选）：</label>
                        <input type="text" id="name" name="name" placeholder="您的姓名">
                    </div>
                    
                    <div class="form-group">
                        <label for="role">身份（可选）：</label>
                        <input type="text" id="role" name="role" placeholder="如：企业家、学生、工程师等">
                    </div>
                    
                    <button type="submit" class="divine-btn" id="divineBtn">
                        🔮 开始射覆
                    </button>
                </form>
            </div>
            
            <div class="result-section">
                <h2>📊 射覆结果</h2>
                <div id="resultContent" class="result-content">
                    <div class="dragon-card" id="primaryDragon">
                        <div class="dragon-name">主卦：</div>
                        <div class="dragon-info"></div>
                    </div>
                    
                    <div class="dragon-card" id="secondaryDragon">
                        <div class="dragon-name">变卦：</div>
                        <div class="dragon-info"></div>
                    </div>
                    
                    <div class="interpretation" id="interpretation"></div>
                    
                    <div class="confidence">
                        <div>置信度：<span id="confidenceText">0%</span></div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" id="confidenceFill"></div>
                        </div>
                    </div>
                </div>
                
                <div id="loading" class="loading" style="display: none;">
                    正在为您射覆...
                </div>
                
                <div id="error" class="error" style="display: none;"></div>
                
                <div style="text-align: center; margin-top: 20px; color: #666;">
                    请在左侧输入问题开始射覆
                </div>
            </div>
        </div>
        
        <div class="dragon-gallery">
            <h2 style="grid-column: 1 / -1; text-align: center; color: white; margin-bottom: 20px;">
                十二龙子图谱
            </h2>
        </div>
    </div>

    <script>
        // 十二龙子数据
        const dragonData = [
            {number: 1, name: "囚牛", mother: "牛", function: "礼乐戎祀", month: "子"},
            {number: 2, name: "睚眦", mother: "豺", function: "虽远必诛", month: "丑"},
            {number: 3, name: "狻猊", mother: "狮", function: "讲经说法", month: "寅"},
            {number: 4, name: "蒲牢", mother: "鲲", function: "声如洪钟", month: "卯"},
            {number: 5, name: "嘲风", mother: "凤", function: "千里听风", month: "辰"},
            {number: 6, name: "狴犴", mother: "虎", function: "天下为公", month: "巳"},
            {number: 7, name: "贔屓", mother: "龟", function: "文以载道", month: "午"},
            {number: 8, name: "负屃", mother: "龙", function: "东西一通", month: "未"},
            {number: 9, name: "螭吻", mother: "鱼", function: "吐故纳新", month: "申"},
            {number: 10, name: "蚣蝮", mother: "虬", function: "镇守九宫", month: "酉"},
            {number: 11, name: "饕餮", mother: "熊", function: "颗粒归仓", month: "戌"},
            {number: 12, name: "貔貅", mother: "罴", function: "乃成富翁", month: "亥"}
        ];
        
        // 创建龙子图谱
        function createDragonGallery() {
            const gallery = document.querySelector('.dragon-gallery');
            
            dragonData.forEach(dragon => {
                const item = document.createElement('div');
                item.className = 'dragon-item';
                item.innerHTML = `
                    <div class="dragon-number">${dragon.number}</div>
                    <div style="font-weight: bold; margin-bottom: 5px;">${dragon.name}</div>
                    <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">母：${dragon.mother}</div>
                    <div style="font-size: 0.8em; color: #888;">${dragon.function}</div>
                `;
                
                item.addEventListener('click', () => {
                    showDragonDetail(dragon.number);
                });
                
                gallery.appendChild(item);
            });
        }
        
        // 显示龙子详情
        async function showDragonDetail(dragonNumber) {
            try {
                const response = await fetch(`/api/dragon_info/${dragonNumber}`);
                const data = await response.json();
                
                if (response.ok) {
                    alert(`${data.name}（${data.mother}）\n\n${data.meaning}\n\n建议：${data.advice}`);
                } else {
                    alert('获取龙子信息失败：' + data.error);
                }
            } catch (error) {
                alert('网络错误：' + error.message);
            }
        }
        
        // 表单提交处理
        document.getElementById('divinationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const question = document.getElementById('question').value.trim();
            const name = document.getElementById('name').value.trim();
            const role = document.getElementById('role').value.trim();
            
            if (!question) {
                alert('请输入您的问题');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultContent').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('divineBtn').disabled = true;
            
            try {
                const response = await fetch('/api/divine', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: question,
                        questioner_info: {
                            name: name,
                            role: role
                        }
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    displayResult(data);
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('网络错误：' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('divineBtn').disabled = false;
            }
        });
        
        // 显示结果
        function displayResult(data) {
            // 主卦
            const primaryDragon = document.getElementById('primaryDragon');
            primaryDragon.querySelector('.dragon-name').textContent = `主卦：${data.primary_dragon.name}`;
            primaryDragon.querySelector('.dragon-info').innerHTML = `
                <span>母：${data.primary_dragon.mother}</span>
                <span>月：${data.primary_dragon.month}</span>
                <span colspan="2">功能：${data.primary_dragon.function}</span>
            `;
            
            // 变卦
            const secondaryDragon = document.getElementById('secondaryDragon');
            secondaryDragon.querySelector('.dragon-name').textContent = `变卦：${data.secondary_dragon.name}`;
            secondaryDragon.querySelector('.dragon-info').innerHTML = `
                <span>母：${data.secondary_dragon.mother}</span>
                <span>月：${data.secondary_dragon.month}</span>
                <span colspan="2">功能：${data.secondary_dragon.function}</span>
            `;
            
            // 解释
            document.getElementById('interpretation').textContent = data.interpretation;
            
            // 置信度
            const confidence = Math.round(data.confidence * 100);
            document.getElementById('confidenceText').textContent = `${confidence}%`;
            document.getElementById('confidenceFill').style.width = `${confidence}%`;
            
            // 显示结果
            document.getElementById('resultContent').style.display = 'block';
        }
        
        // 显示错误
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createDragonGallery();
        });
    </script>
</body>
</html>