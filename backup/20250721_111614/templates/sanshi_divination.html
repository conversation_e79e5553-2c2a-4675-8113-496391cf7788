<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三式占卜系统 - 六行星知识图谱</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .divination-systems {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .system-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .system-card:hover {
            transform: translateY(-5px);
        }
        
        .system-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .system-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .system-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-available {
            background: #4CAF50;
            color: white;
        }
        
        .status-unavailable {
            background: #f44336;
            color: white;
        }
        
        .divination-form {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .result-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }
        
        .result-container.show {
            display: block;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 50px;
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 三式占卜系统</h1>
            <p>六壬 · 奇门 · 太乙 · 龙子 | 六行星知识图谱深度集成</p>
        </div>
        
        <div class="divination-systems" id="systemsGrid">
            <!-- 系统状态将通过JavaScript动态加载 -->
        </div>
        
        <div class="divination-form">
            <h2>🎯 占卜问询</h2>
            <form id="divinationForm">
                <div class="form-group">
                    <label for="question">问题描述</label>
                    <textarea id="question" name="question" placeholder="请详细描述您想要占卜的问题..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="system">占卜系统</label>
                    <select id="system" name="system" required>
                        <option value="">请选择占卜系统</option>
                        <option value="dragon">十二龙子心易射覆</option>
                        <option value="liuren">大六壬</option>
                        <option value="qimen">奇门遁甲</option>
                        <option value="taiyi">太乙神数</option>
                        <option value="comprehensive">综合占卜（推荐）</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="datetime">占卜时间（可选）</label>
                    <input type="datetime-local" id="datetime" name="datetime">
                    <small>留空则使用当前时间</small>
                </div>
                
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">🔮 开始占卜</button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">🔄 重置</button>
                    <a href="/" class="btn btn-secondary">🏠 返回首页</a>
                </div>
            </form>
        </div>
        
        <div class="result-container" id="resultContainer">
            <h2>📋 占卜结果</h2>
            <div id="resultContent"></div>
        </div>
        
        <div class="footer">
            <p>© 2025 六行星知识图谱项目 | 传统智慧与现代技术的完美融合</p>
            <p>这不是AI自动化，而是AI作为史官灵魂的扩音器</p>
        </div>
    </div>

    <script>
        // 页面加载时获取系统状态
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStatus();
        });

        // 加载系统状态
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/v1/divination/systems');
                const data = await response.json();
                
                if (data.status === 'success') {
                    displaySystems(data.available_systems);
                }
            } catch (error) {
                console.error('加载系统状态失败:', error);
            }
        }

        // 显示系统状态
        function displaySystems(systems) {
            const grid = document.getElementById('systemsGrid');
            grid.innerHTML = '';
            
            for (const [key, system] of Object.entries(systems)) {
                const card = document.createElement('div');
                card.className = 'system-card';
                
                const statusClass = system.available ? 'status-available' : 'status-unavailable';
                const statusText = system.available ? '可用' : '不可用';
                
                card.innerHTML = `
                    <h3>${system.name}</h3>
                    <p>${system.description}</p>
                    <span class="system-status ${statusClass}">${statusText}</span>
                `;
                
                grid.appendChild(card);
            }
        }

        // 处理表单提交
        document.getElementById('divinationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const question = formData.get('question');
            const system = formData.get('system');
            const datetime = formData.get('datetime');
            
            if (!question || !system) {
                alert('请填写问题和选择占卜系统');
                return;
            }
            
            // 显示加载状态
            showLoading();
            
            try {
                const endpoint = system === 'comprehensive' ? 
                    '/api/v1/divination/comprehensive' : 
                    `/api/v1/divination/${system}`;
                
                const requestData = {
                    question: question,
                    datetime: datetime || null
                };
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                displayResult(result);
                
            } catch (error) {
                console.error('占卜请求失败:', error);
                displayError('占卜请求失败，请稍后重试');
            }
        });

        // 显示加载状态
        function showLoading() {
            const container = document.getElementById('resultContainer');
            const content = document.getElementById('resultContent');
            
            content.innerHTML = '<div class="loading">正在占卜中，请稍候...</div>';
            container.classList.add('show');
            container.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示结果
        function displayResult(result) {
            const content = document.getElementById('resultContent');
            
            if (result.status === 'success') {
                content.innerHTML = `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h3>问题：${result.question || '未知'}</h3>
                        <p><strong>占卜时间：</strong>${result.datetime || new Date().toISOString()}</p>
                    </div>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                        <h3>占卜结果：</h3>
                        <pre style="white-space: pre-wrap; font-family: inherit;">${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } else {
                displayError(result.message || '占卜失败');
            }
        }

        // 显示错误
        function displayError(message) {
            const content = document.getElementById('resultContent');
            content.innerHTML = `
                <div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px;">
                    <h3>❌ 占卜失败</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        // 清空表单
        function clearForm() {
            document.getElementById('divinationForm').reset();
            document.getElementById('resultContainer').classList.remove('show');
        }
    </script>
</body>
</html>
