#!/usr/bin/env python3
"""
天权-Haystack API服务
提供术数知识图谱的RESTful API接口
"""

import logging
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# 导入核心组件
from pipeline.retrieve import TianquanRetrievalPipeline
from pipeline.generate import TianquanGenerationPipeline
from core.divination.liuren_engine import LiurenEngine
from core.divination.qimen_engine import QimenEngine
from core.divination.taiyi_engine import TaiyiEngine
from utils.config_loader import load_config
from utils.logger import setup_logger

# 设置日志
logger = setup_logger(__name__)

# 全局变量
config = None
retrieval_pipeline = None
generation_pipeline = None
divination_engines = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global config, retrieval_pipeline, generation_pipeline, divination_engines
    
    # 启动时初始化
    logger.info("初始化天权-Haystack API服务...")
    
    try:
        # 加载配置
        config = load_config("config/haystack.yml")
        
        # 初始化检索管道
        retrieval_pipeline = TianquanRetrievalPipeline(config)
        logger.info("检索管道初始化完成")
        
        # 初始化生成管道
        generation_pipeline = TianquanGenerationPipeline(config)
        logger.info("生成管道初始化完成")
        
        # 初始化占卜引擎
        divination_config = config.get('divination_tools', {})
        if divination_config.get('liuren', {}).get('enabled', False):
            divination_engines['liuren'] = LiurenEngine(divination_config['liuren'])
        if divination_config.get('qimen', {}).get('enabled', False):
            divination_engines['qimen'] = QimenEngine(divination_config['qimen'])
        if divination_config.get('taiyi', {}).get('enabled', False):
            divination_engines['taiyi'] = TaiyiEngine(divination_config['taiyi'])
        
        logger.info(f"占卜引擎初始化完成: {list(divination_engines.keys())}")
        
        yield
        
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise
    
    # 关闭时清理
    logger.info("关闭天权-Haystack API服务...")

# 创建FastAPI应用
app = FastAPI(
    title="天权-Haystack API",
    description="术数知识图谱系统API",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== 数据模型 =====

class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询")
    top_k: int = Field(10, ge=1, le=50, description="返回结果数量")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    search_type: str = Field("semantic", description="搜索类型: semantic, graph, hybrid")

class SearchResult(BaseModel):
    """搜索结果模型"""
    id: str
    content: str
    score: float
    metadata: Dict[str, Any]

class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[SearchResult]
    total: int
    query: str
    search_type: str

class GenerationRequest(BaseModel):
    """生成请求模型"""
    question: str = Field(..., description="用户问题")
    context: Optional[str] = Field(None, description="上下文")
    role: str = Field("易学大师", description="角色设定")
    style: str = Field("剧说", description="回答风格")
    max_tokens: int = Field(1000, ge=100, le=4000, description="最大生成长度")

class GenerationResponse(BaseModel):
    """生成响应模型"""
    answer: str
    sources: List[SearchResult]
    role: str
    style: str

class DivinationRequest(BaseModel):
    """占卜请求模型"""
    question: str = Field(..., description="占卜问题")
    method: str = Field(..., description="占卜方法: liuren, qimen, taiyi")
    datetime: Optional[str] = Field(None, description="占卜时间 (ISO格式)")
    location: Optional[Dict[str, float]] = Field(None, description="地理位置 {lat, lon}")

class DivinationResponse(BaseModel):
    """占卜响应模型"""
    method: str
    question: str
    result: Dict[str, Any]
    interpretation: str
    timestamp: str

# ===== 健康检查 =====

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "tianquan-haystack",
        "version": "1.0.0",
        "components": {
            "retrieval_pipeline": retrieval_pipeline is not None,
            "generation_pipeline": generation_pipeline is not None,
            "divination_engines": list(divination_engines.keys())
        }
    }

# ===== 搜索接口 =====

@app.post("/api/v1/search/semantic", response_model=SearchResponse)
async def semantic_search(request: SearchRequest):
    """语义搜索接口"""
    try:
        if not retrieval_pipeline:
            raise HTTPException(status_code=503, detail="检索管道未初始化")
        
        # 执行语义搜索
        results = retrieval_pipeline.semantic_search(
            query=request.query,
            top_k=request.top_k,
            filters=request.filters
        )
        
        # 转换结果格式
        search_results = []
        for result in results:
            search_results.append(SearchResult(
                id=result.id,
                content=result.content,
                score=result.meta.get('score', 0.0),
                metadata=result.meta
            ))
        
        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            search_type="semantic"
        )
        
    except Exception as e:
        logger.error(f"语义搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.post("/api/v1/search/graph", response_model=SearchResponse)
async def graph_search(request: SearchRequest):
    """图谱搜索接口"""
    try:
        if not retrieval_pipeline:
            raise HTTPException(status_code=503, detail="检索管道未初始化")
        
        # 执行图谱搜索
        results = retrieval_pipeline.graph_search(
            query=request.query,
            top_k=request.top_k,
            filters=request.filters
        )
        
        # 转换结果格式
        search_results = []
        for result in results:
            search_results.append(SearchResult(
                id=result.id,
                content=result.content,
                score=result.meta.get('score', 0.0),
                metadata=result.meta
            ))
        
        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            search_type="graph"
        )
        
    except Exception as e:
        logger.error(f"图谱搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.post("/api/v1/search/hybrid", response_model=SearchResponse)
async def hybrid_search(request: SearchRequest):
    """混合搜索接口"""
    try:
        if not retrieval_pipeline:
            raise HTTPException(status_code=503, detail="检索管道未初始化")
        
        # 执行混合搜索
        results = retrieval_pipeline.hybrid_search(
            query=request.query,
            top_k=request.top_k,
            filters=request.filters
        )
        
        # 转换结果格式
        search_results = []
        for result in results:
            search_results.append(SearchResult(
                id=result.id,
                content=result.content,
                score=result.meta.get('score', 0.0),
                metadata=result.meta
            ))
        
        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            search_type="hybrid"
        )
        
    except Exception as e:
        logger.error(f"混合搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# ===== 生成接口 =====

@app.post("/api/v1/generate/explanation", response_model=GenerationResponse)
async def generate_explanation(request: GenerationRequest):
    """术数解释生成接口"""
    try:
        if not generation_pipeline:
            raise HTTPException(status_code=503, detail="生成管道未初始化")
        
        # 执行生成
        result = generation_pipeline.generate_explanation(
            question=request.question,
            context=request.context,
            role=request.role,
            style=request.style,
            max_tokens=request.max_tokens
        )
        
        # 转换源文档格式
        sources = []
        for doc in result.get('sources', []):
            sources.append(SearchResult(
                id=doc.id,
                content=doc.content,
                score=doc.meta.get('score', 0.0),
                metadata=doc.meta
            ))
        
        return GenerationResponse(
            answer=result['answer'],
            sources=sources,
            role=request.role,
            style=request.style
        )
        
    except Exception as e:
        logger.error(f"生成解释失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

# ===== 占卜接口 =====

@app.post("/api/v1/divination/liuren", response_model=DivinationResponse)
async def liuren_divination(request: DivinationRequest):
    """六壬占卜接口"""
    try:
        if 'liuren' not in divination_engines:
            raise HTTPException(status_code=503, detail="六壬引擎未启用")
        
        engine = divination_engines['liuren']
        result = engine.divine(
            question=request.question,
            datetime=request.datetime,
            location=request.location
        )
        
        return DivinationResponse(
            method="liuren",
            question=request.question,
            result=result['calculation'],
            interpretation=result['interpretation'],
            timestamp=result['timestamp']
        )
        
    except Exception as e:
        logger.error(f"六壬占卜失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"占卜失败: {str(e)}")

@app.post("/api/v1/divination/qimen", response_model=DivinationResponse)
async def qimen_divination(request: DivinationRequest):
    """奇门遁甲占卜接口"""
    try:
        if 'qimen' not in divination_engines:
            raise HTTPException(status_code=503, detail="奇门引擎未启用")
        
        engine = divination_engines['qimen']
        result = engine.divine(
            question=request.question,
            datetime=request.datetime,
            location=request.location
        )
        
        return DivinationResponse(
            method="qimen",
            question=request.question,
            result=result['calculation'],
            interpretation=result['interpretation'],
            timestamp=result['timestamp']
        )
        
    except Exception as e:
        logger.error(f"奇门占卜失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"占卜失败: {str(e)}")

@app.post("/api/v1/divination/taiyi", response_model=DivinationResponse)
async def taiyi_divination(request: DivinationRequest):
    """太乙占卜接口"""
    try:
        if 'taiyi' not in divination_engines:
            raise HTTPException(status_code=503, detail="太乙引擎未启用")
        
        engine = divination_engines['taiyi']
        result = engine.divine(
            question=request.question,
            datetime=request.datetime,
            location=request.location
        )
        
        return DivinationResponse(
            method="taiyi",
            question=request.question,
            result=result['calculation'],
            interpretation=result['interpretation'],
            timestamp=result['timestamp']
        )
        
    except Exception as e:
        logger.error(f"太乙占卜失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"占卜失败: {str(e)}")

# ===== 统计接口 =====

@app.get("/api/v1/stats/documents")
async def get_document_stats():
    """获取文档统计信息"""
    try:
        if not retrieval_pipeline:
            raise HTTPException(status_code=503, detail="检索管道未初始化")
        
        stats = retrieval_pipeline.get_document_stats()
        return stats
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

# ===== 错误处理 =====

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"}
    )

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
