#!/usr/bin/env python3
"""
配置加载工具
"""

import os
import yaml
from typing import Dict, Any
from pathlib import Path

def load_config(config_path: str = "config/haystack.yml") -> Dict[str, Any]:
    """加载YAML配置文件"""
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 环境变量替换
    config = _replace_env_vars(config)
    
    return config

def _replace_env_vars(obj):
    """递归替换环境变量"""
    if isinstance(obj, dict):
        return {k: _replace_env_vars(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [_replace_env_vars(item) for item in obj]
    elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
        env_var = obj[2:-1]
        return os.getenv(env_var, obj)
    else:
        return obj
