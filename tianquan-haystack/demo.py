#!/usr/bin/env python3
"""
天权-Haystack 快速演示脚本
无需复杂配置，直接体验核心功能
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import json

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def demo_text_processing():
    """演示文本处理功能"""
    print("🔤 演示：古籍文本处理")
    print("=" * 50)
    
    # 示例古籍文本
    sample_text = """
    太公心易序
    
    太公心易者，乃太公望所传之易学心法也。其法以心为主，以易为用，心易合一，方能洞察天机。
    
    夫易者，变化之道也。天地万物，无不在变化之中。圣人观象于天，察法于地，近取诸身，远取诸物，
    于是始作八卦，以通神明之德，以类万物之情。
    
    太公曰："心者，神之舍也；易者，道之门也。心不静则神不安，神不安则易不明。
    故学易者，必先养心；养心者，必先去欲。"
    """
    
    print("原始文本：")
    print(sample_text.strip())
    print()
    
    # 简单的文本分块
    sentences = [s.strip() for s in sample_text.split('。') if s.strip()]
    
    print("分句结果：")
    for i, sentence in enumerate(sentences, 1):
        if sentence:
            print(f"{i}. {sentence}")
    print()
    
    # 提取关键信息
    keywords = ["太公", "心易", "八卦", "易学", "天机", "神明"]
    found_keywords = [kw for kw in keywords if kw in sample_text]
    
    print("提取的关键词：")
    print(", ".join(found_keywords))
    print()

def demo_knowledge_graph():
    """演示知识图谱概念"""
    print("🕸️  演示：知识图谱结构")
    print("=" * 50)
    
    # 模拟知识图谱数据
    knowledge_graph = {
        "entities": {
            "太公望": {"type": "人物", "dynasty": "周", "role": "军师"},
            "太公心易": {"type": "典籍", "category": "易学", "author": "太公望"},
            "八卦": {"type": "概念", "category": "易学", "components": ["乾", "坤", "震", "巽", "坎", "离", "艮", "兑"]},
            "周朝": {"type": "朝代", "period": "公元前1046-256年"}
        },
        "relationships": [
            {"from": "太公望", "to": "太公心易", "relation": "著作"},
            {"from": "太公望", "to": "周朝", "relation": "生活于"},
            {"from": "太公心易", "to": "八卦", "relation": "论述"},
            {"from": "八卦", "to": "易学", "relation": "属于"}
        ]
    }
    
    print("实体信息：")
    for entity, info in knowledge_graph["entities"].items():
        print(f"• {entity}: {info}")
    print()
    
    print("关系信息：")
    for rel in knowledge_graph["relationships"]:
        print(f"• {rel['from']} --[{rel['relation']}]--> {rel['to']}")
    print()

def demo_semantic_search():
    """演示语义搜索"""
    print("🔍 演示：语义搜索")
    print("=" * 50)
    
    # 模拟文档库
    documents = [
        {"id": "doc1", "content": "太公心易以心为主，以易为用，心易合一", "source": "太公心易序"},
        {"id": "doc2", "content": "八卦者，乾坤震巽坎离艮兑也，圣人观象而作", "source": "周易"},
        {"id": "doc3", "content": "六壬者，以壬为主，配合天地人三才", "source": "六壬大全"},
        {"id": "doc4", "content": "奇门遁甲，以奇门为体，以遁甲为用", "source": "奇门遁甲"},
        {"id": "doc5", "content": "梅花易数，观物取象，以时间起卦", "source": "梅花易数"}
    ]
    
    # 模拟搜索查询
    queries = [
        "什么是心易合一？",
        "八卦的组成",
        "六壬的原理",
        "如何起卦？"
    ]
    
    for query in queries:
        print(f"查询：{query}")
        
        # 简单的关键词匹配（实际应该用向量相似度）
        results = []
        for doc in documents:
            score = 0
            for word in query:
                if word in doc["content"]:
                    score += 1
            if score > 0:
                results.append((doc, score))
        
        results.sort(key=lambda x: x[1], reverse=True)
        
        print("搜索结果：")
        for doc, score in results[:2]:
            print(f"  📄 {doc['source']}: {doc['content']} (相关度: {score})")
        print()

def demo_ai_generation():
    """演示AI生成（模拟）"""
    print("🤖 演示：AI剧说生成（模拟）")
    print("=" * 50)
    
    # 模拟AI生成的回答
    scenarios = [
        {
            "question": "什么是太公心易？",
            "role": "太公望",
            "answer": """
            【太公望抚须而笑】
            
            吾之心易，非寻常占卜之术也。夫易者，变化之道；心者，神明之府。
            
            世人学易，多拘于卦象，执于爻辞，不知易之真谛在于心法。
            吾传心易三要：一曰静心，二曰观象，三曰应变。
            
            静心者，去除杂念，如止水明镜；
            观象者，察天地之变，知阴阳之理；
            应变者，顺时而动，因势利导。
            
            如此则心与易合，易与道通，可洞察天机，趋吉避凶矣！
            """
        },
        {
            "question": "如何理解八卦？",
            "role": "伏羲",
            "answer": """
            【伏羲仰观天象，俯察地理】
            
            吾观天地万象，始画八卦。
            
            乾为天，纯阳之象，刚健不息；
            坤为地，纯阴之德，厚德载物；
            震为雷，动而有威，奋发向上；
            巽为风，入而有序，柔顺谦逊；
            坎为水，险而有信，智慧深沉；
            离为火，明而有礼，光明磊落；
            艮为山，止而有节，稳重踏实；
            兑为泽，悦而有义，和谐喜悦。
            
            八卦相荡，六十四卦生焉。万物之理，尽在其中矣！
            """
        }
    ]
    
    for scenario in scenarios:
        print(f"问题：{scenario['question']}")
        print(f"角色：{scenario['role']}")
        print(f"回答：{scenario['answer'].strip()}")
        print("-" * 40)

def demo_divination():
    """演示占卜功能（模拟）"""
    print("🔮 演示：术数占卜（模拟）")
    print("=" * 50)
    
    import random
    from datetime import datetime
    
    # 模拟六壬占卜
    print("六壬占卜示例：")
    print("问题：今日运势如何？")
    
    # 模拟六壬盘
    tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
    
    current_time = datetime.now()
    year_gan = tiangan[current_time.year % 10]
    month_zhi = dizhi[current_time.month % 12]
    
    print(f"时间：{current_time.strftime('%Y年%m月%d日 %H时')}")
    print(f"年干：{year_gan}")
    print(f"月支：{month_zhi}")
    
    # 模拟起课
    ke_shen = random.choice(["贵人", "螣蛇", "朱雀", "六合", "勾陈", "青龙", "天空", "白虎", "太常", "玄武", "太阴", "天后"])
    print(f"课神：{ke_shen}")
    
    # 模拟解释
    interpretations = {
        "贵人": "贵人相助，事业顺利",
        "青龙": "吉庆之象，财运亨通", 
        "朱雀": "文书信息，口舌是非",
        "白虎": "凶险之象，需防意外"
    }
    
    interpretation = interpretations.get(ke_shen, "平稳之象，无大吉凶")
    print(f"解释：{interpretation}")
    print()

def main():
    """主演示函数"""
    print("🌟 欢迎体验天权-Haystack术数知识图谱系统")
    print("=" * 60)
    print()
    
    demos = [
        ("1", "古籍文本处理", demo_text_processing),
        ("2", "知识图谱结构", demo_knowledge_graph),
        ("3", "语义搜索", demo_semantic_search),
        ("4", "AI剧说生成", demo_ai_generation),
        ("5", "术数占卜", demo_divination)
    ]
    
    while True:
        print("请选择演示功能：")
        for num, name, _ in demos:
            print(f"{num}. {name}")
        print("0. 退出")
        print()
        
        choice = input("请输入选项 (0-5): ").strip()
        print()
        
        if choice == "0":
            print("感谢体验天权-Haystack系统！")
            break
        
        demo_func = None
        for num, name, func in demos:
            if choice == num:
                demo_func = func
                break
        
        if demo_func:
            try:
                demo_func()
            except Exception as e:
                print(f"演示出错: {e}")
        else:
            print("无效选项，请重新选择")
        
        input("\n按回车键继续...")
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    main()
