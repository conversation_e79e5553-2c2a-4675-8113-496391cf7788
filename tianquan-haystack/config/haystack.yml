# 天权-Haystack 系统配置文件

# ===== 系统基础配置 =====
system:
  name: "tianquan-haystack"
  version: "1.0.0"
  description: "术数知识图谱系统"
  debug: true
  log_level: "INFO"

# ===== 数据库配置 =====
databases:
  neo4j:
    uri: "bolt://localhost:7687"
    username: "neo4j"
    password: "sixstars123"
    database: "neo4j"
    max_connection_lifetime: 3600
    max_connection_pool_size: 50
    connection_acquisition_timeout: 60
    
  mongodb:
    uri: "mongodb://localhost:27017"
    database: "tianquan_docs"
    collection: "documents"
    
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    decode_responses: true

# ===== 向量存储配置 =====
vector_stores:
  faiss:
    index_path: "./data/embeddings/faiss_index"
    embedding_dim: 768
    index_type: "IndexFlatIP"
    
  weaviate:
    host: "localhost"
    port: 8080
    grpc_port: 50051
    class_name: "TianquanDocument"
    
# ===== AI模型配置 =====
models:
  openai:
    api_key: "${OPENAI_API_KEY}"
    model_name: "gpt-4-turbo-preview"
    temperature: 0.7
    max_tokens: 2048

  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    model_name: "claude-3-sonnet-20240229"
    temperature: 0.7
    max_tokens: 2048

  gemini:
    api_key: "${GEMINI_API_KEY}"
    model_name: "gemini-1.5-pro"
    temperature: 0.7
    max_tokens: 2048
    safety_settings:
      harassment: "BLOCK_NONE"
      hate_speech: "BLOCK_NONE"
      sexually_explicit: "BLOCK_NONE"
      dangerous_content: "BLOCK_NONE"
    
  embedding:
    model_name: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    device: "cpu"
    batch_size: 32
    
  chinese_embedding:
    model_name: "shibing624/text2vec-base-chinese"
    device: "cpu"
    batch_size: 16

# ===== Pipeline配置 =====
pipelines:
  # 文档摄取管道
  ingest:
    components:
      - name: "file_converter"
        type: "TextFileToDocument"
        params:
          encoding: "utf-8"
          
      - name: "preprocessor"
        type: "DocumentCleaner"
        params:
          clean_empty_lines: true
          clean_whitespace: true
          clean_header_footer: true
          
      - name: "splitter"
        type: "DocumentSplitter"
        params:
          split_by: "sentence"
          split_length: 200
          split_overlap: 50
          
      - name: "embedder"
        type: "SentenceTransformersDocumentEmbedder"
        params:
          model: "shibing624/text2vec-base-chinese"
          
      - name: "writer"
        type: "DocumentWriter"
        params:
          document_store: "neo4j_store"
          
  # 检索管道
  retrieval:
    components:
      - name: "embedder"
        type: "SentenceTransformersTextEmbedder"
        params:
          model: "shibing624/text2vec-base-chinese"
          
      - name: "retriever"
        type: "Neo4jEmbeddingRetriever"
        params:
          document_store: "neo4j_store"
          top_k: 10
          
      - name: "ranker"
        type: "SentenceTransformersRanker"
        params:
          model: "cross-encoder/ms-marco-MiniLM-L-6-v2"
          top_k: 5
          
  # 生成管道
  generation:
    components:
      - name: "prompt_builder"
        type: "PromptBuilder"
        params:
          template: |
            你是一位精通术数的易学大师，请基于以下古籍文献回答问题：
            
            文献内容：
            {% for doc in documents %}
            {{ doc.content }}
            {% endfor %}
            
            问题：{{ question }}
            
            请以剧说的方式，扮演古代易学大师的角色，用深入浅出的语言解释相关概念。
            
      - name: "llm"
        type: "OpenAIGenerator"
        params:
          api_key: "${OPENAI_API_KEY}"
          model: "gpt-4-turbo-preview"
          temperature: 0.7

# ===== 文档处理配置 =====
document_processing:
  # 中文分词配置
  tokenization:
    tool: "jieba"
    user_dict: "./data/dictionaries/术数词典.txt"
    stop_words: "./data/dictionaries/停用词.txt"
    
  # 文档清理配置
  cleaning:
    remove_patterns:
      - "\\[.*?\\]"  # 移除方括号内容
      - "\\(.*?\\)"  # 移除圆括号内容
    replace_patterns:
      "　": " "  # 替换全角空格
      
  # 章节识别配置
  section_detection:
    patterns:
      - "^第[一二三四五六七八九十百千万\\d]+[章节卷篇]"
      - "^[一二三四五六七八九十百千万\\d]+、"
      - "^[\\d]+\\."

# ===== 术数工具配置 =====
divination_tools:
  liuren:
    enabled: true
    calendar_system: "lunar"
    time_zone: "Asia/Shanghai"
    
  qimen:
    enabled: true
    plate_type: "转盘"
    calendar_system: "lunar"
    
  taiyi:
    enabled: true
    calculation_method: "traditional"
    
  yijing:
    enabled: true
    hexagram_system: "64"
    interpretation_style: "classical"

# ===== API配置 =====
api:
  host: "0.0.0.0"
  port: 8000
  reload: true
  workers: 1
  
  cors:
    allow_origins: ["*"]
    allow_methods: ["*"]
    allow_headers: ["*"]
    
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    
# ===== 前端配置 =====
frontend:
  streamlit:
    host: "0.0.0.0"
    port: 8501
    theme: "light"
    
  ui_components:
    sidebar_width: 300
    main_width: 800
    chart_height: 400

# ===== 缓存配置 =====
cache:
  enabled: true
  backend: "redis"
  ttl: 3600  # 1小时
  
  keys:
    embeddings: "embeddings:{hash}"
    search_results: "search:{query_hash}"
    generated_content: "generated:{prompt_hash}"

# ===== 日志配置 =====
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  
  handlers:
    console:
      enabled: true
      level: "INFO"
      
    file:
      enabled: true
      level: "DEBUG"
      path: "./logs/tianquan.log"
      rotation: "1 day"
      retention: "30 days"

# ===== 监控配置 =====
monitoring:
  metrics:
    enabled: true
    port: 9090
    
  health_check:
    enabled: true
    endpoint: "/health"
    
  performance:
    track_response_time: true
    track_memory_usage: true
    track_cpu_usage: true

# ===== 安全配置 =====
security:
  api_key_required: false
  jwt_secret: "${JWT_SECRET}"
  jwt_algorithm: "HS256"
  jwt_expiration: 86400  # 24小时
  
  cors:
    enabled: true
    origins: ["http://localhost:3000", "http://localhost:8501"]

# ===== 数据路径配置 =====
data_paths:
  raw_documents: "./data/raw/"
  processed_documents: "./data/processed/"
  embeddings: "./data/embeddings/"
  models: "./data/models/"
  dictionaries: "./data/dictionaries/"
  logs: "./logs/"
  
# ===== 实验性功能 =====
experimental:
  multi_modal: false
  graph_reasoning: true
  auto_prompt_optimization: false
  distributed_processing: false
