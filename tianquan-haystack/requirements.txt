# 天权-Haystack 术数知识图谱系统依赖

# ===== Haystack 核心框架 =====
haystack-ai>=2.0.0
farm-haystack[inference]>=1.25.0

# ===== 图数据库和向量存储 =====
neo4j>=5.0.0
weaviate-client>=3.25.0
faiss-cpu>=1.7.4
sentence-transformers>=2.2.2

# ===== AI模型和NLP =====
openai>=1.0.0
anthropic>=0.8.0
google-generativeai>=0.3.0
transformers>=4.35.0
torch>=2.0.0
tiktoken>=0.5.0

# ===== 中文处理 =====
jieba>=0.42.1
pypinyin>=0.49.0
opencc-python-reimplemented>=0.1.7
zhconv>=1.4.3

# ===== Web框架和API =====
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
streamlit>=1.47.0
pydantic>=2.11.0
python-multipart>=0.0.6

# ===== 数据处理 =====
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
networkx>=3.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# ===== 文档处理 =====
pypdf>=3.17.0
python-docx>=1.1.0
markdown>=3.5.0
pdfplumber>=0.9.0

# ===== 配置和环境 =====
python-dotenv>=1.0.0
pyyaml>=6.0
click>=8.1.0
rich>=13.0.0

# ===== 数据库连接 =====
pymongo>=4.6.0
redis>=5.0.0
elasticsearch>=8.11.0

# ===== 可视化和图表 =====
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0
graphviz>=0.20.0

# ===== 测试和开发工具 =====
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# ===== 日志和监控 =====
loguru>=0.7.0
prometheus-client>=0.19.0

# ===== 术数计算依赖 =====
pendulum>=3.0.0
pytz>=2022.4
sxtwl>=2.0.6
ephem>=4.2
cn2an>=0.5.17
bidict>=0.23.1
eacal>=0.0.3

# ===== HTTP客户端 =====
httpx>=0.25.0
requests>=2.31.0
aiohttp>=3.9.0

# ===== 缓存和序列化 =====
joblib>=1.3.0
pickle5>=0.0.12
diskcache>=5.6.0

# ===== 安全和认证 =====
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# ===== 异步和并发 =====
asyncio>=3.4.3
aiofiles>=23.2.0
celery>=5.3.0

# ===== 图像处理 (可选) =====
pillow>=10.0.0
opencv-python>=4.8.0

# ===== 音频处理 (可选) =====
# librosa>=0.10.0
# soundfile>=0.12.0

# ===== 部署和容器化 =====
gunicorn>=21.2.0
docker>=6.1.0

# ===== 数据验证 =====
cerberus>=1.3.4
marshmallow>=3.20.0

# ===== 时间处理 =====
arrow>=1.3.0
dateutil>=2.8.2

# ===== 文件操作 =====
pathlib2>=2.3.7
watchdog>=3.0.0

# ===== 网络和爬虫 =====
scrapy>=2.11.0
selenium>=4.15.0

# ===== 数学和科学计算 =====
sympy>=1.12
astropy>=5.3.0

# ===== 编码和解码 =====
chardet>=5.2.0
charset-normalizer>=3.3.0

# ===== 压缩和归档 =====
zipfile36>=0.1.3
tarfile>=0.0.1

# ===== 系统信息 =====
psutil>=5.9.0
platform>=1.0.8

# ===== 国际化 =====
babel>=2.13.0
gettext>=4.0

# ===== 模板引擎 =====
jinja2>=3.1.0
mako>=1.3.0

# ===== 数据格式 =====
toml>=0.10.2
xmltodict>=0.13.0
jsonschema>=4.20.0

# ===== 机器学习扩展 =====
scikit-learn>=1.3.0
xgboost>=2.0.0
lightgbm>=4.1.0

# ===== 深度学习框架 =====
# tensorflow>=2.15.0  # 可选
# pytorch-lightning>=2.1.0  # 可选

# ===== 分布式计算 =====
# dask>=2023.11.0  # 可选
# ray>=2.8.0  # 可选

# ===== 特殊用途 =====
# jupyter>=1.0.0  # 开发环境
# notebook>=7.0.0  # 开发环境
# ipywidgets>=8.1.0  # 交互式组件
