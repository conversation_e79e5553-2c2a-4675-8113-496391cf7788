# 天权-Haystack 环境配置文件示例
# 复制此文件为 .env 并填入实际配置值

# ===== 基础配置 =====
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# ===== AI模型API密钥 =====
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-turbo-preview

# Anthropic Claude API配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Google Gemini API配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-pro

# HuggingFace API配置 (可选)
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# ===== 数据库配置 =====
# Neo4j 图数据库
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=tianquan123
NEO4J_DATABASE=tianquan

# Redis 缓存
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=tianquan123
REDIS_DB=0

# MongoDB 文档数据库 (可选)
MONGODB_URI=************************************************************
MONGODB_DATABASE=tianquan_docs

# Weaviate 向量数据库 (可选)
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_GRPC_PORT=50051

# Elasticsearch (可选)
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

# ===== 应用配置 =====
# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_RELOAD=true

# Streamlit前端配置
STREAMLIT_HOST=0.0.0.0
STREAMLIT_PORT=8501

# ===== 安全配置 =====
# JWT密钥 (生产环境请使用强密钥)
JWT_SECRET=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION=86400

# API密钥 (可选，用于API访问控制)
API_KEY=your_api_key_here

# ===== 文件路径配置 =====
# 数据目录
DATA_ROOT_PATH=./data
RAW_DATA_PATH=./data/raw
PROCESSED_DATA_PATH=./data/processed
EMBEDDINGS_PATH=./data/embeddings
MODELS_PATH=./data/models

# 日志目录
LOG_PATH=./logs

# 古籍文献路径 (指向现有的shushubook目录)
ANCIENT_BOOKS_PATH=../shushubook

# ===== 模型配置 =====
# 中文Embedding模型
CHINESE_EMBEDDING_MODEL=shibing624/text2vec-base-chinese
EMBEDDING_DEVICE=cpu
EMBEDDING_BATCH_SIZE=16

# 多语言Embedding模型
MULTILINGUAL_EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2

# ===== 处理配置 =====
# 文档分块配置
CHUNK_SIZE=200
CHUNK_OVERLAP=50
CHUNK_METHOD=sentence

# 检索配置
DEFAULT_TOP_K=10
MAX_TOP_K=50

# 生成配置
DEFAULT_MAX_TOKENS=1000
MAX_MAX_TOKENS=4000
DEFAULT_TEMPERATURE=0.7

# ===== 占卜工具配置 =====
# 六壬配置
LIUREN_ENABLED=true
LIUREN_CALENDAR_SYSTEM=lunar
LIUREN_TIMEZONE=Asia/Shanghai

# 奇门遁甲配置
QIMEN_ENABLED=true
QIMEN_PLATE_TYPE=转盘
QIMEN_CALENDAR_SYSTEM=lunar

# 太乙配置
TAIYI_ENABLED=true
TAIYI_CALCULATION_METHOD=traditional

# 易经配置
YIJING_ENABLED=true
YIJING_HEXAGRAM_SYSTEM=64
YIJING_INTERPRETATION_STYLE=classical

# ===== 缓存配置 =====
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ===== 监控配置 =====
# Prometheus监控
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# 健康检查
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# ===== 开发工具配置 =====
# Jupyter Notebook
JUPYTER_ENABLED=false
JUPYTER_PORT=8888
JUPYTER_TOKEN=tianquan123

# 调试配置
ENABLE_PROFILING=false
ENABLE_QUERY_LOGGING=true

# ===== 第三方服务配置 =====
# 百度API (可选，用于中文NLP)
BAIDU_API_KEY=your_baidu_api_key_here
BAIDU_SECRET_KEY=your_baidu_secret_key_here

# 腾讯云API (可选)
TENCENT_SECRET_ID=your_tencent_secret_id_here
TENCENT_SECRET_KEY=your_tencent_secret_key_here

# 阿里云API (可选)
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id_here
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret_here

# ===== 邮件配置 (可选，用于通知) =====
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

# ===== 对象存储配置 (可选) =====
# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_BUCKET_NAME=tianquan-data

# 阿里云OSS
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
OSS_BUCKET_NAME=tianquan-data

# ===== 特殊配置 =====
# 代理配置 (如果需要)
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# 时区配置
TIMEZONE=Asia/Shanghai

# 语言配置
LANGUAGE=zh-CN
LOCALE=zh_CN.UTF-8

# ===== 实验性功能 =====
# 多模态支持
ENABLE_MULTIMODAL=false

# 图推理
ENABLE_GRAPH_REASONING=true

# 自动提示词优化
ENABLE_AUTO_PROMPT_OPTIMIZATION=false

# 分布式处理
ENABLE_DISTRIBUTED_PROCESSING=false

# ===== 性能调优 =====
# 并发配置
MAX_WORKERS=4
MAX_CONCURRENT_REQUESTS=100

# 内存配置
MAX_MEMORY_USAGE=8GB
ENABLE_MEMORY_MONITORING=true

# 网络配置
REQUEST_TIMEOUT=30
CONNECTION_TIMEOUT=10
READ_TIMEOUT=30
