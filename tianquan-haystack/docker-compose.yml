# 天权-Haystack 开发环境 Docker Compose 配置

version: '3.8'

services:
  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.15-community
    container_name: tianquan-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/tianquan123
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - tianquan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "tianquan123", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: tianquan-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass tianquan123
    volumes:
      - redis_data:/data
    networks:
      - tianquan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MongoDB 文档数据库 (可选)
  mongodb:
    image: mongo:7
    container_name: tianquan-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=tianquan
      - MONGO_INITDB_ROOT_PASSWORD=tianquan123
      - MONGO_INITDB_DATABASE=tianquan_docs
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - tianquan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Weaviate 向量数据库 (可选)
  weaviate:
    image: semitechnologies/weaviate:1.22.4
    container_name: tianquan-weaviate
    ports:
      - "8080:8080"
      - "50051:50051"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - tianquan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/v1/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Elasticsearch (可选，用于全文搜索)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: tianquan-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - tianquan-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Jupyter Notebook (开发环境)
  jupyter:
    image: jupyter/scipy-notebook:latest
    container_name: tianquan-jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=tianquan123
    volumes:
      - ./:/home/<USER>/work
      - jupyter_data:/home/<USER>
    networks:
      - tianquan-network
    restart: unless-stopped
    depends_on:
      - neo4j
      - redis

  # Grafana 监控面板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: tianquan-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=tianquan123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - tianquan-network
    restart: unless-stopped

  # Prometheus 监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: tianquan-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - tianquan-network
    restart: unless-stopped

# 网络配置
networks:
  tianquan-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  # Neo4j 数据卷
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  
  # Redis 数据卷
  redis_data:
    driver: local
  
  # MongoDB 数据卷
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  
  # Weaviate 数据卷
  weaviate_data:
    driver: local
  
  # Elasticsearch 数据卷
  elasticsearch_data:
    driver: local
  
  # Jupyter 数据卷
  jupyter_data:
    driver: local
  
  # Grafana 数据卷
  grafana_data:
    driver: local
  
  # Prometheus 数据卷
  prometheus_data:
    driver: local
