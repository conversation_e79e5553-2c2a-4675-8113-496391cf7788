#!/usr/bin/env python3
"""
天权-Haystack 快速启动脚本
一键体验核心功能，无需复杂配置
"""

import os
import sys
import json
from pathlib import Path
from typing import List, Dict, Any

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False

    print(f"✅ Python版本: {sys.version}")
    print("✅ 基础环境检查通过")
    return True

def create_sample_data():
    """创建示例数据"""
    print("📚 创建示例数据...")
    
    # 创建数据目录
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建示例古籍
    sample_books = {
        "太公心易.txt": """太公心易序

太公心易者，乃太公望所传之易学心法也。其法以心为主，以易为用，心易合一，方能洞察天机。

夫易者，变化之道也。天地万物，无不在变化之中。圣人观象于天，察法于地，近取诸身，远取诸物，于是始作八卦，以通神明之德，以类万物之情。

太公曰："心者，神之舍也；易者，道之门也。心不静则神不安，神不安则易不明。故学易者，必先养心；养心者，必先去欲。"

又曰："天有三宝日月星，地有三宝水火风，人有三宝精气神。精气神合一，则与天地同德，与日月同明，与四时同序，与鬼神同吉凶。"

其法分为三层：
一曰观象，二曰明理，三曰应用。

观象者，观天地之象，察阴阳之变，知五行之生克，明八卦之消息。
明理者，明易之理，知变之道，察时之宜，识机之微。
应用者，应天时，顺地利，合人和，趋吉避凶。

太公心易之要诀：
心静如水，神清如镜，意专如一，气和如春。
观象不拘于象，明理不滞于理，应用不执于用。
变通无穷，妙用无尽，此太公心易之真谛也。""",

        "梅花易数.txt": """梅花易数

梅花易数者，宋邵康节先生所创，以时间起卦，观物取象之法也。

其法简易，不用蓍草，不用铜钱，随时随地，皆可起卦。见物即可取象，闻声即可断事，神妙无穷。

起卦之法：
以年月日时之数，除以八，取余数为上卦；
以年月日时之数，除以八，取余数为下卦；
以年月日时之数，除以六，取余数为动爻。

八卦取象：
乾为天，为父，为首，为马；
坤为地，为母，为腹，为牛；
震为雷，为长男，为足，为龙；
巽为风，为长女，为股，为鸡；
坎为水，为中男，为耳，为豕；
离为火，为中女，为目，为雉；
艮为山，为少男，为手，为狗；
兑为泽，为少女，为口，为羊。

观物之法：
凡见一物，即以此物起卦。如见梅花，则以梅花之数起卦；见飞鸟，则以飞鸟之数起卦。
数之多少，以时间配合，自然成卦，断事如神。

此法妙在随机应变，不拘一格，真易学之精华也。""",

        "六壬大全.txt": """六壬大全

六壬者，式占之首也。以壬为主，配合天地人三才，推演吉凶祸福。

六壬之法，以月将加时，布四课三传，观其生克制化，以断吉凶。

四课者：
一课：日上神
二课：辰上神  
三课：日阴神
四课：辰阴神

三传者：
初传、中传、末传，乃事之始中终也。

十二神将：
贵人、螣蛇、朱雀、六合、勾陈、青龙、天空、白虎、太常、玄武、太阴、天后。

各神将之象：
贵人主贵，螣蛇主虚，朱雀主文，六合主和，勾陈主勾，青龙主喜，天空主空，白虎主凶，太常主常，玄武主盗，太阴主阴，天后主后。

占法之要：
以日干为主，观其生克制化。生我者为父母，我生者为子孙，克我者为官鬼，我克者为妻财，比和者为兄弟。

六壬之妙，在于变通。熟练四课三传，明了神将生克，则可洞察天机，预知祸福矣。"""
    }
    
    for filename, content in sample_books.items():
        file_path = data_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"✅ 创建了 {len(sample_books)} 个示例古籍文件")
    return list(sample_books.keys())

def simple_text_search(query: str, documents: List[Dict]) -> List[Dict]:
    """简单的文本搜索"""
    results = []
    
    for doc in documents:
        score = 0
        content_lower = doc['content'].lower()
        query_lower = query.lower()
        
        # 简单的关键词匹配
        for char in query_lower:
            if char in content_lower:
                score += content_lower.count(char)
        
        if score > 0:
            results.append({
                'document': doc,
                'score': score
            })
    
    # 按分数排序
    results.sort(key=lambda x: x['score'], reverse=True)
    return results[:5]  # 返回前5个结果

def load_documents() -> List[Dict]:
    """加载文档"""
    documents = []
    data_dir = Path("data/raw")
    
    for file_path in data_dir.glob("*.txt"):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的元数据提取
        title = file_path.stem
        author = "未知"
        category = "术数"
        
        if "太公" in title:
            author = "太公望"
            category = "易学"
        elif "梅花" in title:
            author = "邵雍"
            category = "易学"
        elif "六壬" in title:
            author = "古传"
            category = "六壬"
        
        documents.append({
            'id': file_path.stem,
            'title': title,
            'author': author,
            'category': category,
            'content': content,
            'file_path': str(file_path)
        })
    
    return documents

def interactive_search():
    """交互式搜索"""
    print("🔍 进入交互式搜索模式")
    print("输入 'quit' 退出")
    print("-" * 40)
    
    documents = load_documents()
    print(f"已加载 {len(documents)} 个文档")
    print()
    
    while True:
        query = input("请输入搜索关键词: ").strip()
        
        if query.lower() == 'quit':
            break
        
        if not query:
            continue
        
        print(f"\n搜索: {query}")
        print("-" * 30)
        
        results = simple_text_search(query, documents)
        
        if not results:
            print("没有找到相关结果")
        else:
            for i, result in enumerate(results, 1):
                doc = result['document']
                score = result['score']
                
                print(f"{i}. 《{doc['title']}》 - {doc['author']} (相关度: {score})")
                
                # 显示部分内容
                content_preview = doc['content'][:100].replace('\n', ' ')
                print(f"   内容预览: {content_preview}...")
                print()
        
        print("-" * 40)

def show_statistics():
    """显示统计信息"""
    print("📊 文档统计信息")
    print("-" * 30)
    
    documents = load_documents()
    
    # 基本统计
    total_docs = len(documents)
    total_chars = sum(len(doc['content']) for doc in documents)
    
    print(f"文档总数: {total_docs}")
    print(f"总字符数: {total_chars:,}")
    print(f"平均文档长度: {total_chars // total_docs if total_docs > 0 else 0:,} 字符")
    print()
    
    # 按类别统计
    categories = {}
    for doc in documents:
        category = doc['category']
        categories[category] = categories.get(category, 0) + 1
    
    print("按类别统计:")
    for category, count in categories.items():
        print(f"  {category}: {count} 个文档")
    print()
    
    # 按作者统计
    authors = {}
    for doc in documents:
        author = doc['author']
        authors[author] = authors.get(author, 0) + 1
    
    print("按作者统计:")
    for author, count in authors.items():
        print(f"  {author}: {count} 个文档")
    print()

def main():
    """主函数"""
    print("🌟 天权-Haystack 快速体验")
    print("=" * 40)
    print()
    
    # 检查环境
    if not check_environment():
        return
    
    # 创建示例数据
    sample_files = create_sample_data()
    print()
    
    while True:
        print("请选择功能:")
        print("1. 查看文档统计")
        print("2. 交互式搜索")
        print("3. 运行完整演示")
        print("0. 退出")
        print()
        
        choice = input("请输入选项 (0-3): ").strip()
        print()
        
        if choice == "0":
            print("感谢使用天权-Haystack系统！")
            break
        elif choice == "1":
            show_statistics()
        elif choice == "2":
            interactive_search()
        elif choice == "3":
            # 运行完整演示
            try:
                from demo import main as demo_main
                demo_main()
            except ImportError:
                print("演示模块未找到，请确保 demo.py 文件存在")
        else:
            print("无效选项，请重新选择")
        
        if choice != "0":
            input("按回车键继续...")
            print("\n" + "="*40 + "\n")

if __name__ == "__main__":
    main()
