#!/usr/bin/env python3
"""
天权-Haystack 真实数据Gemini演示
连接到你的六行星知识图谱，使用真实的史书和术数数据
"""

import os
import sys
from pathlib import Path
import google.generativeai as genai
from neo4j import GraphDatabase

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

class RealDataGeminiDemo:
    """连接真实数据的Gemini演示"""
    
    def __init__(self):
        """初始化连接"""
        # 配置Gemini
        self.api_key = "AIzaSyCalfmTapVVTYWVAPm923sz7UtitZbmsaM"
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        
        # 连接Neo4j（你的真实数据库）
        self.driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "sixstars123")
        )
        
        print("🌟 天权-Haystack 真实数据演示")
        print("🔗 连接到六行星知识图谱")
        print("🤖 使用Google Gemini AI")
        print("=" * 50)
    
    def test_neo4j_connection(self):
        """测试Neo4j连接"""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN count(*) as total_nodes")
                total = result.single()["total_nodes"]
                print(f"✅ Neo4j连接成功，总节点数: {total:,}")
                return True
        except Exception as e:
            print(f"❌ Neo4j连接失败: {str(e)}")
            return False
    
    def get_database_overview(self):
        """获取数据库概览"""
        with self.driver.session() as session:
            # 获取所有节点标签
            result = session.run("CALL db.labels()")
            labels = [record['label'] for record in result]

            # 统计每种标签的节点数
            node_stats = {}
            for label in labels:
                result = session.run(f"MATCH (n:{label}) RETURN count(n) as count")
                count = result.single()['count']
                node_stats[label] = count

            # 获取所有关系类型
            result = session.run("CALL db.relationshipTypes()")
            rel_types = [record['relationshipType'] for record in result]

            # 统计每种关系的数量
            rel_stats = {}
            for rel_type in rel_types:
                result = session.run(f"MATCH ()-[r:{rel_type}]->() RETURN count(r) as count")
                count = result.single()['count']
                rel_stats[rel_type] = count

            return node_stats, rel_stats
    
    def search_historical_data(self, search_term: str, limit: int = 5):
        """搜索历史数据"""
        with self.driver.session() as session:
            # 搜索人物
            person_result = session.run("""
                MATCH (p:Person)
                WHERE p.name CONTAINS $search_term OR p.description CONTAINS $search_term
                RETURN p.name as name, p.description as description, 'Person' as type
                LIMIT $limit
            """, search_term=search_term, limit=limit)

            # 搜索事件
            event_result = session.run("""
                MATCH (e:Event)
                WHERE e.name CONTAINS $search_term OR e.description CONTAINS $search_term
                RETURN e.name as name, e.description as description, 'Event' as type
                LIMIT $limit
            """, search_term=search_term, limit=limit)

            # 搜索术数方法
            method_result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE m.name CONTAINS $search_term OR m.description CONTAINS $search_term
                RETURN m.name as name, m.description as description, 'ShushuMethod' as type
                LIMIT $limit
            """, search_term=search_term, limit=limit)
            
            results = []
            for result in [person_result, event_result, method_result]:
                for record in result:
                    results.append({
                        'name': record['name'],
                        'description': record['description'] or '',
                        'type': record['type']
                    })
            
            return results
    
    def get_person_relationships(self, person_name: str):
        """获取人物关系"""
        with self.driver.session() as session:
            result = session.run("""
                MATCH (p:Person {name: $name})-[r]-(related)
                RETURN type(r) as relationship, 
                       labels(related) as related_type,
                       related.name as related_name,
                       related.description as related_desc
                LIMIT 10
            """, name=person_name)
            
            relationships = []
            for record in result:
                relationships.append({
                    'relationship': record['relationship'],
                    'related_type': record['related_type'][0] if record['related_type'] else 'Unknown',
                    'related_name': record['related_name'],
                    'related_desc': record['related_desc'] or ''
                })
            
            return relationships
    
    def ask_gemini_about_data(self, question: str, context_data: list):
        """向Gemini询问关于数据的问题"""
        # 构建上下文
        context = "基于六行星知识图谱的真实历史数据：\n\n"
        for item in context_data:
            context += f"- {item['type']}: {item['name']}\n"
            if item['description']:
                context += f"  描述: {item['description']}\n"
        
        # 构建提示词
        prompt = f"""你是一位精通中国历史和术数的古代学者。
请基于以下真实的历史数据回答问题，用古雅的语言风格，可以适当加入剧说元素。

{context}

问题：{question}

请结合这些真实的历史数据进行分析和回答。"""
        
        try:
            response = self.model.generate_content(prompt)
            return response.text if response.text else "抱歉，无法生成回答。"
        except Exception as e:
            return f"生成回答时出错: {str(e)}"
    
    def interactive_demo(self):
        """交互式演示"""
        if not self.test_neo4j_connection():
            return
        
        # 显示数据库概览
        try:
            node_stats, rel_stats = self.get_database_overview()
            print("\n📊 数据库概览:")
            print("节点类型:")
            for label, count in list(node_stats.items())[:5]:
                print(f"  {label}: {count:,} 个")
            print("关系类型:")
            for rel_type, count in list(rel_stats.items())[:5]:
                print(f"  {rel_type}: {count:,} 个")
        except Exception as e:
            print(f"获取数据库概览失败: {e}")
        
        print("\n" + "="*50)
        print("💬 开始交互式问答")
        print("输入 'quit' 退出")
        print("示例问题：")
        print("- 司马迁")
        print("- 黄巾起义")
        print("- 六壬")
        print("- 太公心易")
        print("-" * 50)
        
        while True:
            user_input = input("\n请输入搜索关键词或问题: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            if not user_input:
                continue
            
            print(f"\n🔍 搜索: {user_input}")
            print("-" * 30)
            
            # 搜索相关数据
            search_results = self.search_historical_data(user_input)
            
            if not search_results:
                print("❌ 未找到相关数据")
                continue
            
            print("📚 找到相关数据:")
            for i, item in enumerate(search_results, 1):
                print(f"{i}. [{item['type']}] {item['name']}")
                if item['description']:
                    print(f"   {item['description'][:100]}...")
            
            # 如果是人物，显示关系
            if search_results and search_results[0]['type'] == 'Person':
                person_name = search_results[0]['name']
                relationships = self.get_person_relationships(person_name)
                if relationships:
                    print(f"\n🔗 {person_name}的关系:")
                    for rel in relationships[:3]:
                        print(f"  {rel['relationship']} -> {rel['related_name']} ({rel['related_type']})")
            
            # 向Gemini提问
            print(f"\n🤖 Gemini分析:")
            print("-" * 30)
            
            question = f"请分析关于'{user_input}'的历史信息"
            answer = self.ask_gemini_about_data(question, search_results[:3])
            print(answer)
            
            print("\n" + "="*50)
    
    def close(self):
        """关闭连接"""
        if self.driver:
            self.driver.close()

def main():
    """主函数"""
    demo = RealDataGeminiDemo()
    
    try:
        demo.interactive_demo()
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用天权-Haystack系统！")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {str(e)}")
    finally:
        demo.close()

if __name__ == "__main__":
    main()
