#!/bin/bash

# 天权-Haystack 项目初始化脚本
# 用于快速搭建开发环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查Python版本
check_python_version() {
    log_info "检查Python版本..."
    python_version=$(python3 --version 2>&1 | awk '{print $2}')
    required_version="3.9"
    
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
        log_success "Python版本检查通过: $python_version"
    else
        log_error "需要Python 3.9或更高版本，当前版本: $python_version"
        exit 1
    fi
}

# 创建虚拟环境
create_virtual_env() {
    log_info "创建Python虚拟环境..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "虚拟环境创建成功"
    else
        log_warning "虚拟环境已存在，跳过创建"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    log_info "虚拟环境已激活"
    
    # 升级pip
    pip install --upgrade pip
    log_success "pip已升级到最新版本"
}

# 安装Python依赖
install_python_dependencies() {
    log_info "安装Python依赖包..."
    
    # 确保虚拟环境已激活
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        source venv/bin/activate
    fi
    
    # 安装基础依赖
    pip install -r requirements.txt
    log_success "Python依赖安装完成"
    
    # 安装开发依赖
    if [ -f "requirements-dev.txt" ]; then
        pip install -r requirements-dev.txt
        log_success "开发依赖安装完成"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建项目目录结构..."
    
    directories=(
        "data/raw"
        "data/processed"
        "data/embeddings"
        "data/models"
        "data/dictionaries"
        "logs"
        "tests/data"
        "core/document_stores"
        "core/retrievers"
        "core/nodes"
        "core/prompts"
        "core/divination"
        "core/preprocessors"
        "core/converters"
        "api/routes"
        "api/schemas"
        "frontend/components"
        "utils"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    log_success "目录结构创建完成"
}

# 创建配置文件
setup_config_files() {
    log_info "设置配置文件..."
    
    # 复制环境变量配置
    if [ ! -f ".env" ]; then
        cp .env.example .env
        log_success "环境配置文件已创建: .env"
        log_warning "请编辑 .env 文件，填入实际的API密钥和配置"
    else
        log_warning ".env 文件已存在，跳过创建"
    fi
    
    # 创建日志配置
    if [ ! -f "config/logging.yml" ]; then
        cat > config/logging.yml << 'EOF'
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '{asctime} | {levelname} | {name}:{funcName}:{lineno} | {message}'
    style: '{'
  detailed:
    format: '{asctime} | {levelname} | {name}:{funcName}:{lineno} | {message} | {pathname}'
    style: '{'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/tianquan.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

loggers:
  tianquan:
    level: DEBUG
    handlers: [console, file]
    propagate: false
  
  haystack:
    level: INFO
    handlers: [console, file]
    propagate: false

root:
  level: INFO
  handlers: [console, file]
EOF
        log_success "日志配置文件已创建"
    fi
}

# 启动Docker服务
start_docker_services() {
    log_info "启动Docker服务..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker未运行，请启动Docker服务"
        exit 1
    fi
    
    # 启动基础服务
    docker-compose up -d neo4j redis
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker服务启动成功"
    else
        log_error "Docker服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化Neo4j数据库..."
    
    # 等待Neo4j完全启动
    log_info "等待Neo4j启动完成..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec tianquan-neo4j cypher-shell -u neo4j -p tianquan123 "RETURN 1" &> /dev/null; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Neo4j启动超时"
        exit 1
    fi
    
    # 创建数据库约束和索引
    docker exec tianquan-neo4j cypher-shell -u neo4j -p tianquan123 "
        CREATE CONSTRAINT document_id_unique IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE;
        CREATE CONSTRAINT author_name_unique IF NOT EXISTS FOR (a:Author) REQUIRE a.name IS UNIQUE;
        CREATE CONSTRAINT book_title_unique IF NOT EXISTS FOR (b:Book) REQUIRE b.title IS UNIQUE;
        CREATE CONSTRAINT category_name_unique IF NOT EXISTS FOR (c:Category) REQUIRE c.name IS UNIQUE;
        CREATE CONSTRAINT dynasty_name_unique IF NOT EXISTS FOR (d:Dynasty) REQUIRE d.name IS UNIQUE;
    "
    
    log_success "Neo4j数据库初始化完成"
}

# 下载模型
download_models() {
    log_info "下载预训练模型..."
    
    # 确保虚拟环境已激活
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        source venv/bin/activate
    fi
    
    # 下载中文embedding模型
    python3 -c "
from sentence_transformers import SentenceTransformer
import os

model_path = 'data/models/chinese-embedding'
if not os.path.exists(model_path):
    print('下载中文embedding模型...')
    model = SentenceTransformer('shibing624/text2vec-base-chinese')
    model.save(model_path)
    print('中文embedding模型下载完成')
else:
    print('中文embedding模型已存在')
"
    
    log_success "模型下载完成"
}

# 创建示例数据
create_sample_data() {
    log_info "创建示例数据..."
    
    # 创建示例古籍文本
    cat > data/raw/sample_taigong_xinyi.txt << 'EOF'
太公心易序

太公心易者，乃太公望所传之易学心法也。其法以心为主，以易为用，心易合一，方能洞察天机。

夫易者，变化之道也。天地万物，无不在变化之中。圣人观象于天，察法于地，近取诸身，远取诸物，于是始作八卦，以通神明之德，以类万物之情。

太公曰："心者，神之舍也；易者，道之门也。心不静则神不安，神不安则易不明。故学易者，必先养心；养心者，必先去欲。"

又曰："天有三宝日月星，地有三宝水火风，人有三宝精气神。精气神合一，则与天地同德，与日月同明，与四时同序，与鬼神同吉凶。"

其法分为三层：
一曰观象，二曰明理，三曰应用。

观象者，观天地之象，察阴阳之变，知五行之生克，明八卦之消息。
明理者，明易之理，知变之道，察时之宜，识机之微。
应用者，应天时，顺地利，合人和，趋吉避凶。

太公心易之要诀：
心静如水，神清如镜，意专如一，气和如春。
观象不拘于象，明理不滞于理，应用不执于用。
变通无穷，妙用无尽，此太公心易之真谛也。
EOF
    
    log_success "示例数据创建完成"
}

# 运行测试
run_tests() {
    log_info "运行基础测试..."
    
    # 确保虚拟环境已激活
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        source venv/bin/activate
    fi
    
    # 创建简单测试
    cat > tests/test_basic.py << 'EOF'
import pytest
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_import_core_modules():
    """测试核心模块导入"""
    try:
        from utils.config_loader import load_config
        from utils.logger import setup_logger
        assert True
    except ImportError as e:
        pytest.fail(f"模块导入失败: {e}")

def test_config_loading():
    """测试配置加载"""
    from utils.config_loader import load_config
    config = load_config("config/haystack.yml")
    assert config is not None
    assert "system" in config

def test_environment_variables():
    """测试环境变量"""
    from dotenv import load_dotenv
    load_dotenv()
    # 这里只测试加载，不验证具体值
    assert True

if __name__ == "__main__":
    pytest.main([__file__])
EOF
    
    # 运行测试
    python -m pytest tests/test_basic.py -v
    
    if [ $? -eq 0 ]; then
        log_success "基础测试通过"
    else
        log_warning "部分测试失败，请检查配置"
    fi
}

# 显示使用说明
show_usage() {
    log_success "天权-Haystack 项目初始化完成！"
    echo
    echo "接下来的步骤："
    echo "1. 编辑 .env 文件，填入你的API密钥"
    echo "2. 激活虚拟环境: source venv/bin/activate"
    echo "3. 摄取古籍数据: python pipeline/ingest.py --source ../shushubook/太公心易.txt"
    echo "4. 启动API服务: uvicorn api.app:app --reload"
    echo "5. 启动前端界面: streamlit run frontend/streamlit_app.py"
    echo
    echo "有用的命令："
    echo "- 查看Docker服务状态: docker-compose ps"
    echo "- 查看Neo4j界面: http://localhost:7474"
    echo "- 查看API文档: http://localhost:8000/docs"
    echo "- 查看Streamlit界面: http://localhost:8501"
    echo
    echo "如需帮助，请查看 README.md 文件"
}

# 主函数
main() {
    log_info "开始初始化天权-Haystack项目..."
    
    # 检查必要的命令
    check_command "python3"
    check_command "pip"
    check_command "docker"
    check_command "docker-compose"
    
    # 执行初始化步骤
    check_python_version
    create_directories
    create_virtual_env
    install_python_dependencies
    setup_config_files
    start_docker_services
    init_database
    download_models
    create_sample_data
    run_tests
    
    # 显示使用说明
    show_usage
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
