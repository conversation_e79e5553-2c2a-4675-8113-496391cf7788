#!/usr/bin/env python3
"""
简单的Gemini测试脚本
"""

import os
import google.generativeai as gena<PERSON>

def test_gemini():
    """测试Gemini API"""
    
    # 配置API密钥
    api_key = "AIzaSyCalfmTapVVTYWVAPm923sz7UtitZbmsaM"
    genai.configure(api_key=api_key)
    
    print("🤖 测试Gemini API连接...")
    
    try:
        # 创建模型
        model = genai.GenerativeModel('gemini-1.5-pro')
        
        # 测试问题
        questions = [
            {
                "question": "什么是太公心易？",
                "role": "太公望",
                "prompt": """你是太公望，周朝的开国功臣，精通兵法和易学。
请以太公的身份和语气回答问题，体现出军师的智慧和易学的深度。
语言要有古代贤者的风范，可以结合兵法和治国的智慧。

请以剧说的方式回答以下问题，可以加入适当的动作描述和场景设置：

问题：什么是太公心易？"""
            },
            {
                "question": "如何理解八卦？",
                "role": "伏羲",
                "prompt": """你是伏羲，传说中的易学始祖，八卦的创造者。
请以伏羲的身份回答问题，体现出创世者的智慧和对宇宙规律的深刻理解。

请以剧说的方式回答以下问题：

问题：如何理解八卦的含义？"""
            }
        ]
        
        for i, item in enumerate(questions, 1):
            print(f"\n问题 {i}: {item['question']}")
            print(f"角色: {item['role']}")
            print("-" * 50)
            
            try:
                # 生成回答
                response = model.generate_content(item['prompt'])
                
                if response.text:
                    print("回答:")
                    print(response.text)
                    print()
                    print(f"生成成功 ✅")
                else:
                    print("❌ 生成失败：空响应")
                
            except Exception as e:
                print(f"❌ 生成失败: {str(e)}")
            
            print("=" * 50)
            
            if i < len(questions):
                input("按回车键继续下一个问题...")
        
        print("\n🎉 Gemini API测试完成！")
        
    except Exception as e:
        print(f"❌ Gemini API连接失败: {str(e)}")

if __name__ == "__main__":
    test_gemini()
