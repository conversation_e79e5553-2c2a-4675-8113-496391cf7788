# 🤖 Gemini集成指南

天权-Haystack系统完全支持Google Gemini模型！Gemini在中文理解和术数领域表现优秀，特别适合古籍文献的分析和剧说式对话。

## 🚀 快速开始

### 1. 获取Gemini API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录你的Google账号
3. 点击"Create API Key"创建新的API密钥
4. 复制生成的API密钥

### 2. 配置API密钥

#### 方法一：环境变量（推荐）
```bash
export GEMINI_API_KEY="your_api_key_here"
```

#### 方法二：.env文件
```bash
# 编辑.env文件
echo "GEMINI_API_KEY=your_api_key_here" >> .env
```

### 3. 安装依赖
```bash
pip install google-generativeai
```

### 4. 运行Gemini演示
```bash
cd tianquan-haystack
python3 gemini_demo.py
```

## 🎯 Gemini的优势

### ✅ **中文理解能力强**
- 对古文和文言文有很好的理解
- 能准确把握术数概念的含义
- 支持繁体字和古代用词

### ✅ **免费额度充足**
- 每分钟15次请求
- 每天1500次请求
- 对于学习和研究完全够用

### ✅ **多模态支持**
- 支持文本和图像输入
- 可以分析古籍图片和卦象图
- 未来可扩展到符号识别

### ✅ **安全性好**
- 内置安全过滤机制
- 可自定义安全等级
- 适合教育和文化应用

## 🔧 配置选项

### 模型选择
```yaml
# config/haystack.yml
models:
  gemini:
    model_name: "gemini-1.5-pro"  # 推荐，理解能力最强
    # model_name: "gemini-1.5-flash"  # 更快，适合简单任务
```

### 生成参数
```yaml
models:
  gemini:
    temperature: 0.7      # 创造性，0-1
    max_tokens: 2048      # 最大输出长度
    top_p: 0.8           # 采样参数
    top_k: 40            # 候选词数量
```

### 安全设置
```yaml
models:
  gemini:
    safety_settings:
      harassment: "BLOCK_NONE"          # 不过滤骚扰内容
      hate_speech: "BLOCK_NONE"         # 不过滤仇恨言论
      sexually_explicit: "BLOCK_NONE"   # 不过滤性内容
      dangerous_content: "BLOCK_NONE"   # 不过滤危险内容
```

## 🎭 角色设定

天权-Haystack为Gemini预设了多个术数角色：

### 📚 **易学大师**
- 精通易经和八卦
- 语言古雅，引经据典
- 适合易学概念解释

### ⚔️ **太公望**
- 周朝军师，易学高手
- 结合兵法和治国智慧
- 适合战略性问题

### 🌸 **邵雍（邵康节）**
- 梅花易数创始人
- 象数易学专家
- 适合占卜和预测

### 🔮 **六壬大师**
- 精通六壬术数
- 专业术语和理论
- 适合六壬相关问题

### 🗡️ **奇门大师**
- 精通奇门遁甲
- 玄妙而实用
- 适合奇门相关问题

## 💡 使用示例

### 基础问答
```python
from core.generators.gemini_generator import TianquanGeminiGenerator

generator = TianquanGeminiGenerator()

result = generator.generate_explanation(
    question="什么是太公心易？",
    role="太公望",
    style="剧说"
)

print(result['answer'])
```

### 占卜解释
```python
divination_result = {
    "method": "六壬",
    "course": "贵人临门",
    "interpretation": "吉象"
}

explanation = generator.generate_divination_interpretation(
    divination_result=divination_result,
    question="今日运势如何？",
    method="六壬"
)

print(explanation)
```

## 🔍 故障排除

### 问题1：API密钥无效
```
错误：Invalid API key
解决：检查API密钥是否正确，是否已启用Gemini API
```

### 问题2：请求频率限制
```
错误：Rate limit exceeded
解决：等待一分钟后重试，或升级到付费计划
```

### 问题3：安全过滤
```
错误：Content filtered for safety
解决：调整safety_settings为BLOCK_NONE
```

### 问题4：包导入失败
```
错误：No module named 'google.generativeai'
解决：pip install google-generativeai
```

## 📊 性能对比

| 模型 | 中文理解 | 古文理解 | 创造性 | 速度 | 成本 |
|------|----------|----------|--------|------|------|
| Gemini-1.5-Pro | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 免费 |
| GPT-4 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | 付费 |
| Claude-3 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 付费 |

## 🎯 最佳实践

### 1. **提示词优化**
- 使用具体的角色设定
- 提供充足的上下文
- 明确输出格式要求

### 2. **参数调优**
- temperature=0.7 适合创造性回答
- temperature=0.3 适合事实性回答
- max_tokens根据需要调整

### 3. **错误处理**
- 设置重试机制
- 处理安全过滤情况
- 记录API使用情况

### 4. **成本控制**
- 合理设置max_tokens
- 避免不必要的重复请求
- 使用缓存机制

## 🔮 未来扩展

### 多模态功能
- 古籍图片识别
- 卦象图分析
- 符号自动识别

### 专业化训练
- 术数专用词典
- 古文语料微调
- 领域知识增强

### 实时交互
- 语音对话支持
- 实时占卜解释
- 动态角色切换

---

**开始你的Gemini术数之旅吧！** 🌟
