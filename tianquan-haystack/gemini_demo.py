#!/usr/bin/env python3
"""
天权-Haystack Gemini演示脚本
展示如何使用Google Gemini进行术数AI生成
"""

import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def check_gemini_setup():
    """检查Gemini设置"""
    print("🔍 检查Gemini配置...")
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ 未找到GEMINI_API_KEY环境变量")
        print("请设置你的Gemini API密钥：")
        print("export GEMINI_API_KEY='your_api_key_here'")
        print()
        print("或者在.env文件中添加：")
        print("GEMINI_API_KEY=your_api_key_here")
        print()
        print("获取API密钥：https://makersuite.google.com/app/apikey")
        return False
    
    print(f"✅ 找到API密钥: {api_key[:10]}...")
    
    # 检查google-generativeai包
    try:
        import google.generativeai as genai
        print("✅ google-generativeai包已安装")
        return True
    except ImportError:
        print("❌ 缺少google-generativeai包")
        print("请安装: pip install google-generativeai")
        return False

def demo_with_real_gemini():
    """使用真实Gemini API的演示"""
    print("🤖 使用真实Gemini API演示")
    print("=" * 50)
    
    try:
        from core.generators.gemini_generator import TianquanGeminiGenerator
        
        # 初始化生成器
        generator = TianquanGeminiGenerator()
        print("✅ Gemini生成器初始化成功")
        print()
        
        # 演示问题
        questions = [
            {"question": "什么是太公心易？", "role": "太公望"},
            {"question": "如何理解八卦的含义？", "role": "易学大师"},
            {"question": "六壬占卜的基本原理是什么？", "role": "六壬大师"},
            {"question": "梅花易数如何起卦？", "role": "邵雍"}
        ]
        
        for i, item in enumerate(questions, 1):
            print(f"问题 {i}: {item['question']}")
            print(f"角色: {item['role']}")
            print("-" * 40)
            
            try:
                result = generator.generate_explanation(
                    question=item['question'],
                    role=item['role'],
                    style="剧说",
                    max_tokens=800
                )
                
                print("回答:")
                print(result['answer'])
                print()
                print(f"模型信息: {result['meta'].get('model', 'unknown')}")
                print(f"Token使用: {result['meta'].get('total_tokens', 'unknown')}")
                
            except Exception as e:
                print(f"❌ 生成失败: {str(e)}")
            
            print("=" * 50)
            
            if i < len(questions):
                input("按回车键继续下一个问题...")
                print()
    
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")

def demo_with_mock_gemini():
    """使用模拟Gemini的演示"""
    print("🎭 使用模拟Gemini演示")
    print("=" * 50)
    
    try:
        from core.generators.gemini_generator import MockGeminiGenerator
        
        # 初始化模拟生成器
        generator = MockGeminiGenerator()
        print("✅ 模拟Gemini生成器初始化成功")
        print()
        
        # 演示问题
        questions = [
            "什么是太公心易？",
            "如何理解八卦？", 
            "六壬占卜的原理",
            "易经的核心思想"
        ]
        
        for i, question in enumerate(questions, 1):
            print(f"问题 {i}: {question}")
            print("-" * 40)
            
            result = generator.generate_explanation(
                question=question,
                role="易学大师",
                style="剧说"
            )
            
            print("回答:")
            print(result['answer'])
            print()
            print(f"模型: {result['meta']['model']}")
            print(f"字符数: {result['meta']['tokens']}")
            print("=" * 50)
            
            if i < len(questions):
                input("按回车键继续下一个问题...")
                print()
    
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

def interactive_gemini_chat():
    """交互式Gemini对话"""
    print("💬 交互式Gemini对话")
    print("输入 'quit' 退出，'role:角色名' 切换角色")
    print("-" * 50)
    
    # 检查是否有真实API
    has_real_api = check_gemini_setup()
    
    if has_real_api:
        try:
            from core.generators.gemini_generator import TianquanGeminiGenerator
            generator = TianquanGeminiGenerator()
            print("✅ 使用真实Gemini API")
        except:
            from core.generators.gemini_generator import MockGeminiGenerator
            generator = MockGeminiGenerator()
            print("⚠️  使用模拟Gemini（API初始化失败）")
    else:
        from core.generators.gemini_generator import MockGeminiGenerator
        generator = MockGeminiGenerator()
        print("⚠️  使用模拟Gemini（未配置API）")
    
    print()
    
    # 可选角色
    roles = ["易学大师", "太公望", "邵雍", "六壬大师", "奇门大师"]
    current_role = "易学大师"
    
    print(f"当前角色: {current_role}")
    print(f"可选角色: {', '.join(roles)}")
    print()
    
    while True:
        user_input = input("请输入问题: ").strip()
        
        if user_input.lower() == 'quit':
            break
        
        if user_input.startswith('role:'):
            new_role = user_input[5:].strip()
            if new_role in roles:
                current_role = new_role
                print(f"✅ 已切换到角色: {current_role}")
            else:
                print(f"❌ 未知角色，可选: {', '.join(roles)}")
            continue
        
        if not user_input:
            continue
        
        print(f"\n【{current_role}】正在思考...")
        print("-" * 30)
        
        try:
            result = generator.generate_explanation(
                question=user_input,
                role=current_role,
                style="剧说",
                max_tokens=600
            )
            
            print(result['answer'])
            
        except Exception as e:
            print(f"❌ 生成失败: {str(e)}")
        
        print("\n" + "=" * 50 + "\n")

def main():
    """主函数"""
    print("🌟 天权-Haystack Gemini演示")
    print("=" * 40)
    print()
    
    while True:
        print("请选择演示模式:")
        print("1. 真实Gemini API演示")
        print("2. 模拟Gemini演示")
        print("3. 交互式对话")
        print("4. 检查Gemini配置")
        print("0. 退出")
        print()
        
        choice = input("请输入选项 (0-4): ").strip()
        print()
        
        if choice == "0":
            print("感谢使用天权-Haystack Gemini演示！")
            break
        elif choice == "1":
            if check_gemini_setup():
                demo_with_real_gemini()
            else:
                print("请先配置Gemini API密钥")
        elif choice == "2":
            demo_with_mock_gemini()
        elif choice == "3":
            interactive_gemini_chat()
        elif choice == "4":
            check_gemini_setup()
        else:
            print("无效选项，请重新选择")
        
        if choice != "0":
            input("\n按回车键继续...")
            print("\n" + "="*40 + "\n")

if __name__ == "__main__":
    main()
