# 天权-Haystack 术数知识图谱系统

## 🎯 项目概述

天权-Haystack是基于Haystack框架构建的术数典籍知识图谱系统，专门用于处理易经、六壬、奇门、太乙等传统术数文献，实现结构化知识检索、语义推理和剧说式AI对话。

## 🏗️ 系统架构

```
📦 tianquan-haystack/
├── 📊 data/                    # 数据目录
│   ├── raw/                    # 原始古籍文本
│   ├── processed/              # 处理后的结构化数据
│   └── embeddings/             # 向量化数据
├── 🔧 pipeline/                # Haystack Pipeline组件
│   ├── ingest.py              # 文档摄取管道
│   ├── retrieve.py            # 检索器组件
│   ├── generate.py            # 生成器组件
│   ├── tools.py               # 自定义工具节点
│   └── graph.py               # Neo4j图谱操作
├── 🧠 core/                    # 核心业务逻辑
│   ├── document_stores/        # 文档存储器
│   ├── retrievers/            # 检索器实现
│   ├── nodes/                 # 自定义节点
│   └── prompts/               # 提示词模板
├── 🌐 api/                     # API接口
│   ├── app.py                 # FastAPI应用
│   ├── routes/                # 路由定义
│   └── schemas/               # 数据模型
├── 🎨 frontend/                # 前端界面
│   ├── streamlit_app.py       # Streamlit应用
│   └── components/            # UI组件
├── ⚙️ config/                  # 配置文件
│   ├── haystack.yml           # Haystack配置
│   ├── neo4j.yml              # Neo4j配置
│   └── models.yml             # 模型配置
├── 🧪 tests/                   # 测试文件
└── 📋 requirements.txt         # 依赖包
```

## 🚀 核心特性

### 1. 多源知识整合
- **古籍文档处理**: 支持文言文分词、章节结构保留
- **Neo4j图谱集成**: 基于关系的语义检索和路径推理
- **多模态数据**: 文本、图像、符号的统一处理

### 2. 智能检索系统
- **语义检索**: 基于embedding的相似度搜索
- **图谱检索**: 基于Neo4j的关系路径查询
- **混合检索**: 结合向量和图谱的多维度检索

### 3. 剧说式AI对话
- **角色扮演**: 易学大师、术数专家等角色模拟
- **多模型协作**: GPT-4、Claude等模型的协同推理
- **结构化输出**: 卦象、爻辞、解释的结构化生成

### 4. 术数工具集成
- **三式占卜**: 六壬、奇门、太乙的自动化计算
- **易经推演**: 卦象变化、爻辞解读
- **天象关联**: 历史事件与天文现象的关联分析

## 🛠️ 技术栈

### 后端核心
- **Haystack 2.0**: RAG框架和Pipeline编排
- **Neo4j**: 知识图谱存储和查询
- **FastAPI**: 高性能API服务
- **Pydantic**: 数据验证和序列化

### AI模型
- **OpenAI GPT-4**: 主要语言模型
- **Claude-3**: 辅助推理模型
- **HuggingFace Transformers**: 中文embedding模型
- **Sentence-Transformers**: 语义向量化

### 数据处理
- **jieba**: 中文分词
- **pypinyin**: 拼音转换
- **pandas**: 数据处理
- **numpy**: 数值计算

## 📚 数据源

### 第一阶段目标文献
- **《太公心易》**: 易经与预测的结合
- **《梅花易数》**: 象数易学经典
- **《六壬大全》**: 六壬术数集成
- **《奇门遁甲》**: 奇门术数要典

### 扩展文献库
- 200+ 术数典籍 (来自../shushubook/)
- 史书数据 (史记、汉书、后汉书、三国志)
- 天文历法资料

## 🎯 使用场景

### 1. 学术研究
- 术数文献的语义检索和比较分析
- 历史事件与术数理论的关联研究
- 不同流派观点的对比分析

### 2. 教育培训
- 易学知识的结构化学习
- 术数概念的可视化展示
- 互动式问答和案例分析

### 3. 实用应用
- 基于古法的占卜推演
- 现代决策的术数参考
- 文化传承的数字化保护

## 🚀 快速开始

### 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd tianquan-haystack

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，配置API密钥和数据库连接
```

### 数据初始化
```bash
# 启动Neo4j数据库
docker-compose up -d neo4j

# 运行数据摄取
python pipeline/ingest.py --source ../shushubook/太公心易.txt

# 构建向量索引
python pipeline/build_index.py
```

### 启动服务
```bash
# 启动API服务
uvicorn api.app:app --reload --port 8000

# 启动前端界面
streamlit run frontend/streamlit_app.py --server.port 8501
```

## 📖 API文档

### 核心接口
- `POST /api/v1/search/semantic`: 语义搜索
- `POST /api/v1/search/graph`: 图谱检索
- `POST /api/v1/generate/explanation`: 术数解释生成
- `POST /api/v1/divination/liuren`: 六壬占卜
- `POST /api/v1/divination/qimen`: 奇门遁甲

### 示例请求
```python
import requests

# 语义搜索示例
response = requests.post("http://localhost:8000/api/v1/search/semantic", 
    json={"query": "太公心易中关于时间的论述", "top_k": 5})

# 术数解释生成示例
response = requests.post("http://localhost:8000/api/v1/generate/explanation",
    json={"context": "乾卦", "question": "如何理解乾卦的象征意义？"})
```

## 🤝 贡献指南

1. Fork项目仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢Haystack团队提供优秀的RAG框架
- 感谢Neo4j提供强大的图数据库
- 感谢开源社区的贡献和支持
