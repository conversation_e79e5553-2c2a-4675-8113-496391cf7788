#!/usr/bin/env python3
"""
Google Gemini生成器
专门用于术数领域的AI生成
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union
import json

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

# from haystack import component, Document
# from haystack.components.generators.utils import serialize_callback_handler

logger = logging.getLogger(__name__)

# @component
class GeminiGenerator:
    """Google Gemini生成器组件"""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gemini-1.5-pro",
        temperature: float = 0.7,
        max_output_tokens: int = 2048,
        top_p: float = 0.8,
        top_k: int = 40,
        safety_settings: Optional[Dict[str, str]] = None,
        generation_kwargs: Optional[Dict[str, Any]] = None
    ):
        """
        初始化Gemini生成器
        
        Args:
            api_key: Gemini API密钥
            model: 模型名称
            temperature: 生成温度
            max_output_tokens: 最大输出token数
            top_p: Top-p采样参数
            top_k: Top-k采样参数
            safety_settings: 安全设置
            generation_kwargs: 其他生成参数
        """
        if not GEMINI_AVAILABLE:
            raise ImportError(
                "google-generativeai包未安装。请运行: pip install google-generativeai"
            )
        
        # 配置API密钥
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("需要提供GEMINI_API_KEY环境变量或api_key参数")
        
        genai.configure(api_key=self.api_key)
        
        # 模型配置
        self.model_name = model
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.top_p = top_p
        self.top_k = top_k
        
        # 安全设置
        if safety_settings is None:
            safety_settings = {
                "harassment": "BLOCK_NONE",
                "hate_speech": "BLOCK_NONE", 
                "sexually_explicit": "BLOCK_NONE",
                "dangerous_content": "BLOCK_NONE"
            }
        
        self.safety_settings = self._convert_safety_settings(safety_settings)
        
        # 生成配置
        self.generation_config = genai.types.GenerationConfig(
            temperature=self.temperature,
            max_output_tokens=self.max_output_tokens,
            top_p=self.top_p,
            top_k=self.top_k
        )
        
        # 其他参数
        self.generation_kwargs = generation_kwargs or {}
        
        # 初始化模型
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=self.generation_config,
            safety_settings=self.safety_settings
        )
        
        logger.info(f"Gemini生成器初始化完成: {self.model_name}")
    
    def _convert_safety_settings(self, settings: Dict[str, str]) -> List:
        """转换安全设置格式"""
        safety_map = {
            "harassment": HarmCategory.HARM_CATEGORY_HARASSMENT,
            "hate_speech": HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            "sexually_explicit": HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            "dangerous_content": HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT
        }
        
        threshold_map = {
            "BLOCK_NONE": HarmBlockThreshold.BLOCK_NONE,
            "BLOCK_ONLY_HIGH": HarmBlockThreshold.BLOCK_ONLY_HIGH,
            "BLOCK_MEDIUM_AND_ABOVE": HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            "BLOCK_LOW_AND_ABOVE": HarmBlockThreshold.BLOCK_LOW_AND_ABOVE
        }
        
        safety_settings = []
        for category, threshold in settings.items():
            if category in safety_map and threshold in threshold_map:
                safety_settings.append({
                    "category": safety_map[category],
                    "threshold": threshold_map[threshold]
                })
        
        return safety_settings
    
    # @component.output_types(replies=List[str], meta=List[Dict[str, Any]])
    def run(
        self,
        prompt: str,
        generation_kwargs: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        运行Gemini生成
        
        Args:
            prompt: 输入提示词
            generation_kwargs: 生成参数
            
        Returns:
            包含生成结果和元数据的字典
        """
        try:
            # 合并生成参数
            kwargs = {**self.generation_kwargs}
            if generation_kwargs:
                kwargs.update(generation_kwargs)
            
            # 生成响应
            response = self.model.generate_content(
                prompt,
                **kwargs
            )
            
            # 检查响应
            if not response.text:
                logger.warning("Gemini返回空响应")
                return {"replies": [""], "meta": [{"error": "空响应"}]}
            
            # 构建元数据
            meta = {
                "model": self.model_name,
                "prompt_tokens": len(prompt.split()),  # 简单估算
                "completion_tokens": len(response.text.split()),
                "total_tokens": len(prompt.split()) + len(response.text.split()),
                "finish_reason": "stop"
            }
            
            # 添加安全评级信息
            if hasattr(response, 'prompt_feedback'):
                meta["safety_ratings"] = response.prompt_feedback
            
            return {
                "replies": [response.text],
                "meta": [meta]
            }
            
        except Exception as e:
            logger.error(f"Gemini生成失败: {str(e)}")
            return {
                "replies": [""],
                "meta": [{"error": str(e)}]
            }

class TianquanGeminiGenerator:
    """天权专用Gemini生成器，包含术数领域的专用提示词"""
    
    def __init__(self, api_key: Optional[str] = None, **kwargs):
        """初始化天权Gemini生成器"""
        self.generator = GeminiGenerator(api_key=api_key, **kwargs)
        
        # 术数领域的系统提示词
        self.system_prompts = {
            "易学大师": """你是一位精通易经的古代易学大师，拥有深厚的易学造诣。
请用古雅的语言风格回答问题，引用经典，深入浅出地解释易学概念。
在回答中可以适当使用【动作描述】来增加剧说效果。""",
            
            "太公望": """你是太公望，周朝的开国功臣，精通兵法和易学。
请以太公的身份和语气回答问题，体现出军师的智慧和易学的深度。
语言要有古代贤者的风范，可以结合兵法和治国的智慧。""",
            
            "邵雍": """你是北宋易学大师邵雍（邵康节），梅花易数的创始人。
请以邵雍的身份回答问题，体现出象数易学的特色和观物取象的智慧。
语言要体现宋代学者的理学风格。""",
            
            "六壬大师": """你是精通六壬术数的古代术士，对六壬的理论和实践都有深入研究。
请用专业的六壬术语和理论回答问题，解释六壬的原理和应用方法。
语言要体现术数家的专业性和神秘感。""",
            
            "奇门大师": """你是精通奇门遁甲的古代术士，深谙奇门的奥秘。
请用奇门遁甲的理论和术语回答问题，解释奇门的布局和应用。
语言要体现奇门术数的玄妙和实用性。"""
        }
    
    def generate_explanation(
        self,
        question: str,
        context: Optional[str] = None,
        role: str = "易学大师",
        style: str = "剧说",
        max_tokens: int = 1000
    ) -> Dict[str, Any]:
        """
        生成术数解释
        
        Args:
            question: 用户问题
            context: 上下文信息
            role: 角色设定
            style: 回答风格
            max_tokens: 最大token数
            
        Returns:
            生成结果
        """
        # 构建提示词
        system_prompt = self.system_prompts.get(role, self.system_prompts["易学大师"])
        
        prompt_parts = [system_prompt]
        
        if context:
            prompt_parts.append(f"\n参考文献：\n{context}")
        
        if style == "剧说":
            prompt_parts.append(f"\n请以剧说的方式回答以下问题，可以加入适当的动作描述和场景设置：")
        else:
            prompt_parts.append(f"\n请回答以下问题：")
        
        prompt_parts.append(f"\n问题：{question}")
        
        full_prompt = "\n".join(prompt_parts)
        
        # 生成回答
        result = self.generator.run(
            prompt=full_prompt,
            generation_kwargs={}
        )
        
        return {
            "answer": result["replies"][0] if result["replies"] else "",
            "meta": result["meta"][0] if result["meta"] else {},
            "role": role,
            "style": style
        }
    
    def generate_divination_interpretation(
        self,
        divination_result: Dict[str, Any],
        question: str,
        method: str = "六壬"
    ) -> str:
        """
        生成占卜解释
        
        Args:
            divination_result: 占卜结果
            question: 占卜问题
            method: 占卜方法
            
        Returns:
            解释文本
        """
        role_map = {
            "六壬": "六壬大师",
            "奇门": "奇门大师",
            "梅花": "邵雍",
            "易经": "易学大师"
        }
        
        role = role_map.get(method, "易学大师")
        
        prompt = f"""
{self.system_prompts[role]}

现在有一个{method}占卜的结果，请你详细解释其含义：

占卜问题：{question}
占卜结果：{json.dumps(divination_result, ensure_ascii=False, indent=2)}

请从以下几个方面进行解释：
1. 卦象/课式的基本含义
2. 对问题的直接回答
3. 吉凶判断和建议
4. 需要注意的事项

请用剧说的方式，以{role}的身份进行解释。
"""
        
        result = self.generator.run(prompt=prompt)
        return result["replies"][0] if result["replies"] else "解释生成失败"

# 简化的演示版本（不需要API密钥）
class MockGeminiGenerator:
    """模拟Gemini生成器，用于演示"""
    
    def __init__(self, **kwargs):
        self.responses = {
            "太公心易": """【太公望抚须而笑，目光深邃如星海】

吾之心易，非寻常占卜之术也。夫易者，变化之道；心者，神明之府。

世人学易，多拘于卦象，执于爻辞，不知易之真谛在于心法。
吾传心易三要：一曰静心，二曰观象，三曰应变。

静心者，去除杂念，如止水明镜；
观象者，察天地之变，知阴阳之理；
应变者，顺时而动，因势利导。

如此则心与易合，易与道通，可洞察天机，趋吉避凶矣！

【太公望起身，指向窗外】

汝观此梅花数朵，虽在寒冬，却傲雪而开。此即心易之象也——
不为外境所动，内心自有春意。学易者当如此梅花，
外观天地之象，内养浩然之气，方能得易之真传。""",
            
            "八卦": """【伏羲仰观天象，俯察地理，手持龟甲】

吾观天地万象，始画八卦。

乾为天，纯阳之象，刚健不息，如龙腾九霄；
坤为地，纯阴之德，厚德载物，如马行千里；
震为雷，动而有威，奋发向上，如春雷惊蛰；
巽为风，入而有序，柔顺谦逊，如清风徐来；
坎为水，险而有信，智慧深沉，如江河奔流；
离为火，明而有礼，光明磊落，如日照大地；
艮为山，止而有节，稳重踏实，如泰山巍峨；
兑为泽，悦而有义，和谐喜悦，如湖水澄澈。

【伏羲将八卦图展示】

八卦相荡，六十四卦生焉。万物之理，尽在其中矣！
天地定位，山泽通气，雷风相薄，水火不相射。
此乃宇宙之根本，造化之枢机也。""",
            
            "六壬": """【六壬大师端坐案前，手持式盘，神情专注】

六壬者，式占之首也。以壬为主，配合天地人三才。

今观汝之课式：
四课已立，三传已明。
贵人临门，青龙护身，此乃吉象也。

初传见财，中传逢官，末传归库，
此课主事业有成，财源广进之象。

然需防白虎作祟，勾陈缠绕。
宜择吉日而行，避凶时而止。

【大师抚须沉思】

壬水通根，甲木得生，
此时正是发展良机。
但需谨记：顺天时，应人事，
方能趋吉避凶，事半功倍。"""
        }
    
    def generate_explanation(self, question: str, **kwargs) -> Dict[str, Any]:
        """模拟生成解释"""
        # 简单的关键词匹配
        for key, response in self.responses.items():
            if key in question:
                return {
                    "answer": response,
                    "meta": {"model": "mock-gemini", "tokens": len(response)},
                    "role": kwargs.get("role", "易学大师"),
                    "style": kwargs.get("style", "剧说")
                }
        
        # 默认回答
        return {
            "answer": f"【易学大师沉思片刻】\n\n关于「{question}」这个问题，需要结合具体的卦象和时机来分析。\n\n易经云：「穷则变，变则通，通则久。」万事万物皆在变化之中，关键在于把握变化的规律和时机。",
            "meta": {"model": "mock-gemini", "tokens": 100},
            "role": kwargs.get("role", "易学大师"),
            "style": kwargs.get("style", "剧说")
        }
