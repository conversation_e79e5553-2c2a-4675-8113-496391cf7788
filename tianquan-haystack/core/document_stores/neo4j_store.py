#!/usr/bin/env python3
"""
Neo4j文档存储器
专门用于术数典籍的图谱存储和检索
"""

import logging
from typing import List, Dict, Any, Optional, Union
import uuid
from datetime import datetime

import numpy as np
from neo4j import GraphDatabase, Driver, Session
from haystack import Document
from haystack.document_stores.types import DuplicatePolicy

logger = logging.getLogger(__name__)

class Neo4jDocumentStore:
    """基于Neo4j的文档存储器，支持向量检索和图谱关系"""
    
    def __init__(
        self,
        uri: str,
        username: str,
        password: str,
        database: str = "neo4j",
        embedding_dim: int = 768,
        similarity_function: str = "cosine"
    ):
        """初始化Neo4j文档存储器"""
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.embedding_dim = embedding_dim
        self.similarity_function = similarity_function
        
        # 建立连接
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self._verify_connection()
        self._create_indexes()
        
        logger.info(f"Neo4j文档存储器初始化完成: {uri}")
    
    def _verify_connection(self):
        """验证数据库连接"""
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("Neo4j连接验证成功")
        except Exception as e:
            logger.error(f"Neo4j连接失败: {str(e)}")
            raise
    
    def _create_indexes(self):
        """创建必要的索引"""
        indexes = [
            # 文档ID索引
            "CREATE INDEX document_id_index IF NOT EXISTS FOR (d:Document) ON (d.id)",
            # 文档内容全文索引
            "CREATE FULLTEXT INDEX document_content_index IF NOT EXISTS FOR (d:Document) ON EACH [d.content]",
            # 元数据索引
            "CREATE INDEX document_title_index IF NOT EXISTS FOR (d:Document) ON (d.title)",
            "CREATE INDEX document_category_index IF NOT EXISTS FOR (d:Document) ON (d.category)",
            "CREATE INDEX document_author_index IF NOT EXISTS FOR (d:Document) ON (d.author)",
            "CREATE INDEX document_dynasty_index IF NOT EXISTS FOR (d:Document) ON (d.dynasty)",
            # 向量索引 (Neo4j 5.0+)
            f"CREATE VECTOR INDEX document_embedding_index IF NOT EXISTS FOR (d:Document) ON (d.embedding) OPTIONS {{indexConfig: {{`vector.dimensions`: {self.embedding_dim}, `vector.similarity_function`: '{self.similarity_function}'}}}}"
        ]
        
        with self.driver.session(database=self.database) as session:
            for index_query in indexes:
                try:
                    session.run(index_query)
                    logger.debug(f"索引创建成功: {index_query[:50]}...")
                except Exception as e:
                    logger.warning(f"索引创建失败: {str(e)}")
    
    def write_documents(
        self,
        documents: List[Document],
        policy: DuplicatePolicy = DuplicatePolicy.NONE
    ) -> int:
        """写入文档到Neo4j"""
        if not documents:
            return 0
        
        written_count = 0
        
        with self.driver.session(database=self.database) as session:
            for doc in documents:
                try:
                    # 准备文档数据
                    doc_data = self._prepare_document_data(doc)
                    
                    # 检查重复策略
                    if policy == DuplicatePolicy.SKIP:
                        existing = session.run(
                            "MATCH (d:Document {id: $id}) RETURN d.id",
                            id=doc_data["id"]
                        ).single()
                        if existing:
                            continue
                    
                    # 创建或更新文档节点
                    query = """
                    MERGE (d:Document {id: $id})
                    SET d += $properties
                    SET d.updated_at = datetime()
                    """
                    
                    session.run(query, id=doc_data["id"], properties=doc_data)
                    
                    # 创建关系
                    self._create_document_relationships(session, doc, doc_data)
                    
                    written_count += 1
                    
                except Exception as e:
                    logger.error(f"写入文档失败: {doc.id}, 错误: {str(e)}")
        
        logger.info(f"成功写入 {written_count} 个文档到Neo4j")
        return written_count
    
    def _prepare_document_data(self, doc: Document) -> Dict[str, Any]:
        """准备文档数据"""
        doc_data = {
            "id": doc.id or str(uuid.uuid4()),
            "content": doc.content,
            "created_at": datetime.now().isoformat()
        }
        
        # 添加元数据
        if doc.meta:
            for key, value in doc.meta.items():
                if key == "embedding" and isinstance(value, (list, np.ndarray)):
                    # 处理embedding向量
                    doc_data["embedding"] = list(value) if isinstance(value, np.ndarray) else value
                else:
                    doc_data[key] = value
        
        return doc_data
    
    def _create_document_relationships(self, session: Session, doc: Document, doc_data: Dict[str, Any]):
        """创建文档关系"""
        # 创建作者关系
        if "author" in doc_data:
            session.run("""
                MERGE (a:Author {name: $author})
                WITH a
                MATCH (d:Document {id: $doc_id})
                MERGE (a)-[:AUTHORED]->(d)
            """, author=doc_data["author"], doc_id=doc_data["id"])
        
        # 创建朝代关系
        if "dynasty" in doc_data:
            session.run("""
                MERGE (dy:Dynasty {name: $dynasty})
                WITH dy
                MATCH (d:Document {id: $doc_id})
                MERGE (d)-[:BELONGS_TO_DYNASTY]->(dy)
            """, dynasty=doc_data["dynasty"], doc_id=doc_data["id"])
        
        # 创建类别关系
        if "category" in doc_data:
            session.run("""
                MERGE (c:Category {name: $category})
                WITH c
                MATCH (d:Document {id: $doc_id})
                MERGE (d)-[:BELONGS_TO_CATEGORY]->(c)
            """, category=doc_data["category"], doc_id=doc_data["id"])
        
        # 创建书籍关系
        if "title" in doc_data:
            session.run("""
                MERGE (b:Book {title: $title})
                WITH b
                MATCH (d:Document {id: $doc_id})
                MERGE (d)-[:PART_OF_BOOK]->(b)
            """, title=doc_data["title"], doc_id=doc_data["id"])
    
    def filter_documents(self, filters: Optional[Dict[str, Any]] = None) -> List[Document]:
        """根据过滤条件查询文档"""
        if not filters:
            filters = {}
        
        # 构建查询条件
        where_clauses = []
        params = {}
        
        for key, value in filters.items():
            if key in ["title", "author", "dynasty", "category"]:
                where_clauses.append(f"d.{key} = ${key}")
                params[key] = value
            elif key == "content_contains":
                where_clauses.append("d.content CONTAINS $content_contains")
                params["content_contains"] = value
        
        where_clause = " AND ".join(where_clauses) if where_clauses else "true"
        
        query = f"""
        MATCH (d:Document)
        WHERE {where_clause}
        RETURN d.id as id, d.content as content, d {{.*}} as meta
        LIMIT 1000
        """
        
        documents = []
        with self.driver.session(database=self.database) as session:
            result = session.run(query, **params)
            for record in result:
                doc = Document(
                    id=record["id"],
                    content=record["content"],
                    meta=dict(record["meta"])
                )
                documents.append(doc)
        
        return documents
    
    def query_by_embedding(
        self,
        query_embedding: List[float],
        top_k: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """基于向量相似度查询文档"""
        # 构建过滤条件
        filter_clause = ""
        params = {"query_embedding": query_embedding, "top_k": top_k}
        
        if filters:
            filter_conditions = []
            for key, value in filters.items():
                if key in ["title", "author", "dynasty", "category"]:
                    filter_conditions.append(f"d.{key} = ${key}")
                    params[key] = value
            
            if filter_conditions:
                filter_clause = "WHERE " + " AND ".join(filter_conditions)
        
        # 使用向量索引查询
        query = f"""
        CALL db.index.vector.queryNodes('document_embedding_index', $top_k, $query_embedding)
        YIELD node as d, score
        {filter_clause}
        RETURN d.id as id, d.content as content, d {{.*}} as meta, score
        ORDER BY score DESC
        """
        
        documents = []
        with self.driver.session(database=self.database) as session:
            result = session.run(query, **params)
            for record in result:
                meta = dict(record["meta"])
                meta["score"] = record["score"]
                
                doc = Document(
                    id=record["id"],
                    content=record["content"],
                    meta=meta
                )
                documents.append(doc)
        
        return documents
    
    def get_document_count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """获取文档数量"""
        if not filters:
            query = "MATCH (d:Document) RETURN count(d) as count"
            params = {}
        else:
            where_clauses = []
            params = {}
            
            for key, value in filters.items():
                if key in ["title", "author", "dynasty", "category"]:
                    where_clauses.append(f"d.{key} = ${key}")
                    params[key] = value
            
            where_clause = " AND ".join(where_clauses) if where_clauses else "true"
            query = f"MATCH (d:Document) WHERE {where_clause} RETURN count(d) as count"
        
        with self.driver.session(database=self.database) as session:
            result = session.run(query, **params)
            return result.single()["count"]
    
    def delete_documents(self, document_ids: List[str]) -> None:
        """删除指定文档"""
        if not document_ids:
            return
        
        query = """
        MATCH (d:Document)
        WHERE d.id IN $document_ids
        DETACH DELETE d
        """
        
        with self.driver.session(database=self.database) as session:
            session.run(query, document_ids=document_ids)
        
        logger.info(f"删除了 {len(document_ids)} 个文档")
    
    def get_related_documents(
        self,
        document_id: str,
        relation_types: Optional[List[str]] = None,
        max_depth: int = 2
    ) -> List[Document]:
        """获取相关文档（基于图谱关系）"""
        if relation_types is None:
            relation_types = ["AUTHORED", "BELONGS_TO_CATEGORY", "BELONGS_TO_DYNASTY", "PART_OF_BOOK"]
        
        # 构建关系类型过滤
        relation_filter = "|".join(relation_types)
        
        query = f"""
        MATCH (d1:Document {{id: $document_id}})
        MATCH (d1)-[:{relation_filter}*1..{max_depth}]-(d2:Document)
        WHERE d1.id <> d2.id
        RETURN DISTINCT d2.id as id, d2.content as content, d2 {{.*}} as meta
        LIMIT 50
        """
        
        documents = []
        with self.driver.session(database=self.database) as session:
            result = session.run(query, document_id=document_id)
            for record in result:
                doc = Document(
                    id=record["id"],
                    content=record["content"],
                    meta=dict(record["meta"])
                )
                documents.append(doc)
        
        return documents
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j连接已关闭")
    
    def __del__(self):
        """析构函数"""
        self.close()
