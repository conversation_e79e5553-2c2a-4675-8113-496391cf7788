#!/usr/bin/env python3
"""
天权-Haystack 文档摄取管道
用于处理术数典籍文档，构建知识图谱和向量索引
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import yaml
import click
from rich.console import Console
from rich.progress import Progress, TaskID

# Haystack imports
from haystack import Pipeline, Document
from haystack.components.converters import TextFileToDocument
from haystack.components.preprocessors import DocumentCleaner, DocumentSplitter
from haystack.components.embedders import SentenceTransformersDocumentEmbedder
from haystack.components.writers import DocumentWriter

# 自定义组件
from core.document_stores.neo4j_store import Neo4jDocumentStore
from core.preprocessors.chinese_preprocessor import ChineseDocumentPreprocessor
from core.converters.ancient_text_converter import AncientTextConverter
from utils.config_loader import load_config
from utils.logger import setup_logger

console = Console()
logger = setup_logger(__name__)

class TianquanIngestPipeline:
    """天权文档摄取管道"""
    
    def __init__(self, config_path: str = "config/haystack.yml"):
        """初始化摄取管道"""
        self.config = load_config(config_path)
        self.pipeline = None
        self.document_store = None
        self._setup_document_store()
        self._build_pipeline()
    
    def _setup_document_store(self):
        """设置文档存储"""
        neo4j_config = self.config['databases']['neo4j']
        self.document_store = Neo4jDocumentStore(
            uri=neo4j_config['uri'],
            username=neo4j_config['username'],
            password=neo4j_config['password'],
            database=neo4j_config['database']
        )
        logger.info("Neo4j文档存储初始化完成")
    
    def _build_pipeline(self):
        """构建摄取管道"""
        # 获取embedding模型配置
        embedding_config = self.config['models']['chinese_embedding']
        
        # 创建管道组件
        components = {
            "converter": AncientTextConverter(),
            "cleaner": DocumentCleaner(
                clean_empty_lines=True,
                clean_whitespace=True,
                clean_header_footer=True
            ),
            "preprocessor": ChineseDocumentPreprocessor(
                tokenizer_config=self.config['document_processing']['tokenization']
            ),
            "splitter": DocumentSplitter(
                split_by="sentence",
                split_length=200,
                split_overlap=50
            ),
            "embedder": SentenceTransformersDocumentEmbedder(
                model=embedding_config['model_name'],
                device=embedding_config['device'],
                batch_size=embedding_config['batch_size']
            ),
            "writer": DocumentWriter(document_store=self.document_store)
        }
        
        # 构建管道
        self.pipeline = Pipeline()
        for name, component in components.items():
            self.pipeline.add_component(name, component)
        
        # 连接组件
        self.pipeline.connect("converter", "cleaner")
        self.pipeline.connect("cleaner", "preprocessor")
        self.pipeline.connect("preprocessor", "splitter")
        self.pipeline.connect("splitter", "embedder")
        self.pipeline.connect("embedder", "writer")
        
        logger.info("摄取管道构建完成")
    
    def ingest_file(self, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """摄取单个文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 准备元数据
            if metadata is None:
                metadata = {}
            
            metadata.update({
                "source_file": str(file_path),
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "document_type": "ancient_text"
            })
            
            # 运行管道
            logger.info(f"开始摄取文件: {file_path}")
            result = self.pipeline.run({
                "converter": {"sources": [file_path]},
                "writer": {"meta": metadata}
            })
            
            # 统计结果
            documents_written = len(result.get("writer", {}).get("documents_written", []))
            logger.info(f"文件摄取完成: {file_path}, 生成文档片段: {documents_written}")
            
            return {
                "status": "success",
                "file_path": str(file_path),
                "documents_count": documents_written,
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"文件摄取失败: {file_path}, 错误: {str(e)}")
            return {
                "status": "error",
                "file_path": str(file_path),
                "error": str(e)
            }
    
    def ingest_directory(self, directory_path: str, pattern: str = "*.txt") -> List[Dict[str, Any]]:
        """摄取目录中的所有文件"""
        directory_path = Path(directory_path)
        if not directory_path.exists():
            raise FileNotFoundError(f"目录不存在: {directory_path}")
        
        # 查找匹配的文件
        files = list(directory_path.glob(pattern))
        if not files:
            logger.warning(f"目录中没有找到匹配的文件: {directory_path}/{pattern}")
            return []
        
        results = []
        
        with Progress() as progress:
            task = progress.add_task(f"摄取文件", total=len(files))
            
            for file_path in files:
                # 从文件名推断元数据
                metadata = self._extract_metadata_from_filename(file_path.name)
                
                # 摄取文件
                result = self.ingest_file(file_path, metadata)
                results.append(result)
                
                progress.update(task, advance=1)
        
        # 统计总结果
        success_count = sum(1 for r in results if r["status"] == "success")
        total_documents = sum(r.get("documents_count", 0) for r in results if r["status"] == "success")
        
        logger.info(f"目录摄取完成: {directory_path}")
        logger.info(f"成功文件: {success_count}/{len(files)}, 总文档片段: {total_documents}")
        
        return results
    
    def _extract_metadata_from_filename(self, filename: str) -> Dict[str, Any]:
        """从文件名提取元数据"""
        metadata = {}
        
        # 解析文件名格式: 书名-朝代-作者.txt
        name_parts = filename.replace('.txt', '').split('-')
        
        if len(name_parts) >= 1:
            metadata['title'] = name_parts[0]
        if len(name_parts) >= 2:
            metadata['dynasty'] = name_parts[1]
        if len(name_parts) >= 3:
            metadata['author'] = name_parts[2]
        
        # 根据书名判断类别
        title = metadata.get('title', '')
        if any(keyword in title for keyword in ['易', '卦', '爻']):
            metadata['category'] = '易学'
        elif any(keyword in title for keyword in ['六壬', '壬']):
            metadata['category'] = '六壬'
        elif any(keyword in title for keyword in ['奇门', '遁甲']):
            metadata['category'] = '奇门'
        elif any(keyword in title for keyword in ['太乙', '太一']):
            metadata['category'] = '太乙'
        elif any(keyword in title for keyword in ['相', '面相', '手相']):
            metadata['category'] = '相术'
        elif any(keyword in title for keyword in ['风水', '地理', '堪舆']):
            metadata['category'] = '风水'
        else:
            metadata['category'] = '其他'
        
        return metadata
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取摄取统计信息"""
        try:
            stats = self.document_store.get_document_count()
            return {
                "total_documents": stats,
                "document_store_type": "Neo4j",
                "status": "active"
            }
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {
                "error": str(e),
                "status": "error"
            }

@click.command()
@click.option('--source', '-s', required=True, help='源文件或目录路径')
@click.option('--pattern', '-p', default='*.txt', help='文件匹配模式 (仅目录模式)')
@click.option('--config', '-c', default='config/haystack.yml', help='配置文件路径')
@click.option('--metadata', '-m', help='额外元数据 (JSON格式)')
def main(source: str, pattern: str, config: str, metadata: Optional[str]):
    """天权-Haystack 文档摄取工具"""
    
    console.print("[bold blue]天权-Haystack 文档摄取工具[/bold blue]")
    console.print(f"源路径: {source}")
    console.print(f"配置文件: {config}")
    
    try:
        # 初始化摄取管道
        ingest_pipeline = TianquanIngestPipeline(config)
        
        # 解析额外元数据
        extra_metadata = {}
        if metadata:
            import json
            extra_metadata = json.loads(metadata)
        
        # 判断是文件还是目录
        source_path = Path(source)
        if source_path.is_file():
            # 摄取单个文件
            result = ingest_pipeline.ingest_file(source, extra_metadata)
            if result["status"] == "success":
                console.print(f"[green]✓[/green] 文件摄取成功: {result['documents_count']} 个文档片段")
            else:
                console.print(f"[red]✗[/red] 文件摄取失败: {result['error']}")
                sys.exit(1)
        
        elif source_path.is_dir():
            # 摄取目录
            results = ingest_pipeline.ingest_directory(source, pattern)
            success_count = sum(1 for r in results if r["status"] == "success")
            total_documents = sum(r.get("documents_count", 0) for r in results if r["status"] == "success")
            
            console.print(f"[green]✓[/green] 目录摄取完成")
            console.print(f"成功文件: {success_count}/{len(results)}")
            console.print(f"总文档片段: {total_documents}")
            
            # 显示失败的文件
            failed_files = [r for r in results if r["status"] == "error"]
            if failed_files:
                console.print("\n[red]失败的文件:[/red]")
                for failed in failed_files:
                    console.print(f"  - {failed['file_path']}: {failed['error']}")
        
        else:
            console.print(f"[red]✗[/red] 路径不存在: {source}")
            sys.exit(1)
        
        # 显示统计信息
        stats = ingest_pipeline.get_statistics()
        console.print(f"\n[bold]数据库统计:[/bold]")
        console.print(f"总文档数: {stats.get('total_documents', 'N/A')}")
        
    except Exception as e:
        console.print(f"[red]✗[/red] 摄取过程出错: {str(e)}")
        logger.exception("摄取过程异常")
        sys.exit(1)

if __name__ == "__main__":
    main()
