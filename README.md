# 六行星知识图谱项目

🏛️ **中华传统术数数字化平台**

[![Deploy to HF Spaces](https://github.com/your-username/5-planets/actions/workflows/deploy-to-huggingface.yml/badge.svg)](https://github.com/your-username/5-planets/actions/workflows/deploy-to-huggingface.yml)
[![Streamlit App](https://static.streamlit.io/badges/streamlit_badge_black_white.svg)](https://huggingface.co/spaces/your-username/sixstars-streamlit)

## 🌟 项目概述

六行星知识图谱项目是一个综合性的中华传统术数数字化平台，结合了：

- **📚 古籍知识图谱**: 200+本术数典籍 + 二十四史
- **🔮 术数排盘系统**: 六壬、奇门、太乙、易经、梅花易数
- **🤖 AI古代大师**: 基于真实历史数据的Gemini AI对话

## 📊 项目架构

```
5-planets/
├── shushu/                    # Streamlit排盘应用
│   ├── app.py                # 主界面
│   ├── kinliuren/            # 六壬算法
│   ├── kinqimen/             # 奇门算法
│   └── kintaiyi/             # 太乙算法
├── src/                      # 数据处理和分析
│   ├── analyzers/            # 数据分析器
│   └── processors/           # 数据处理器
├── tianquan-haystack/        # Gemini AI集成
├── nuc12-setup/              # NUC12服务器配置
└── .github/workflows/        # GitHub Actions配置
```

## 🚀 在线演示

- **[排盘系统](https://huggingface.co/spaces/your-username/sixstars-streamlit)**: Streamlit排盘应用
- **[AI古代大师](https://huggingface.co/spaces/your-username/sixstars-demo)**: Gemini AI对话系统

## 🔧 本地安装

### 前提条件

- Python 3.9+
- Neo4j数据库
- Docker (可选)

### 安装步骤

```bash
# 克隆仓库
git clone https://github.com/your-username/5-planets.git
cd 5-planets

# 安装依赖
pip install -r requirements.txt

# 启动Streamlit应用
cd shushu
streamlit run app.py
```

### Docker部署

```bash
# 使用Docker Compose启动所有服务
cd nuc12-setup
./start-all-services.sh
```

## 📚 数据来源

- **前四史**: 史记、汉书、后汉书、三国志
- **二十四史**: 完整的中国正史
- **术数典籍**: 200+本古代术数书籍

## 🤖 AI集成

项目集成了多种AI模型：

- **Gemini AI**: 古代学者角色扮演
- **Neo4j图谱**: 人物关系网络分析
- **自然语言处理**: 古籍文本分析

## 🏆 项目特色

- **真实数据**: 基于原始古籍的知识图谱
- **完整算法**: 正统的术数排盘算法
- **AI增强**: 智能解读和历史分析
- **开源共享**: 促进传统文化传播

## 📝 使用说明

### 排盘系统

1. 选择排盘类型 (六壬/奇门/太乙/易经)
2. 输入时间信息
3. 获取排盘结果和解读

### AI古代大师

1. 选择古代学者角色
2. 输入问题
3. 获取基于真实历史数据的回答

## 🛠️ 开发指南

### 环境设置

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements-dev.txt
```

### 代码贡献

1. Fork仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- 感谢所有为中华传统文化数字化做出贡献的学者和开发者
- 特别感谢kinliuren、kinqimen、kintaiyi等开源项目的贡献者

---

**六行星知识图谱** - 传承古代智慧，连接现代科技
# 5-planets
# 5-planets
