# 部署指南 - GitHub到Hugging Face自动部署

## 🎯 部署目标

将六行星知识图谱项目自动部署到Hugging Face Spaces，包括：
1. **Streamlit排盘应用** - 完整的术数排盘系统
2. **Gemini AI演示** - 基于真实历史数据的AI对话

## 📋 部署前准备

### 1. GitHub仓库设置

```bash
# 1. 创建GitHub仓库
# 在GitHub上创建新仓库: 5-planets

# 2. 推送代码到GitHub
git init
git add .
git commit -m "Initial commit: 六行星知识图谱项目"
git branch -M main
git remote add origin https://github.com/your-username/5-planets.git
git push -u origin main
```

### 2. Hugging Face账号设置

```bash
# 1. 注册Hugging Face账号
# 访问: https://huggingface.co/join

# 2. 创建API Token
# 访问: https://huggingface.co/settings/tokens
# 创建一个Write权限的Token

# 3. 创建两个Spaces
# Space 1: sixstars-streamlit (Streamlit排盘应用)
# Space 2: sixstars-demo (Gemini AI演示)
```

### 3. GitHub Secrets配置

在GitHub仓库设置中添加以下Secrets：

| Secret名称 | 值 | 说明 |
|-----------|----|----|
| `HF_TOKEN` | `hf_xxxxxxxxxx` | Hugging Face API Token |
| `HF_SPACE_NAME` | `your-username/sixstars-streamlit` | Streamlit应用Space名称 |
| `HF_DEMO_SPACE_NAME` | `your-username/sixstars-demo` | Gemini演示Space名称 |
| `GEMINI_API_KEY` | `AIzaSyxxxxxxxxxx` | Gemini API密钥 (可选) |

## 🚀 自动部署流程

### 触发条件

GitHub Actions会在以下情况自动触发部署：

1. **推送到main分支**
2. **修改shushu/或tianquan-haystack/目录**
3. **手动触发工作流**

### 部署步骤

```mermaid
graph TD
    A[推送代码到GitHub] --> B[触发GitHub Actions]
    B --> C[准备Streamlit应用]
    C --> D[部署到HF Spaces - Streamlit]
    D --> E[准备Gemini演示]
    E --> F[部署到HF Spaces - Demo]
    F --> G[部署完成]
```

### 手动触发部署

1. 在GitHub仓库页面，点击"Actions"
2. 选择"Deploy to Hugging Face Spaces"
3. 点击"Run workflow"
4. 选择分支，点击"Run workflow"

## 📱 部署后访问

部署成功后，可以通过以下地址访问：

### Streamlit排盘应用
- **URL**: `https://huggingface.co/spaces/your-username/sixstars-streamlit`
- **功能**: 完整的术数排盘系统
- **包含**: 六壬、奇门、太乙、易经、梅花易数

### Gemini AI演示
- **URL**: `https://huggingface.co/spaces/your-username/sixstars-demo`
- **功能**: AI古代学者对话
- **特色**: 基于真实历史数据的智能问答

## 🔧 自定义配置

### 修改应用配置

编辑`.github/workflows/deploy-to-huggingface.yml`文件：

```yaml
# 修改应用标题
title: 你的应用标题

# 修改应用描述
short_description: 你的应用描述

# 修改应用图标
emoji: 🎯

# 修改颜色主题
colorFrom: blue
colorTo: purple
```

### 添加环境变量

在Hugging Face Spaces设置中添加环境变量：

1. 进入Space设置页面
2. 点击"Settings"
3. 在"Repository secrets"中添加环境变量

## 🛠️ 故障排除

### 常见问题

#### 1. 部署失败 - Token无效
```
Error: Invalid token
```
**解决方案**: 检查HF_TOKEN是否正确设置

#### 2. 部署失败 - Space不存在
```
Error: Repository not found
```
**解决方案**: 确认Space名称正确，格式为`username/space-name`

#### 3. 应用启动失败 - 依赖错误
```
Error: No module named 'xxx'
```
**解决方案**: 检查requirements.txt中的依赖包

#### 4. 应用运行错误 - API密钥
```
Error: API key not found
```
**解决方案**: 在Space设置中添加相应的API密钥

### 调试方法

1. **查看GitHub Actions日志**
   - 在Actions页面查看详细的部署日志
   - 检查每个步骤的执行结果

2. **查看Hugging Face Spaces日志**
   - 在Space页面点击"Logs"
   - 查看应用启动和运行日志

3. **本地测试**
   ```bash
   # 本地运行Streamlit应用
   cd shushu
   streamlit run app.py
   
   # 本地测试Gemini演示
   cd tianquan-haystack
   python demo_app.py
   ```

## 📊 监控和维护

### 自动更新

- 每次推送代码到main分支都会自动部署
- 建议在本地充分测试后再推送

### 手动维护

```bash
# 更新依赖包
pip install --upgrade streamlit kinliuren kinqimen kintaiyi

# 重新部署
git add .
git commit -m "Update dependencies"
git push origin main
```

### 性能监控

- 在Hugging Face Spaces页面查看使用统计
- 监控应用响应时间和错误率
- 根据用户反馈优化功能

## 🎉 部署成功标志

部署成功后，你将拥有：

1. ✅ **在线排盘系统** - 随时随地进行术数排盘
2. ✅ **AI古代大师** - 基于真实历史数据的智能对话
3. ✅ **自动化部署** - 代码更新自动同步到线上
4. ✅ **全球访问** - 通过Hugging Face平台全球可访问

---

**恭喜！你的六行星知识图谱项目现在可以在全世界范围内访问了！** 🌍✨
