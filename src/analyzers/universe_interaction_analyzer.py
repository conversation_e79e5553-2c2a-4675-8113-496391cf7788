#!/usr/bin/env python3
"""
六行星知识图谱 - 两大宇宙连接与互动分析器
回答关于正史宇宙与术数宇宙连接互动的9个核心问题
"""

from neo4j import GraphDatabase
import os
import json
import re
from collections import defaultdict, Counter
from datetime import datetime

class UniverseInteractionAnalyzer:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到六行星知识图谱")
        print(f"🌌 启动两大宇宙连接与互动分析")
        print(f"🎯 目标：找到连接正史与术数的所有虫洞")
        
        # 定义重要的历史事件和时间
        self.major_historical_events = {
            '王莽篡汉': {'time': '公元9年', 'keywords': ['王莽', '新朝', '篡汉']},
            '党锢之祸': {'time': '公元166-184年', 'keywords': ['党锢', '宦官', '清流']},
            '黄巾起义': {'time': '公元184年', 'keywords': ['黄巾', '张角', '太平道']},
            '董卓之乱': {'time': '公元189-192年', 'keywords': ['董卓', '长安', '洛阳']}
        }
        
        # 定义重要地理节点
        self.important_locations = [
            '长安', '洛阳', '东海', '西海', '南海', '北海',
            '泰山', '华山', '嵩山', '恒山', '衡山',
            '黄河', '长江', '淮河', '渭河'
        ]
        
        # 定义术数核心概念
        self.shushu_core_concepts = [
            '五德终始', '谶纬', '天人感应', '阴阳五行',
            '奇门遁甲', '六壬', '太乙', '八卦', '易经'
        ]
    
    def close(self):
        self.driver.close()
    
    def find_cross_universe_persons(self):
        """问题10: 寻找跨界者名单"""
        print("\n👥 === 问题10: 跨界者名单分析 ===")
        
        with self.driver.session() as session:
            # 查找同时出现在正史和术数中的人物
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL AND size(p.books) > 0
                WITH p,
                     [book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志']] as zhengshi_books,
                     [book IN p.books WHERE NOT book IN ['史记', '汉书', '后汉书', '三国志']] as shushu_books
                WHERE size(zhengshi_books) > 0 AND size(shushu_books) > 0
                RETURN p.name as person_name,
                       zhengshi_books,
                       shushu_books,
                       size(zhengshi_books) + size(shushu_books) as total_books
                ORDER BY total_books DESC
            """)
            
            cross_universe_persons = []
            for record in result:
                cross_universe_persons.append({
                    'name': record['person_name'],
                    'zhengshi_books': record['zhengshi_books'],
                    'shushu_books': record['shushu_books'],
                    'total_books': record['total_books']
                })
            
            # 统计总人数和重叠比例
            total_persons_result = session.run("MATCH (p:Person) RETURN count(p) as total")
            total_persons = total_persons_result.single()['total']
            
            overlap_ratio = len(cross_universe_persons) / total_persons if total_persons > 0 else 0
            
            print(f"📊 跨界者统计:")
            print(f"   👥 总人物数: {total_persons}")
            print(f"   🌉 跨界者数: {len(cross_universe_persons)}")
            print(f"   📈 重叠比例: {overlap_ratio:.2%}")
            
            print(f"\n🌟 主要跨界者:")
            for person in cross_universe_persons[:10]:
                zhengshi_str = ', '.join(person['zhengshi_books'])
                shushu_str = ', '.join(person['shushu_books'][:2])
                print(f"   🔸 {person['name']}")
                print(f"      正史记录: {zhengshi_str}")
                print(f"      术数记录: {shushu_str}")
                print()
            
            return cross_universe_persons, overlap_ratio
    
    def analyze_temporal_resonance(self):
        """问题11: 分析时间共振现象"""
        print("\n⏰ === 问题11: 时间共振分析 ===")
        
        with self.driver.session() as session:
            resonance_analysis = {}
            
            for event_name, event_info in self.major_historical_events.items():
                print(f"\n🔍 分析事件: {event_name} ({event_info['time']})")
                
                # 在正史中查找相关记录
                zhengshi_mentions = 0
                for keyword in event_info['keywords']:
                    result = session.run("""
                        MATCH (p:Person)
                        WHERE any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                        AND p.name CONTAINS $keyword
                        RETURN count(p) as mentions
                    """, keyword=keyword)
                    
                    mentions = result.single()['mentions']
                    zhengshi_mentions += mentions
                
                # 在术数典籍中查找相关记录
                shushu_mentions = 0
                for keyword in event_info['keywords']:
                    result = session.run("""
                        MATCH (m:ShushuMethod)
                        WHERE m.name CONTAINS $keyword
                        RETURN count(m) as mentions
                    """, keyword=keyword)
                    
                    mentions = result.single()['mentions']
                    shushu_mentions += mentions
                
                resonance_score = min(zhengshi_mentions, shushu_mentions) / max(zhengshi_mentions + shushu_mentions, 1)
                
                resonance_analysis[event_name] = {
                    'time': event_info['time'],
                    'zhengshi_mentions': zhengshi_mentions,
                    'shushu_mentions': shushu_mentions,
                    'resonance_score': resonance_score
                }
                
                print(f"   📚 正史提及: {zhengshi_mentions}次")
                print(f"   🔮 术数提及: {shushu_mentions}次")
                print(f"   📊 共振指数: {resonance_score:.3f}")
            
            return resonance_analysis
    
    def analyze_geographical_overlap(self):
        """问题12: 分析地理重叠"""
        print("\n🗺️ === 问题12: 地理重叠分析 ===")
        
        with self.driver.session() as session:
            geographical_analysis = {}
            
            for location in self.important_locations:
                print(f"\n📍 分析地点: {location}")
                
                # 在正史中的重要性
                result = session.run("""
                    MATCH (l:Location)
                    WHERE any(book IN l.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                    AND l.name CONTAINS $location
                    RETURN count(l) as zhengshi_importance
                """, location=location)
                
                zhengshi_importance = result.single()['zhengshi_importance']
                
                # 在术数（特别是风水）中的重要性
                result = session.run("""
                    MATCH (m:ShushuMethod)
                    WHERE (m.name CONTAINS $location OR m.name CONTAINS '风水' OR m.name CONTAINS '堪舆')
                    AND m.name CONTAINS $location
                    RETURN count(m) as shushu_importance
                """, location=location)
                
                shushu_importance = result.single()['shushu_importance']
                
                # 查找风水相关的特殊意义
                result = session.run("""
                    MATCH (c:ShushuConcept)
                    WHERE (c.name CONTAINS '风水' OR c.name CONTAINS '堪舆')
                    AND c.name CONTAINS $location
                    RETURN count(c) as fengshui_significance
                """, location=location)
                
                fengshui_significance = result.single()['fengshui_significance']
                
                geographical_analysis[location] = {
                    'zhengshi_importance': zhengshi_importance,
                    'shushu_importance': shushu_importance,
                    'fengshui_significance': fengshui_significance,
                    'total_significance': zhengshi_importance + shushu_importance + fengshui_significance
                }
                
                print(f"   🏛️ 正史重要性: {zhengshi_importance}")
                print(f"   🔮 术数重要性: {shushu_importance}")
                print(f"   🌬️ 风水意义: {fengshui_significance}")
            
            # 排序并显示最重要的地理重叠
            sorted_locations = sorted(geographical_analysis.items(), 
                                    key=lambda x: x[1]['total_significance'], 
                                    reverse=True)
            
            print(f"\n🏆 地理重叠重要性排名:")
            for location, data in sorted_locations[:5]:
                if data['total_significance'] > 0:
                    print(f"   🔸 {location}: 总重要性 {data['total_significance']}")
            
            return geographical_analysis
    
    def find_shushu_concepts_in_zhengshi(self):
        """问题13: 在正史中搜索术数概念"""
        print("\n🔍 === 问题13: 正史中的术数引用分析 ===")
        
        with self.driver.session() as session:
            concept_usage = {}
            
            for concept in self.shushu_core_concepts:
                print(f"\n🎯 搜索概念: {concept}")
                
                # 在正史人物名字中搜索
                result = session.run("""
                    MATCH (p:Person)
                    WHERE any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                    AND (p.name CONTAINS $concept OR p.source CONTAINS $concept)
                    RETURN p.name as person, p.books as books, p.source as source
                    LIMIT 10
                """, concept=concept)
                
                usage_records = []
                for record in result:
                    usage_records.append({
                        'person': record['person'],
                        'books': record['books'],
                        'context': record['source']
                    })
                
                # 在正史事件中搜索
                result = session.run("""
                    MATCH (e:Event)
                    WHERE any(book IN e.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                    AND e.name CONTAINS $concept
                    RETURN e.name as event, e.books as books
                    LIMIT 5
                """, concept=concept)
                
                event_records = []
                for record in result:
                    event_records.append({
                        'event': record['event'],
                        'books': record['books']
                    })
                
                concept_usage[concept] = {
                    'person_usage': usage_records,
                    'event_usage': event_records,
                    'total_usage': len(usage_records) + len(event_records)
                }
                
                print(f"   👤 人物相关: {len(usage_records)}条")
                print(f"   ⚔️ 事件相关: {len(event_records)}条")
                
                if usage_records:
                    for record in usage_records[:3]:
                        books_str = ', '.join(record['books']) if record['books'] else '无'
                        print(f"      - {record['person']} ({books_str})")
            
            return concept_usage
    
    def analyze_cross_universe_reputation(self, cross_universe_persons):
        """问题14: 分析术数人物的历史声望"""
        print("\n🏆 === 问题14: 跨界者历史声望分析 ===")
        
        reputation_analysis = {}
        
        # 简单的声望评估（基于出现频率和书籍数量）
        for person in cross_universe_persons[:10]:
            name = person['name']
            zhengshi_books = person['zhengshi_books']
            shushu_books = person['shushu_books']
            
            # 计算正史声望（基于出现在多少正史中）
            zhengshi_reputation = len(zhengshi_books)
            
            # 计算术数地位（基于出现在多少术数典籍中）
            shushu_status = len(shushu_books)
            
            # 简单的声望分类
            if zhengshi_reputation >= 3:
                zhengshi_rating = "高声望"
            elif zhengshi_reputation >= 2:
                zhengshi_rating = "中等声望"
            else:
                zhengshi_rating = "一般声望"
            
            if shushu_status >= 3:
                shushu_rating = "术数大师"
            elif shushu_status >= 2:
                shushu_rating = "术数专家"
            else:
                shushu_rating = "术数学者"
            
            reputation_analysis[name] = {
                'zhengshi_reputation': zhengshi_reputation,
                'shushu_status': shushu_status,
                'zhengshi_rating': zhengshi_rating,
                'shushu_rating': shushu_rating,
                'correlation': abs(zhengshi_reputation - shushu_status)
            }
            
            print(f"🔸 {name}:")
            print(f"   📚 正史声望: {zhengshi_rating} ({zhengshi_reputation})")
            print(f"   🔮 术数地位: {shushu_rating} ({shushu_status})")
            print(f"   🔗 关联度: {reputation_analysis[name]['correlation']}")
            print()
        
        return reputation_analysis
    
    def analyze_wuxing_zhi_connections(self):
        """问题15: 分析五行志的暗物质显现"""
        print("\n🌟 === 问题15: 五行志暗物质显现分析 ===")
        
        with self.driver.session() as session:
            # 查找五行相关的术数方法
            result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE m.name CONTAINS '五行' OR m.name CONTAINS '灾' OR m.name CONTAINS '异'
                OR m.name CONTAINS '天' OR m.name CONTAINS '象'
                RETURN m.name as method, m.books as books
                LIMIT 20
            """)
            
            wuxing_methods = []
            for record in result:
                wuxing_methods.append({
                    'method': record['method'],
                    'books': record['books']
                })
            
            # 查找五行概念
            result = session.run("""
                MATCH (c:ShushuConcept)
                WHERE c.name CONTAINS '五行' OR c.name CONTAINS '天人感应'
                RETURN c.name as concept, c.books as books
            """)
            
            wuxing_concepts = []
            for record in result:
                wuxing_concepts.append({
                    'concept': record['concept'],
                    'books': record['books']
                })
            
            print(f"📊 五行志相关分析:")
            print(f"   🔮 五行相关方法: {len(wuxing_methods)}个")
            print(f"   💡 五行相关概念: {len(wuxing_concepts)}个")
            
            print(f"\n🌟 主要五行方法:")
            for method in wuxing_methods[:5]:
                books_str = ', '.join(method['books'][:2]) if method['books'] else '无'
                print(f"   - {method['method']} ({books_str})")
            
            print(f"\n💡 核心五行概念:")
            for concept in wuxing_concepts[:5]:
                books_str = ', '.join(concept['books'][:2]) if concept['books'] else '无'
                print(f"   - {concept['concept']} ({books_str})")
            
            return wuxing_methods, wuxing_concepts
    
    def find_knowledge_conflicts(self):
        """问题16: 寻找两种知识的冲突"""
        print("\n⚔️ === 问题16: 知识冲突分析 ===")
        
        with self.driver.session() as session:
            # 查找可能的批判或否定记录
            conflict_keywords = ['批', '驳', '斥', '非', '谬', '妄', '伪', '惑']
            
            conflicts = []
            for keyword in conflict_keywords:
                result = session.run("""
                    MATCH (p:Person)
                    WHERE any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                    AND p.name CONTAINS $keyword
                    RETURN p.name as person, p.books as books
                    LIMIT 5
                """, keyword=keyword)
                
                for record in result:
                    conflicts.append({
                        'person': record['person'],
                        'books': record['books'],
                        'conflict_type': keyword
                    })
            
            print(f"📊 知识冲突分析:")
            print(f"   ⚔️ 发现潜在冲突: {len(conflicts)}个")
            
            if conflicts:
                print(f"\n🔍 主要冲突记录:")
                for conflict in conflicts[:5]:
                    books_str = ', '.join(conflict['books']) if conflict['books'] else '无'
                    print(f"   - {conflict['person']} (类型: {conflict['conflict_type']}, 来源: {books_str})")
            else:
                print(f"   ✅ 未发现明显的知识冲突记录")
            
            return conflicts
    
    def classify_institutional_vs_folk(self, cross_universe_persons):
        """问题17: 分类建制派与民间派"""
        print("\n🏛️ === 问题17: 建制派与民间派分析 ===")
        
        # 基于人物名字中的官职标识进行简单分类
        institutional_keywords = ['帝', '王', '公', '侯', '伯', '子', '男', '太', '丞', '令', '守']
        folk_keywords = ['子', '道人', '真人', '仙人', '师', '先生']
        
        institutional_persons = []
        folk_persons = []
        unclear_persons = []
        
        for person in cross_universe_persons:
            name = person['name']
            
            is_institutional = any(keyword in name for keyword in institutional_keywords)
            is_folk = any(keyword in name for keyword in folk_keywords)
            
            if is_institutional and not is_folk:
                institutional_persons.append(person)
            elif is_folk and not is_institutional:
                folk_persons.append(person)
            else:
                unclear_persons.append(person)
        
        print(f"📊 建制派与民间派分类:")
        print(f"   🏛️ 建制派: {len(institutional_persons)}人")
        print(f"   🏠 民间派: {len(folk_persons)}人")
        print(f"   ❓ 不明确: {len(unclear_persons)}人")
        
        print(f"\n🏛️ 主要建制派人物:")
        for person in institutional_persons[:5]:
            zhengshi_str = ', '.join(person['zhengshi_books'])
            print(f"   - {person['name']} ({zhengshi_str})")
        
        print(f"\n🏠 主要民间派人物:")
        for person in folk_persons[:5]:
            shushu_str = ', '.join(person['shushu_books'][:2])
            print(f"   - {person['name']} ({shushu_str})")
        
        return institutional_persons, folk_persons, unclear_persons
    
    def analyze_cezi_social_significance(self):
        """问题18: 分析测字秘牒的社会学意义"""
        print("\n📝 === 问题18: 测字秘牒社会学分析 ===")
        
        with self.driver.session() as session:
            # 获取测字秘牒中的所有方法
            result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(book IN m.books WHERE book CONTAINS '测字秘牒')
                RETURN m.name as method
            """)
            
            cezi_methods = [record['method'] for record in result]
            
            # 分析民众关心的问题类型
            concern_categories = {
                '求官问仕': ['官', '仕', '职', '位', '禄', '升'],
                '财富经商': ['财', '钱', '富', '贫', '商', '利'],
                '婚姻家庭': ['婚', '嫁', '妻', '夫', '子', '女'],
                '健康疾病': ['病', '疾', '健', '康', '医', '药'],
                '出行安全': ['行', '路', '归', '来', '去', '安'],
                '寻人找物': ['寻', '找', '失', '得', '见', '人']
            }
            
            concern_analysis = {}
            for category, keywords in concern_categories.items():
                count = 0
                examples = []
                
                for method in cezi_methods:
                    for keyword in keywords:
                        if keyword in method:
                            count += 1
                            if len(examples) < 3:
                                examples.append(method)
                            break
                
                concern_analysis[category] = {
                    'count': count,
                    'examples': examples,
                    'percentage': count / len(cezi_methods) * 100 if cezi_methods else 0
                }
            
            print(f"📊 测字秘牒社会学分析 (总计{len(cezi_methods)}个方法):")
            
            # 按关注度排序
            sorted_concerns = sorted(concern_analysis.items(), 
                                   key=lambda x: x[1]['count'], 
                                   reverse=True)
            
            for category, data in sorted_concerns:
                print(f"\n🎯 {category}: {data['count']}个方法 ({data['percentage']:.1f}%)")
                for example in data['examples']:
                    print(f"   - {example}")
            
            return concern_analysis

def main():
    """主函数"""
    analyzer = UniverseInteractionAnalyzer()
    
    try:
        print("🌟 开始两大宇宙连接与互动分析...")
        
        # 回答9个核心问题
        results = {}
        
        # 问题10-18
        cross_universe_persons, overlap_ratio = analyzer.find_cross_universe_persons()
        results['cross_universe_persons'] = {'persons': cross_universe_persons, 'overlap_ratio': overlap_ratio}
        
        results['temporal_resonance'] = analyzer.analyze_temporal_resonance()
        results['geographical_overlap'] = analyzer.analyze_geographical_overlap()
        results['shushu_in_zhengshi'] = analyzer.find_shushu_concepts_in_zhengshi()
        results['reputation_analysis'] = analyzer.analyze_cross_universe_reputation(cross_universe_persons)
        results['wuxing_connections'] = analyzer.analyze_wuxing_zhi_connections()
        results['knowledge_conflicts'] = analyzer.find_knowledge_conflicts()
        
        institutional, folk, unclear = analyzer.classify_institutional_vs_folk(cross_universe_persons)
        results['institutional_vs_folk'] = {
            'institutional': institutional,
            'folk': folk,
            'unclear': unclear
        }
        
        results['cezi_social_analysis'] = analyzer.analyze_cezi_social_significance()
        
        # 保存分析结果
        with open('universe_interaction_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✨ 两大宇宙连接与互动分析完成！")
        print(f"📄 详细结果已保存到: universe_interaction_analysis.json")
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
