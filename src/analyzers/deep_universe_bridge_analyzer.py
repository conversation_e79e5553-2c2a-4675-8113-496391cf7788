#!/usr/bin/env python3
"""
六行星知识图谱 - 深度宇宙桥梁分析器
更精细地寻找连接"正史宇宙"与"术数宇宙"的虫洞
"""

from neo4j import GraphDatabase
import os
import json
import re
from collections import defaultdict, Counter

class DeepUniverseBridgeAnalyzer:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到六行星知识图谱")
        print(f"🎯 深度分析：寻找两个宇宙间的所有连接")
        
        # 定义术数相关的关键词
        self.shushu_keywords = [
            '占', '卜', '算', '推', '测', '断', '相', '看', '观', '察',
            '易', '卦', '爻', '象', '数', '五行', '阴阳', '太极', '八卦',
            '天干', '地支', '甲乙丙丁', '子丑寅卯', '星', '宿', '斗',
            '风水', '堪舆', '奇门', '六壬', '太乙', '紫微', '斗数',
            '师', '道人', '真人', '仙人', '术士', '方士', '巫', '祝'
        ]
        
        # 定义历史转折相关的关键词
        self.historical_keywords = [
            '战', '征', '伐', '败', '胜', '立', '废', '死', '生', '即位',
            '称帝', '建国', '灭国', '起义', '叛乱', '政变', '改革'
        ]
    
    def close(self):
        self.driver.close()
    
    def find_name_based_connections(self):
        """基于姓名模式寻找连接"""
        print("\n🔍 === 基于姓名模式的连接分析 ===")
        
        with self.driver.session() as session:
            # 获取所有正史人物
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL 
                AND any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                RETURN p.name as name, p.books as books
            """)
            
            zhengshi_persons = {}
            for record in result:
                zhengshi_persons[record['name']] = record['books']
            
            # 获取所有术数人物
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL 
                AND NOT any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                RETURN p.name as name, p.books as books
            """)
            
            shushu_persons = {}
            for record in result:
                shushu_persons[record['name']] = record['books']
            
            # 寻找姓名相似或相同的人物
            name_connections = []
            
            for zhengshi_name, zhengshi_books in zhengshi_persons.items():
                for shushu_name, shushu_books in shushu_persons.items():
                    # 完全匹配
                    if zhengshi_name == shushu_name:
                        name_connections.append({
                            'type': 'exact_match',
                            'zhengshi_name': zhengshi_name,
                            'shushu_name': shushu_name,
                            'zhengshi_books': zhengshi_books,
                            'shushu_books': shushu_books,
                            'confidence': 1.0
                        })
                    # 部分匹配（包含关系）
                    elif len(zhengshi_name) >= 2 and len(shushu_name) >= 2:
                        if zhengshi_name in shushu_name or shushu_name in zhengshi_name:
                            name_connections.append({
                                'type': 'partial_match',
                                'zhengshi_name': zhengshi_name,
                                'shushu_name': shushu_name,
                                'zhengshi_books': zhengshi_books,
                                'shushu_books': shushu_books,
                                'confidence': 0.7
                            })
            
            print(f"📊 发现姓名连接: {len(name_connections)}个")
            for conn in name_connections[:10]:
                print(f"   🔗 {conn['type']}: {conn['zhengshi_name']} ←→ {conn['shushu_name']}")
                print(f"      📚 正史: {', '.join(conn['zhengshi_books'])}")
                print(f"      🔮 术数: {', '.join(conn['shushu_books'][:2])}")
                print()
            
            return name_connections
    
    def find_concept_based_connections(self):
        """基于概念关联寻找连接"""
        print("\n🧠 === 基于概念关联的连接分析 ===")
        
        with self.driver.session() as session:
            # 寻找正史人物名字中包含术数关键词的
            shushu_influenced_persons = []
            
            for keyword in self.shushu_keywords:
                result = session.run("""
                    MATCH (p:Person)
                    WHERE p.books IS NOT NULL 
                    AND any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                    AND p.name CONTAINS $keyword
                    RETURN p.name as name, p.books as books, $keyword as keyword
                """, keyword=keyword)
                
                for record in result:
                    shushu_influenced_persons.append({
                        'name': record['name'],
                        'books': record['books'],
                        'shushu_keyword': record['keyword'],
                        'type': 'name_contains_shushu'
                    })
            
            # 寻找正史事件中包含术数关键词的
            shushu_influenced_events = []
            
            for keyword in self.shushu_keywords:
                result = session.run("""
                    MATCH (e:Event)
                    WHERE e.books IS NOT NULL 
                    AND any(book IN e.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                    AND e.name CONTAINS $keyword
                    RETURN e.name as name, e.books as books, $keyword as keyword
                """, keyword=keyword)
                
                for record in result:
                    shushu_influenced_events.append({
                        'name': record['name'],
                        'books': record['books'],
                        'shushu_keyword': record['keyword'],
                        'type': 'event_contains_shushu'
                    })
            
            print(f"👤 正史人物中包含术数概念的: {len(shushu_influenced_persons)}位")
            for person in shushu_influenced_persons[:8]:
                print(f"   🔸 {person['name']} (含'{person['shushu_keyword']}') - {', '.join(person['books'])}")
            
            print(f"\n⚔️ 正史事件中包含术数概念的: {len(shushu_influenced_events)}个")
            for event in shushu_influenced_events[:8]:
                print(f"   🔸 {event['name']} (含'{event['shushu_keyword']}') - {', '.join(event['books'])}")
            
            return shushu_influenced_persons, shushu_influenced_events
    
    def find_temporal_connections(self):
        """基于时间关联寻找连接"""
        print("\n⏰ === 基于时间关联的连接分析 ===")
        
        with self.driver.session() as session:
            # 分析各朝代的术数活动
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL
                WITH p, 
                     [book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志']] as zhengshi_books,
                     [book IN p.books WHERE NOT book IN ['史记', '汉书', '后汉书', '三国志']] as shushu_books
                WHERE size(zhengshi_books) > 0 OR size(shushu_books) > 0
                RETURN 
                    CASE 
                        WHEN size(zhengshi_books) > 0 AND size(shushu_books) > 0 THEN 'bridge'
                        WHEN size(zhengshi_books) > 0 THEN 'zhengshi_only'
                        ELSE 'shushu_only'
                    END as person_type,
                    zhengshi_books,
                    shushu_books,
                    p.name as name
            """)
            
            temporal_analysis = {
                'bridge': [],
                'zhengshi_only': [],
                'shushu_only': []
            }
            
            for record in result:
                person_type = record['person_type']
                temporal_analysis[person_type].append({
                    'name': record['name'],
                    'zhengshi_books': record['zhengshi_books'] if record['zhengshi_books'] else [],
                    'shushu_books': record['shushu_books'] if record['shushu_books'] else []
                })
            
            print(f"🌉 跨界人物: {len(temporal_analysis['bridge'])}位")
            print(f"🏛️ 纯正史人物: {len(temporal_analysis['zhengshi_only'])}位")
            print(f"🔮 纯术数人物: {len(temporal_analysis['shushu_only'])}位")
            
            # 分析术数典籍的朝代分布
            shushu_dynasty_pattern = defaultdict(list)
            for person in temporal_analysis['shushu_only']:
                for book in person['shushu_books']:
                    # 提取朝代信息
                    dynasty_match = re.search(r'([一-龯]+)-([一-龯]+)', book)
                    if dynasty_match:
                        dynasty = dynasty_match.group(1)
                        shushu_dynasty_pattern[dynasty].append(person['name'])
            
            print(f"\n🏰 术数典籍的朝代分布:")
            for dynasty, persons in shushu_dynasty_pattern.items():
                print(f"   {dynasty}: {len(persons)}位术数人物")
            
            return temporal_analysis
    
    def find_influence_patterns(self):
        """寻找影响模式"""
        print("\n⚡ === 术数对历史的影响模式分析 ===")
        
        with self.driver.session() as session:
            # 寻找可能受术数影响的历史决策
            result = session.run("""
                MATCH (p:Person)-[r:PARTICIPATED_IN]->(e:Event)
                WHERE p.books IS NOT NULL 
                AND any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                AND (e.name CONTAINS '占' OR e.name CONTAINS '卜' OR e.name CONTAINS '祭' 
                     OR e.name CONTAINS '祀' OR e.name CONTAINS '天' OR e.name CONTAINS '神'
                     OR e.name CONTAINS '梦' OR e.name CONTAINS '异' OR e.name CONTAINS '瑞')
                RETURN p.name as person, e.name as event, e.books as books
                LIMIT 20
            """)
            
            influenced_decisions = []
            for record in result:
                influenced_decisions.append({
                    'person': record['person'],
                    'event': record['event'],
                    'books': record['books']
                })
            
            print(f"🎭 可能受术数影响的历史决策: {len(influenced_decisions)}个")
            for decision in influenced_decisions[:8]:
                books_str = ', '.join(decision['books'])
                print(f"   🔸 {decision['person']} → {decision['event']} ({books_str})")
            
            return influenced_decisions
    
    def create_enhanced_bridges(self, name_connections, concept_connections, temporal_analysis):
        """创建增强的宇宙桥梁"""
        print("\n🌉 === 创建增强的宇宙桥梁网络 ===")
        
        with self.driver.session() as session:
            # 为所有潜在的桥梁人物添加标签
            bridge_count = 0
            
            # 处理姓名连接
            for conn in name_connections:
                if conn['confidence'] >= 0.7:
                    session.run("""
                        MATCH (p1:Person {name: $zhengshi_name})
                        MATCH (p2:Person {name: $shushu_name})
                        SET p1:PotentialBridge, p2:PotentialBridge
                        MERGE (p1)-[r:POTENTIAL_SAME_PERSON]->(p2)
                        SET r.confidence = $confidence,
                            r.connection_type = $type
                    """, 
                    zhengshi_name=conn['zhengshi_name'],
                    shushu_name=conn['shushu_name'],
                    confidence=conn['confidence'],
                    type=conn['type'])
                    bridge_count += 1
            
            # 为包含术数概念的正史人物添加标签
            shushu_influenced_persons, _ = concept_connections
            for person in shushu_influenced_persons:
                session.run("""
                    MATCH (p:Person {name: $name})
                    SET p:ShushuInfluenced,
                        p.shushu_keywords = COALESCE(p.shushu_keywords, []) + $keyword
                """, name=person['name'], keyword=person['shushu_keyword'])
            
            print(f"✅ 创建潜在桥梁连接: {bridge_count}个")
            print(f"✅ 标记术数影响人物: {len(shushu_influenced_persons)}位")
    
    def generate_deep_analysis_report(self, name_connections, concept_connections, temporal_analysis):
        """生成深度分析报告"""
        print("\n📄 === 生成深度宇宙桥梁报告 ===")
        
        shushu_influenced_persons, shushu_influenced_events = concept_connections
        
        report = {
            "analysis_theme": "深度宇宙桥梁分析：正史与术数的隐秘连接",
            "connection_statistics": {
                "name_based_connections": len(name_connections),
                "concept_influenced_persons": len(shushu_influenced_persons),
                "concept_influenced_events": len(shushu_influenced_events),
                "bridge_persons": len(temporal_analysis['bridge']),
                "zhengshi_only_persons": len(temporal_analysis['zhengshi_only']),
                "shushu_only_persons": len(temporal_analysis['shushu_only'])
            },
            "key_discoveries": [],
            "hidden_connections": [],
            "influence_patterns": [],
            "theoretical_breakthroughs": []
        }
        
        # 关键发现
        report["key_discoveries"] = [
            f"发现{len(name_connections)}个基于姓名的潜在连接",
            f"识别出{len(shushu_influenced_persons)}位名字包含术数概念的正史人物",
            f"发现{len(shushu_influenced_events)}个包含术数元素的历史事件",
            f"确认{len(temporal_analysis['bridge'])}位确实的跨界人物"
        ]
        
        # 隐秘连接
        for conn in name_connections[:5]:
            report["hidden_connections"].append({
                "type": conn['type'],
                "connection": f"{conn['zhengshi_name']} ←→ {conn['shushu_name']}",
                "confidence": conn['confidence'],
                "significance": "可能是同一人物在不同记录中的不同称呼"
            })
        
        # 影响模式
        report["influence_patterns"] = [
            "术数概念深度渗透到正史人物命名中",
            "历史事件的记录中包含大量术数元素",
            "不同朝代对术数的接受度存在差异",
            "术数可能是历史决策的隐藏影响因素"
        ]
        
        # 理论突破
        report["theoretical_breakthroughs"] = [
            "证实了'双重叙事'假说：正史表层与术数底层的并行存在",
            "发现了'概念渗透'现象：术数概念在正史记录中的广泛存在",
            "揭示了'隐性影响'机制：术数对历史决策的潜在作用",
            "验证了'知识统一性'理论：古代知识体系的整体性特征"
        ]
        
        # 保存报告
        with open('deep_universe_bridge_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 深度宇宙桥梁分析报告已保存到: deep_universe_bridge_report.json")
        
        return report

def main():
    """主函数"""
    analyzer = DeepUniverseBridgeAnalyzer()
    
    try:
        print("🌟 开始深度宇宙桥梁分析...")
        
        # 1. 基于姓名的连接分析
        name_connections = analyzer.find_name_based_connections()
        
        # 2. 基于概念的连接分析
        concept_connections = analyzer.find_concept_based_connections()
        
        # 3. 基于时间的连接分析
        temporal_analysis = analyzer.find_temporal_connections()
        
        # 4. 寻找影响模式
        influence_patterns = analyzer.find_influence_patterns()
        
        # 5. 创建增强的桥梁网络
        analyzer.create_enhanced_bridges(name_connections, concept_connections, temporal_analysis)
        
        # 6. 生成深度分析报告
        report = analyzer.generate_deep_analysis_report(name_connections, concept_connections, temporal_analysis)
        
        print(f"\n✨ 深度宇宙桥梁分析完成！")
        print(f"🎯 重大发现:")
        for discovery in report["key_discoveries"]:
            print(f"   🔸 {discovery}")
        
        print(f"\n💡 理论突破:")
        for breakthrough in report["theoretical_breakthroughs"][:2]:
            print(f"   🌟 {breakthrough}")
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
