#!/usr/bin/env python3
"""
六行星知识图谱 - 宇宙桥梁分析器
寻找连接"正史宇宙"与"术数宇宙"的虫洞和跨界人物
"""

from neo4j import GraphDatabase
import os
import json
from collections import defaultdict, Counter

class UniverseBridgeAnalyzer:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到六行星知识图谱")
        print(f"🎯 分析主题：寻找连接两个宇宙的虫洞")
        print(f"🌌 正史宇宙 ←→ 术数宇宙")
    
    def close(self):
        self.driver.close()
    
    def analyze_universe_overlap(self):
        """分析两个宇宙的重叠"""
        print("\n🔍 === 宇宙重叠分析 ===")
        
        with self.driver.session() as session:
            # 统计正史宇宙的人物
            result = session.run("""
                MATCH (p:Person) 
                WHERE NOT 'shushu_master' IN COALESCE(p.category, [])
                RETURN count(p) as zhengshi_persons
            """)
            zhengshi_count = result.single()["zhengshi_persons"]
            
            # 统计术数宇宙的人物
            result = session.run("""
                MATCH (p:Person) 
                WHERE 'shushu_master' IN COALESCE(p.category, [])
                RETURN count(p) as shushu_persons
            """)
            shushu_count = result.single()["shushu_persons"]
            
            # 寻找跨界人物（同时存在于两个宇宙）
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL AND size(p.books) > 0
                WITH p, 
                     [book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志']] as zhengshi_books,
                     [book IN p.books WHERE NOT book IN ['史记', '汉书', '后汉书', '三国志']] as shushu_books
                WHERE size(zhengshi_books) > 0 AND size(shushu_books) > 0
                RETURN p.name as name, 
                       zhengshi_books, 
                       shushu_books,
                       p.books as all_books,
                       p.category as category
                ORDER BY size(p.books) DESC
            """)
            
            bridge_persons = []
            for record in result:
                bridge_persons.append({
                    'name': record['name'],
                    'zhengshi_books': record['zhengshi_books'],
                    'shushu_books': record['shushu_books'],
                    'all_books': record['all_books'],
                    'category': record['category'] if record['category'] else []
                })
            
            print(f"📊 宇宙规模统计:")
            print(f"   🏛️ 正史宇宙人物: {zhengshi_count}位")
            print(f"   🔮 术数宇宙人物: {shushu_count}位")
            print(f"   🌉 跨界人物（虫洞）: {len(bridge_persons)}位")
            
            if len(bridge_persons) > 0:
                print(f"\n🌟 发现的宇宙桥梁人物:")
                for person in bridge_persons[:10]:
                    zhengshi_str = ', '.join(person['zhengshi_books'])
                    shushu_str = ', '.join(person['shushu_books'][:2])
                    print(f"   🌉 {person['name']}")
                    print(f"      📚 正史记录: {zhengshi_str}")
                    print(f"      🔮 术数记录: {shushu_str}")
                    if person['category']:
                        print(f"      🏷️ 类别: {', '.join(person['category'])}")
                    print()
            
            return bridge_persons, zhengshi_count, shushu_count
    
    def find_historical_shushu_masters(self):
        """寻找历史上的术数大师"""
        print("\n🔮 === 历史术数大师分析 ===")
        
        with self.driver.session() as session:
            # 查找在正史中被记录的术数相关人物
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL 
                AND any(book IN p.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                AND (p.name CONTAINS '占' OR p.name CONTAINS '卜' OR p.name CONTAINS '算' 
                     OR p.name CONTAINS '相' OR p.name CONTAINS '师' OR p.name CONTAINS '道人'
                     OR p.name CONTAINS '真人' OR p.name CONTAINS '仙人')
                RETURN p.name as name, p.books as books, p.source as source
                ORDER BY size(p.books) DESC
            """)
            
            historical_masters = []
            for record in result:
                historical_masters.append({
                    'name': record['name'],
                    'books': record['books'],
                    'source': record['source']
                })
            
            print(f"🎯 在正史中被记录的术数相关人物: {len(historical_masters)}位")
            for master in historical_masters[:10]:
                books_str = ', '.join(master['books'])
                print(f"   🔸 {master['name']} (记录于: {books_str})")
            
            return historical_masters
    
    def analyze_shushu_influence_on_history(self):
        """分析术数对历史的影响"""
        print("\n⚡ === 术数对历史的影响分析 ===")
        
        with self.driver.session() as session:
            # 查找与术数概念相关的历史事件
            result = session.run("""
                MATCH (e:Event)-[r]-(p:Person)
                WHERE e.books IS NOT NULL 
                AND any(book IN e.books WHERE book IN ['史记', '汉书', '后汉书', '三国志'])
                AND (e.name CONTAINS '占' OR e.name CONTAINS '卜' OR e.name CONTAINS '祭' 
                     OR e.name CONTAINS '祀' OR e.name CONTAINS '天' OR e.name CONTAINS '神')
                RETURN e.name as event, p.name as person, e.books as books
                LIMIT 20
            """)
            
            shushu_events = []
            for record in result:
                shushu_events.append({
                    'event': record['event'],
                    'person': record['person'],
                    'books': record['books']
                })
            
            print(f"🎭 发现与术数相关的历史事件: {len(shushu_events)}个")
            for event in shushu_events[:8]:
                books_str = ', '.join(event['books'])
                print(f"   ⚔️ {event['event']} (涉及: {event['person']}, 记录于: {books_str})")
            
            return shushu_events
    
    def find_dynasty_shushu_patterns(self):
        """发现朝代与术数的模式"""
        print("\n🏰 === 朝代术数模式分析 ===")
        
        with self.driver.session() as session:
            # 分析各朝代对术数的态度
            result = session.run("""
                MATCH (d:Dynasty)
                WHERE d.books IS NOT NULL
                OPTIONAL MATCH (p:Person)-[r]-(sc:ShushuConcept)
                WHERE any(book IN p.books WHERE book IN d.books)
                RETURN d.name as dynasty, 
                       d.books as dynasty_books,
                       count(DISTINCT sc) as shushu_concepts,
                       collect(DISTINCT sc.name)[0..5] as concept_examples
                ORDER BY shushu_concepts DESC
            """)
            
            dynasty_patterns = []
            for record in result:
                dynasty_patterns.append({
                    'dynasty': record['dynasty'],
                    'books': record['dynasty_books'],
                    'shushu_concepts': record['shushu_concepts'],
                    'concept_examples': record['concept_examples']
                })
            
            print(f"👑 朝代术数关联分析:")
            for pattern in dynasty_patterns[:8]:
                if pattern['shushu_concepts'] > 0:
                    books_str = ', '.join(pattern['books']) if pattern['books'] else '无'
                    concepts_str = ', '.join(pattern['concept_examples']) if pattern['concept_examples'] else '无'
                    print(f"   🏛️ {pattern['dynasty']}: {pattern['shushu_concepts']}个术数概念")
                    print(f"      📚 记录来源: {books_str}")
                    print(f"      🔮 相关概念: {concepts_str}")
                    print()
            
            return dynasty_patterns
    
    def create_universe_bridges_in_neo4j(self, bridge_persons):
        """在Neo4j中创建宇宙桥梁关系"""
        print("\n🌉 === 在Neo4j中构建宇宙桥梁 ===")
        
        with self.driver.session() as session:
            bridge_count = 0
            
            for person in bridge_persons:
                # 为跨界人物添加特殊标签和属性
                session.run("""
                    MATCH (p:Person {name: $name})
                    SET p:UniverseBridge,
                        p.bridge_type = 'zhengshi_shushu',
                        p.zhengshi_books = $zhengshi_books,
                        p.shushu_books = $shushu_books,
                        p.bridge_strength = size($zhengshi_books) + size($shushu_books)
                """, 
                name=person['name'],
                zhengshi_books=person['zhengshi_books'],
                shushu_books=person['shushu_books'])
                
                bridge_count += 1
            
            # 创建宇宙桥梁关系
            session.run("""
                MATCH (bridge:UniverseBridge)
                MATCH (sc:ShushuConcept)
                WHERE any(book IN bridge.shushu_books WHERE book IN sc.books)
                MERGE (bridge)-[r:BRIDGES_TO_SHUSHU]->(sc)
                SET r.confidence = 0.9,
                    r.type = 'universe_bridge'
            """)
            
            print(f"✅ 创建宇宙桥梁人物: {bridge_count}位")
            print(f"✅ 建立桥梁关系网络")
    
    def generate_universe_bridge_report(self, bridge_persons, zhengshi_count, shushu_count):
        """生成宇宙桥梁分析报告"""
        print("\n📄 === 生成宇宙桥梁报告 ===")
        
        report = {
            "analysis_theme": "连接正史宇宙与术数宇宙的虫洞分析",
            "universe_statistics": {
                "zhengshi_universe_persons": zhengshi_count,
                "shushu_universe_persons": shushu_count,
                "bridge_persons": len(bridge_persons),
                "bridge_ratio": round(len(bridge_persons) / (zhengshi_count + shushu_count) * 100, 2)
            },
            "key_findings": [],
            "bridge_persons": [],
            "theoretical_implications": [],
            "cypher_queries": []
        }
        
        # 关键发现
        report["key_findings"] = [
            f"发现{len(bridge_persons)}位跨界人物作为连接两个宇宙的虫洞",
            f"桥梁人物占总人物的{report['universe_statistics']['bridge_ratio']}%",
            "证实了正史叙事与术数底层逻辑的深度关联",
            "揭示了历史表层与玄学底层的互动模式"
        ]
        
        # 桥梁人物详情
        for person in bridge_persons[:10]:
            report["bridge_persons"].append({
                "name": person['name'],
                "zhengshi_presence": person['zhengshi_books'],
                "shushu_presence": person['shushu_books'],
                "bridge_strength": len(person['all_books']),
                "significance": "连接正史与术数的关键节点"
            })
        
        # 理论意义
        report["theoretical_implications"] = [
            "验证了'历史暗物质'理论：术数是影响历史的隐藏力量",
            "发现了'双重叙事'模式：表层政治史与底层玄学史的并行",
            "证明了古代知识体系的整体性：政治、军事、术数的统一",
            "揭示了'跨界者'在历史转折中的关键作用"
        ]
        
        # 推荐查询
        report["cypher_queries"] = [
            "// 查看所有宇宙桥梁人物\nMATCH (bridge:UniverseBridge) RETURN bridge ORDER BY bridge.bridge_strength DESC",
            "// 查看桥梁人物的完整关系网络\nMATCH (bridge:UniverseBridge)-[r]-(other) RETURN bridge, r, other LIMIT 50",
            "// 分析术数对历史事件的影响\nMATCH (bridge:UniverseBridge)-[:PARTICIPATED_IN]->(e:Event) RETURN bridge.name, e.name",
            "// 查看跨宇宙的知识传承\nMATCH path = (p:Person)-[:BRIDGES_TO_SHUSHU]->(sc:ShushuConcept) RETURN path LIMIT 20"
        ]
        
        # 保存报告
        with open('universe_bridge_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 宇宙桥梁分析报告已保存到: universe_bridge_report.json")
        
        return report

def main():
    """主函数"""
    analyzer = UniverseBridgeAnalyzer()
    
    try:
        print("🌟 开始寻找连接两个宇宙的虫洞...")
        
        # 1. 分析宇宙重叠
        bridge_persons, zhengshi_count, shushu_count = analyzer.analyze_universe_overlap()
        
        # 2. 寻找历史术数大师
        historical_masters = analyzer.find_historical_shushu_masters()
        
        # 3. 分析术数对历史的影响
        shushu_events = analyzer.analyze_shushu_influence_on_history()
        
        # 4. 发现朝代术数模式
        dynasty_patterns = analyzer.find_dynasty_shushu_patterns()
        
        # 5. 在Neo4j中创建桥梁关系
        analyzer.create_universe_bridges_in_neo4j(bridge_persons)
        
        # 6. 生成报告
        report = analyzer.generate_universe_bridge_report(bridge_persons, zhengshi_count, shushu_count)
        
        print(f"\n✨ 宇宙桥梁分析完成！")
        print(f"🎯 核心发现:")
        for finding in report["key_findings"]:
            print(f"   🔸 {finding}")
        
        print(f"\n💡 理论意义:")
        for implication in report["theoretical_implications"][:2]:
            print(f"   🌟 {implication}")
        
        print(f"\n🔍 推荐Neo4j查询:")
        print(f"   {report['cypher_queries'][0]}")
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
