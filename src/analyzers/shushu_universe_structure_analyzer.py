#!/usr/bin/env python3
"""
六行星知识图谱 - 术数宇宙基础结构分析器
回答关于术数宇宙内在规律的9个核心问题
"""

from neo4j import GraphDatabase
import os
import json
import re
from collections import defaultdict, Counter
import networkx as nx
from itertools import combinations

class ShushuUniverseStructureAnalyzer:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到六行星知识图谱")
        print(f"🌌 启动术数宇宙基础结构分析")
        print(f"🎯 目标：解析术数宇宙的内在构造规律")
    
    def close(self):
        self.driver.close()
    
    def analyze_method_tech_tree(self):
        """问题1: 分析方法论的科技树"""
        print("\n🌳 === 问题1: 方法论的科技树分析 ===")
        
        with self.driver.session() as session:
            # 获取所有术数方法及其关系
            result = session.run("""
                MATCH (m:ShushuMethod)
                OPTIONAL MATCH (m)-[r:EVOLVES_TO|COMBINES_INTO]->(derived:ShushuMethod)
                RETURN m.name as method, 
                       collect(derived.name) as derived_methods,
                       size(collect(derived)) as derivation_count
                ORDER BY derivation_count DESC
            """)
            
            tech_tree = {}
            for record in result:
                method = record['method']
                derived = record['derived_methods']
                count = record['derivation_count']
                
                tech_tree[method] = {
                    'derived_methods': derived,
                    'derivation_count': count,
                    'level': self.classify_method_level(method)
                }
            
            # 分析基础方法和复合方法
            basic_methods = [m for m, info in tech_tree.items() 
                           if info['level'] == 'basic' and info['derivation_count'] > 0]
            complex_methods = [m for m, info in tech_tree.items() 
                             if info['level'] == 'complex']
            
            print(f"📊 方法论科技树统计:")
            print(f"   🌱 基础方法: {len(basic_methods)}个")
            print(f"   🏗️ 复合方法: {len(complex_methods)}个")
            print(f"   🔗 有衍生关系的方法: {sum(1 for info in tech_tree.values() if info['derivation_count'] > 0)}个")
            
            print(f"\n🌟 最有影响力的基础方法:")
            for method in sorted(basic_methods, key=lambda x: tech_tree[x]['derivation_count'], reverse=True)[:5]:
                count = tech_tree[method]['derivation_count']
                print(f"   🔸 {method}: 衍生出{count}个方法")
            
            return tech_tree
    
    def classify_method_level(self, method_name: str) -> str:
        """分类方法层级"""
        basic_patterns = [r'^占$|^卜$|^算$|^推$|^测$|^断$', r'^起.*|^立.*|^定.*']
        complex_patterns = [r'奇门.*|六壬.*|太乙.*', r'.*法$|.*术$|.*式$']
        
        for pattern in basic_patterns:
            if re.search(pattern, method_name):
                return 'basic'
        
        for pattern in complex_patterns:
            if re.search(pattern, method_name):
                return 'complex'
        
        return 'intermediate'
    
    def analyze_concept_centrality(self):
        """问题2: 分析概念的向心力"""
        print("\n🎯 === 问题2: 概念向心力分析 ===")
        
        with self.driver.session() as session:
            # 分析概念与方法的连接度
            result = session.run("""
                MATCH (c:ShushuConcept)-[r]-(m:ShushuMethod)
                RETURN c.name as concept, count(m) as method_connections
                ORDER BY method_connections DESC
            """)
            
            concept_centrality = []
            for record in result:
                concept_centrality.append({
                    'concept': record['concept'],
                    'connections': record['method_connections']
                })
            
            # 分析概念间的关系网络
            result = session.run("""
                MATCH (c1:ShushuConcept)-[r]-(c2:ShushuConcept)
                RETURN c1.name as concept1, c2.name as concept2, type(r) as relation_type
            """)
            
            concept_network = defaultdict(set)
            for record in result:
                c1, c2 = record['concept1'], record['concept2']
                concept_network[c1].add(c2)
                concept_network[c2].add(c1)
            
            print(f"📊 概念向心力排名:")
            for i, item in enumerate(concept_centrality[:10]):
                print(f"   {i+1}. {item['concept']}: {item['connections']}个方法连接")
            
            # 找出网络中心
            if concept_centrality:
                center_concept = concept_centrality[0]
                print(f"\n🎯 网络中心概念: {center_concept['concept']} ({center_concept['connections']}个连接)")
            
            return concept_centrality, concept_network
    
    def analyze_scholar_clusters(self):
        """问题3: 分析人物的学派聚类"""
        print("\n👥 === 问题3: 学派聚类分析 ===")
        
        with self.driver.session() as session:
            # 获取人物及其研究的典籍
            result = session.run("""
                MATCH (p:Person)
                WHERE p.books IS NOT NULL AND size(p.books) > 0
                RETURN p.name as person, p.books as books
            """)
            
            person_books = {}
            for record in result:
                person_books[record['person']] = set(record['books'])
            
            # 计算人物间的相似度（基于共同研究的典籍）
            clusters = defaultdict(list)
            similarity_threshold = 0.3
            
            for p1, p2 in combinations(person_books.keys(), 2):
                books1, books2 = person_books[p1], person_books[p2]
                if books1 and books2:
                    intersection = len(books1 & books2)
                    union = len(books1 | books2)
                    similarity = intersection / union if union > 0 else 0
                    
                    if similarity >= similarity_threshold:
                        # 简单聚类：找到共同研究的主要典籍
                        common_books = books1 & books2
                        if common_books:
                            main_book = list(common_books)[0]  # 取第一个作为聚类标识
                            clusters[main_book].extend([p1, p2])
            
            # 去重并整理聚类
            final_clusters = {}
            for book, persons in clusters.items():
                unique_persons = list(set(persons))
                if len(unique_persons) >= 2:  # 至少2个人才算一个学派
                    final_clusters[book] = unique_persons
            
            print(f"📊 发现学派聚类: {len(final_clusters)}个")
            for book, scholars in final_clusters.items():
                print(f"   📚 {book}派: {len(scholars)}位学者")
                for scholar in scholars[:3]:
                    print(f"      - {scholar}")
                if len(scholars) > 3:
                    print(f"      ... 还有{len(scholars)-3}位")
                print()
            
            return final_clusters
    
    def analyze_book_influence(self):
        """问题4: 分析典籍的影响力指数"""
        print("\n📚 === 问题4: 典籍影响力分析 ===")
        
        with self.driver.session() as session:
            # 分析典籍被引用的频率
            result = session.run("""
                MATCH (b:ShushuBook)
                OPTIONAL MATCH (p:Person)-[r]-(b)
                OPTIONAL MATCH (m:ShushuMethod)-[r2]-(b)
                OPTIONAL MATCH (c:ShushuConcept)-[r3]-(b)
                RETURN b.name as book,
                       count(DISTINCT p) as person_connections,
                       count(DISTINCT m) as method_connections,
                       count(DISTINCT c) as concept_connections
                ORDER BY (person_connections + method_connections + concept_connections) DESC
            """)
            
            book_influence = []
            for record in result:
                total_influence = (record['person_connections'] + 
                                 record['method_connections'] + 
                                 record['concept_connections'])
                book_influence.append({
                    'book': record['book'],
                    'person_connections': record['person_connections'],
                    'method_connections': record['method_connections'],
                    'concept_connections': record['concept_connections'],
                    'total_influence': total_influence
                })
            
            print(f"📊 典籍影响力排名:")
            for i, item in enumerate(book_influence[:10]):
                print(f"   {i+1}. {item['book']}")
                print(f"      总影响力: {item['total_influence']}")
                print(f"      人物连接: {item['person_connections']}")
                print(f"      方法连接: {item['method_connections']}")
                print(f"      概念连接: {item['concept_connections']}")
                print()
            
            return book_influence
    
    def analyze_method_application_scope(self):
        """问题5: 分析方法的适用范围"""
        print("\n🎯 === 问题5: 方法适用范围分析 ===")
        
        with self.driver.session() as session:
            result = session.run("""
                MATCH (m:ShushuMethod)
                RETURN m.name as method
            """)
            
            scope_categories = {
                'human_affairs': [],  # 人事
                'national_fortune': [],  # 国运
                'celestial_timing': [],  # 天时
                'geography': [],  # 地理
                'general': []  # 通用
            }
            
            # 分类模式
            patterns = {
                'human_affairs': [r'占.*人|占.*命|占.*婚|占.*病|占.*财|相.*人'],
                'national_fortune': [r'占.*国|占.*君|占.*政|占.*战|占.*兵'],
                'celestial_timing': [r'占.*时|占.*年|占.*月|占.*日|占.*节'],
                'geography': [r'占.*地|占.*宅|占.*墓|风水|堪舆'],
            }
            
            for record in result:
                method = record['method']
                categorized = False
                
                for category, pattern_list in patterns.items():
                    for pattern in pattern_list:
                        if re.search(pattern, method):
                            scope_categories[category].append(method)
                            categorized = True
                            break
                    if categorized:
                        break
                
                if not categorized:
                    scope_categories['general'].append(method)
            
            print(f"📊 方法适用范围分布:")
            for category, methods in scope_categories.items():
                category_names = {
                    'human_affairs': '人事类',
                    'national_fortune': '国运类',
                    'celestial_timing': '天时类',
                    'geography': '地理类',
                    'general': '通用类'
                }
                print(f"   {category_names[category]}: {len(methods)}个方法")
                if methods:
                    for method in methods[:3]:
                        print(f"      - {method}")
                    if len(methods) > 3:
                        print(f"      ... 还有{len(methods)-3}个")
                print()
            
            return scope_categories
    
    def detect_pseudo_books(self):
        """问题6: 识别伪书"""
        print("\n🕵️ === 问题6: 伪书识别分析 ===")
        
        with self.driver.session() as session:
            # 分析典籍的时代一致性
            result = session.run("""
                MATCH (b:ShushuBook)
                OPTIONAL MATCH (p:Person)-[r]-(b)
                WHERE p.books IS NOT NULL
                RETURN b.name as book, collect(DISTINCT p.name) as related_persons
            """)
            
            suspicious_books = []
            
            for record in result:
                book = record['book']
                persons = record['related_persons']
                
                # 简单的伪书检测：检查书名中的朝代信息
                dynasty_match = re.search(r'([一-龯]+)-([一-龯]+)', book)
                if dynasty_match:
                    claimed_dynasty = dynasty_match.group(1)
                    claimed_author = dynasty_match.group(2)
                    
                    # 检查是否有时代不符的迹象
                    anachronism_score = 0
                    
                    # 检查是否引用了后世的概念或人物
                    later_dynasties = ['明', '清', '民国']
                    for person in persons:
                        for later_dynasty in later_dynasties:
                            if later_dynasty in person and claimed_dynasty not in later_dynasties:
                                anachronism_score += 1
                    
                    if anachronism_score > 0:
                        suspicious_books.append({
                            'book': book,
                            'claimed_dynasty': claimed_dynasty,
                            'claimed_author': claimed_author,
                            'anachronism_score': anachronism_score,
                            'suspicious_elements': persons
                        })
            
            print(f"📊 疑似伪书分析:")
            if suspicious_books:
                for item in suspicious_books[:5]:
                    print(f"   📚 {item['book']}")
                    print(f"      声称朝代: {item['claimed_dynasty']}")
                    print(f"      可疑程度: {item['anachronism_score']}")
                    print(f"      可疑元素: {', '.join(item['suspicious_elements'][:3])}")
                    print()
            else:
                print("   ✅ 未发现明显的伪书特征")
            
            return suspicious_books
    
    def analyze_duanyi_algorithms(self):
        """问题7: 挖掘《断易天机》的核心算法"""
        print("\n🧮 === 问题7: 《断易天机》算法挖掘 ===")
        
        with self.driver.session() as session:
            # 获取断易天机中的所有方法
            result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(book IN m.books WHERE book CONTAINS '断易天机')
                RETURN m.name as method
            """)
            
            duanyi_methods = [record['method'] for record in result]
            
            # 分析高频判断逻辑
            judgment_patterns = defaultdict(int)
            calculation_patterns = defaultdict(int)
            
            for method in duanyi_methods:
                # 提取判断逻辑
                if '吉' in method or '凶' in method:
                    judgment_patterns['吉凶判断'] += 1
                if '得' in method or '失' in method:
                    judgment_patterns['得失判断'] += 1
                if '成' in method or '败' in method:
                    judgment_patterns['成败判断'] += 1
                
                # 提取计算模式
                if '卦' in method:
                    calculation_patterns['卦象计算'] += 1
                if '爻' in method:
                    calculation_patterns['爻位计算'] += 1
                if '数' in method:
                    calculation_patterns['数理计算'] += 1
            
            print(f"📊 《断易天机》算法分析 (总计{len(duanyi_methods)}个方法):")
            print(f"   🧠 高频判断逻辑:")
            for pattern, count in sorted(judgment_patterns.items(), key=lambda x: x[1], reverse=True):
                print(f"      {pattern}: {count}次")
            
            print(f"   🧮 核心计算模式:")
            for pattern, count in sorted(calculation_patterns.items(), key=lambda x: x[1], reverse=True):
                print(f"      {pattern}: {count}次")
            
            return duanyi_methods, judgment_patterns, calculation_patterns
    
    def analyze_bingjian_uniqueness(self):
        """问题8: 分析《冰鉴》的独特性"""
        print("\n🪞 === 问题8: 《冰鉴》独特性分析 ===")
        
        with self.driver.session() as session:
            # 获取冰鉴相关的方法和概念
            result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(book IN m.books WHERE book CONTAINS '冰鉴')
                RETURN m.name as method
            """)
            
            bingjian_methods = [record['method'] for record in result]
            
            # 分析冰鉴的特征
            traditional_concepts = ['阴阳', '五行', '八卦', '干支']
            empirical_features = ['相', '观', '察', '看', '形', '色', '神', '气']
            
            traditional_count = 0
            empirical_count = 0
            
            for method in bingjian_methods:
                for concept in traditional_concepts:
                    if concept in method:
                        traditional_count += 1
                        break
                
                for feature in empirical_features:
                    if feature in method:
                        empirical_count += 1
                        break
            
            total_methods = len(bingjian_methods)
            traditional_ratio = traditional_count / total_methods if total_methods > 0 else 0
            empirical_ratio = empirical_count / total_methods if total_methods > 0 else 0
            
            print(f"📊 《冰鉴》独特性分析:")
            print(f"   📚 总方法数: {total_methods}")
            print(f"   🔮 传统术数概念比例: {traditional_ratio:.2%}")
            print(f"   👁️ 经验观察特征比例: {empirical_ratio:.2%}")
            
            if empirical_ratio > traditional_ratio:
                print(f"   🎯 结论: 《冰鉴》更偏向经验主义的相人术")
            else:
                print(f"   🎯 结论: 《冰鉴》与传统术数概念关联紧密")
            
            return bingjian_methods, traditional_ratio, empirical_ratio
    
    def find_islands_and_bridges(self):
        """问题9: 寻找孤岛与桥梁"""
        print("\n🌉 === 问题9: 孤岛与桥梁分析 ===")
        
        with self.driver.session() as session:
            # 分析节点的连接度
            result = session.run("""
                MATCH (n)
                WHERE n:ShushuConcept OR n:ShushuMethod OR n:Person
                OPTIONAL MATCH (n)-[r]-(connected)
                RETURN labels(n)[0] as node_type, n.name as name, count(connected) as connections
                ORDER BY connections
            """)
            
            nodes_by_connection = []
            for record in result:
                nodes_by_connection.append({
                    'type': record['node_type'],
                    'name': record['name'],
                    'connections': record['connections']
                })
            
            # 找出孤岛（连接数为0或很少）
            islands = [node for node in nodes_by_connection if node['connections'] <= 1]
            
            # 找出桥梁（连接数很多）
            bridges = [node for node in nodes_by_connection if node['connections'] >= 10]
            
            print(f"📊 网络结构分析:")
            print(f"   🏝️ 孤岛节点: {len(islands)}个")
            for island in islands[:5]:
                print(f"      {island['type']}: {island['name']} ({island['connections']}个连接)")
            
            print(f"   🌉 桥梁节点: {len(bridges)}个")
            for bridge in bridges[:5]:
                print(f"      {bridge['type']}: {bridge['name']} ({bridge['connections']}个连接)")
            
            return islands, bridges

def main():
    """主函数"""
    analyzer = ShushuUniverseStructureAnalyzer()
    
    try:
        print("🌟 开始术数宇宙基础结构分析...")
        
        # 回答9个核心问题
        results = {}
        
        results['tech_tree'] = analyzer.analyze_method_tech_tree()
        results['concept_centrality'] = analyzer.analyze_concept_centrality()
        results['scholar_clusters'] = analyzer.analyze_scholar_clusters()
        results['book_influence'] = analyzer.analyze_book_influence()
        results['method_scope'] = analyzer.analyze_method_application_scope()
        results['pseudo_books'] = analyzer.detect_pseudo_books()
        results['duanyi_algorithms'] = analyzer.analyze_duanyi_algorithms()
        results['bingjian_uniqueness'] = analyzer.analyze_bingjian_uniqueness()
        results['islands_bridges'] = analyzer.find_islands_and_bridges()
        
        # 保存分析结果
        with open('shushu_universe_structure_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✨ 术数宇宙基础结构分析完成！")
        print(f"📄 详细结果已保存到: shushu_universe_structure_analysis.json")
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
