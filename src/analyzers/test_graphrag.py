#!/usr/bin/env python3
import json
import requests

def test_graphrag():
    """测试GraphRAG的功能"""
    
    # 1. 加载史记数据
    print("📚 加载史记数据...")
    with open('cleaned_shishu/史记.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 2. 提取测试文本
    first_chapter = data['chapters'][0]
    test_text = first_chapter['paragraphs'][1]['paragraph'][:300]  # 前300字符
    print(f"📝 测试文本 ({len(test_text)}字符):")
    print(test_text)
    print("\n" + "="*50)
    
    # 3. 测试GraphRAG API
    print("🔍 测试GraphRAG API...")
    
    # 测试基本的graphrag端点 - 使用正确的ChatCompletion格式
    try:
        response = requests.post(
            "http://localhost:8888/v1/graphrag",
            json={
                "messages": [
                    {
                        "role": "user",
                        "content": f"请从以下文本中提取人物、地点、事件：{test_text}"
                    }
                ],
                "model": "graphrag"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ GraphRAG响应成功!")
            result = response.json()
            print("📊 响应结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"❌ GraphRAG响应失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ GraphRAG请求超时")
    except requests.exceptions.ConnectionError:
        print("🔌 无法连接到GraphRAG服务")
    except Exception as e:
        print(f"❌ 请求出错: {e}")

if __name__ == "__main__":
    test_graphrag()
