#!/usr/bin/env python3
"""
六行星知识图谱 - 术数方法论家谱分析器
分析1,215个术数方法的传承演化关系，构建中华术数"科技树"
"""

from neo4j import GraphDatabase
import os
import json
import re
from collections import defaultdict, Counter
import networkx as nx

class ShushuTechTreeAnalyzer:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到六行星知识图谱")
        print(f"🎯 分析主题：术数方法论的家谱与科技树")
        
        # 定义方法层级关系模式
        self.method_hierarchy_patterns = {
            # 基础层：原始方法
            'foundation': [
                r'^占$|^卜$|^筮$|^算$',  # 最基础的占卜算命
                r'^观$|^察$|^看$|^视$',  # 基础观察方法
                r'^取$|^择$|^选$|^定$'   # 基础选择方法
            ],
            # 工具层：具体工具和技术
            'tools': [
                r'卦|爻|象|数',           # 卦象相关
                r'星|宿|斗|辰',           # 天文相关
                r'干|支|甲乙丙丁',        # 干支相关
                r'五行|金木水火土',        # 五行相关
            ],
            # 应用层：具体应用方法
            'applications': [
                r'推.*|断.*|测.*|判.*',   # 推算类方法
                r'排.*|布.*|立.*|设.*',   # 布局类方法
                r'择.*日|选.*时|定.*期',   # 择时类方法
                r'相.*|看.*相|观.*形'      # 相术类方法
            ],
            # 综合层：复合高级方法
            'advanced': [
                r'奇门遁甲|六壬|太乙',     # 三式
                r'紫微斗数|四柱八字',      # 命理系统
                r'风水|堪舆|地理',         # 风水系统
                r'梅花易数|六爻'           # 易学应用
            ]
        }
        
        # 方法演化关系模式
        self.evolution_patterns = {
            'derives_from': [
                (r'推(.+)', r'占\1'),     # 推X <- 占X
                (r'断(.+)', r'卜\1'),     # 断X <- 卜X  
                (r'测(.+)', r'算\1'),     # 测X <- 算X
                (r'排(.+)', r'择\1'),     # 排X <- 择X
                (r'布(.+)', r'定\1'),     # 布X <- 定X
            ],
            'combines_with': [
                (r'(.+)数', r'\1算'),     # X数 + X算
                (r'(.+)法', r'\1术'),     # X法 + X术
                (r'(.+)式', r'\1卦'),     # X式 + X卦
            ]
        }
    
    def close(self):
        self.driver.close()
    
    def analyze_method_hierarchy(self):
        """分析方法层级结构"""
        print("\n🏗️ === 术数方法层级分析 ===")
        
        with self.driver.session() as session:
            # 获取所有术数方法
            result = session.run("""
                MATCH (m:ShushuMethod) 
                RETURN m.name as method_name, m.books as books
                ORDER BY m.name
            """)
            
            methods = [(record['method_name'], record['books']) for record in result]
            print(f"📊 总计术数方法: {len(methods)}个")
            
            # 按层级分类方法
            hierarchy = {
                'foundation': [],
                'tools': [],
                'applications': [],
                'advanced': [],
                'unclassified': []
            }
            
            for method_name, books in methods:
                classified = False
                
                for level, patterns in self.method_hierarchy_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, method_name):
                            hierarchy[level].append({
                                'name': method_name,
                                'books': books if books else [],
                                'level': level
                            })
                            classified = True
                            break
                    if classified:
                        break
                
                if not classified:
                    hierarchy['unclassified'].append({
                        'name': method_name,
                        'books': books if books else [],
                        'level': 'unclassified'
                    })
            
            # 输出层级分析结果
            level_names = {
                'foundation': '基础层（原始方法）',
                'tools': '工具层（技术工具）', 
                'applications': '应用层（具体应用）',
                'advanced': '综合层（高级系统）',
                'unclassified': '未分类'
            }
            
            for level, methods_list in hierarchy.items():
                print(f"\n📚 {level_names[level]}: {len(methods_list)}个方法")
                # 显示前10个方法
                for method in methods_list[:10]:
                    books_str = ', '.join(method['books'][:2]) if method['books'] else '无记录'
                    print(f"   🔹 {method['name']} (来源: {books_str})")
                if len(methods_list) > 10:
                    print(f"   ... 还有{len(methods_list)-10}个方法")
            
            return hierarchy
    
    def discover_method_evolution_chains(self):
        """发现方法演化链"""
        print("\n🧬 === 方法演化链发现 ===")
        
        with self.driver.session() as session:
            # 获取所有方法
            result = session.run("MATCH (m:ShushuMethod) RETURN m.name as name")
            all_methods = [record['name'] for record in result]
            
            evolution_chains = []
            
            # 基于命名模式发现演化关系
            for derived_pattern, base_pattern in self.evolution_patterns['derives_from']:
                for method in all_methods:
                    derived_match = re.search(derived_pattern, method)
                    if derived_match:
                        # 寻找可能的基础方法
                        base_name = re.sub(derived_pattern, base_pattern, method)
                        if base_name in all_methods and base_name != method:
                            evolution_chains.append({
                                'base': base_name,
                                'derived': method,
                                'type': 'evolution',
                                'pattern': f"{base_pattern} -> {derived_pattern}"
                            })
            
            # 发现组合关系
            combination_chains = []
            for combo_pattern, base_pattern in self.evolution_patterns['combines_with']:
                for method in all_methods:
                    combo_match = re.search(combo_pattern, method)
                    if combo_match:
                        base_name = re.sub(combo_pattern, base_pattern, method)
                        if base_name in all_methods and base_name != method:
                            combination_chains.append({
                                'base': base_name,
                                'combined': method,
                                'type': 'combination',
                                'pattern': f"{base_pattern} + X -> {combo_pattern}"
                            })
            
            print(f"🔗 发现演化链: {len(evolution_chains)}条")
            for chain in evolution_chains[:10]:
                print(f"   📈 {chain['base']} → {chain['derived']} ({chain['type']})")
            
            print(f"\n🔗 发现组合链: {len(combination_chains)}条")
            for chain in combination_chains[:10]:
                print(f"   🔄 {chain['base']} + X → {chain['combined']} ({chain['type']})")
            
            return evolution_chains, combination_chains
    
    def build_tech_tree_graph(self, hierarchy, evolution_chains, combination_chains):
        """构建术数科技树图"""
        print("\n🌳 === 构建术数科技树 ===")
        
        # 创建有向图
        tech_tree = nx.DiGraph()
        
        # 添加节点（按层级）
        for level, methods in hierarchy.items():
            for method in methods:
                tech_tree.add_node(method['name'], 
                                 level=level, 
                                 books=method['books'])
        
        # 添加演化边
        for chain in evolution_chains:
            tech_tree.add_edge(chain['base'], chain['derived'], 
                             relation='evolution', 
                             type=chain['type'])
        
        # 添加组合边
        for chain in combination_chains:
            tech_tree.add_edge(chain['base'], chain['combined'], 
                             relation='combination', 
                             type=chain['type'])
        
        # 分析图结构
        print(f"📊 科技树统计:")
        print(f"   🔹 节点数: {tech_tree.number_of_nodes()}")
        print(f"   🔹 边数: {tech_tree.number_of_edges()}")
        print(f"   🔹 连通分量: {nx.number_weakly_connected_components(tech_tree)}")
        
        # 找出根节点（入度为0的节点）
        root_nodes = [node for node in tech_tree.nodes() if tech_tree.in_degree(node) == 0]
        print(f"   🌱 根节点（基础方法）: {len(root_nodes)}个")
        for root in root_nodes[:10]:
            print(f"      🔸 {root}")
        
        # 找出叶节点（出度为0的节点）
        leaf_nodes = [node for node in tech_tree.nodes() if tech_tree.out_degree(node) == 0]
        print(f"   🍃 叶节点（终端方法）: {len(leaf_nodes)}个")
        
        # 找出最有影响力的节点（出度最高）
        influential_nodes = sorted(tech_tree.nodes(), 
                                 key=lambda x: tech_tree.out_degree(x), 
                                 reverse=True)[:10]
        print(f"   👑 最有影响力的方法（衍生出最多子方法）:")
        for node in influential_nodes:
            out_degree = tech_tree.out_degree(node)
            if out_degree > 0:
                print(f"      🔸 {node}: 衍生出{out_degree}个方法")
        
        return tech_tree
    
    def analyze_method_families(self, tech_tree):
        """分析方法家族"""
        print("\n👨‍👩‍👧‍👦 === 术数方法家族分析 ===")
        
        # 找出强连通分量（方法家族）
        families = list(nx.weakly_connected_components(tech_tree))
        
        print(f"📊 发现方法家族: {len(families)}个")
        
        # 分析最大的几个家族
        families_sorted = sorted(families, key=len, reverse=True)
        
        for i, family in enumerate(families_sorted[:5]):
            if len(family) > 1:
                print(f"\n🏠 家族 {i+1} ({len(family)}个方法):")
                
                # 找出家族中的根节点
                family_subgraph = tech_tree.subgraph(family)
                family_roots = [node for node in family if family_subgraph.in_degree(node) == 0]
                
                print(f"   🌱 家族根源: {family_roots}")
                
                # 显示家族成员
                for member in list(family)[:10]:
                    level = tech_tree.nodes[member].get('level', 'unknown')
                    print(f"   🔹 {member} ({level})")
                
                if len(family) > 10:
                    print(f"   ... 还有{len(family)-10}个成员")
    
    def create_tech_tree_in_neo4j(self, tech_tree):
        """在Neo4j中创建科技树关系"""
        print("\n💾 === 在Neo4j中构建科技树 ===")
        
        with self.driver.session() as session:
            # 创建演化关系
            evolution_count = 0
            for source, target, data in tech_tree.edges(data=True):
                if data.get('relation') == 'evolution':
                    session.run("""
                        MATCH (base:ShushuMethod {name: $base})
                        MATCH (derived:ShushuMethod {name: $derived})
                        MERGE (base)-[r:EVOLVES_TO]->(derived)
                        SET r.type = 'evolution',
                            r.confidence = 0.8
                    """, base=source, derived=target)
                    evolution_count += 1
            
            # 创建组合关系
            combination_count = 0
            for source, target, data in tech_tree.edges(data=True):
                if data.get('relation') == 'combination':
                    session.run("""
                        MATCH (base:ShushuMethod {name: $base})
                        MATCH (combined:ShushuMethod {name: $combined})
                        MERGE (base)-[r:COMBINES_INTO]->(combined)
                        SET r.type = 'combination',
                            r.confidence = 0.7
                    """, base=source, combined=target)
                    combination_count += 1
            
            print(f"✅ 创建演化关系: {evolution_count}条")
            print(f"✅ 创建组合关系: {combination_count}条")
    
    def generate_tech_tree_report(self, hierarchy, tech_tree):
        """生成科技树分析报告"""
        print("\n📄 === 生成术数科技树报告 ===")
        
        report = {
            "analysis_theme": "中华术数方法论的家谱与科技树",
            "total_methods": tech_tree.number_of_nodes(),
            "total_relationships": tech_tree.number_of_edges(),
            "hierarchy_analysis": {},
            "key_findings": [],
            "tech_tree_structure": {
                "root_methods": [],
                "influential_methods": [],
                "method_families": []
            },
            "cypher_queries": []
        }
        
        # 层级分析
        for level, methods in hierarchy.items():
            report["hierarchy_analysis"][level] = {
                "count": len(methods),
                "examples": [m['name'] for m in methods[:5]]
            }
        
        # 根节点分析
        root_nodes = [node for node in tech_tree.nodes() if tech_tree.in_degree(node) == 0]
        report["tech_tree_structure"]["root_methods"] = root_nodes[:10]
        
        # 影响力分析
        influential_nodes = sorted(tech_tree.nodes(), 
                                 key=lambda x: tech_tree.out_degree(x), 
                                 reverse=True)[:10]
        report["tech_tree_structure"]["influential_methods"] = [
            {"name": node, "derivatives": tech_tree.out_degree(node)} 
            for node in influential_nodes if tech_tree.out_degree(node) > 0
        ]
        
        # 关键发现
        report["key_findings"] = [
            f"发现{len(root_nodes)}个基础方法作为术数体系的根源",
            f"识别出{len(influential_nodes)}个核心方法，它们衍生出大量子方法",
            f"构建了包含{tech_tree.number_of_edges()}条传承关系的完整科技树",
            "证实了术数方法存在明确的层级结构和演化路径"
        ]
        
        # 推荐查询
        report["cypher_queries"] = [
            "// 查看术数方法的演化链\nMATCH path = (base:ShushuMethod)-[:EVOLVES_TO*1..3]->(derived:ShushuMethod) RETURN path LIMIT 10",
            "// 查看最有影响力的基础方法\nMATCH (base:ShushuMethod)-[:EVOLVES_TO]->(derived:ShushuMethod) RETURN base.name, count(derived) as derivatives ORDER BY derivatives DESC LIMIT 10",
            "// 查看方法组合关系\nMATCH (base:ShushuMethod)-[:COMBINES_INTO]->(combined:ShushuMethod) RETURN base.name, combined.name LIMIT 20",
            "// 查看完整的术数科技树\nMATCH (m:ShushuMethod)-[r:EVOLVES_TO|COMBINES_INTO]-(other:ShushuMethod) RETURN m, r, other LIMIT 50"
        ]
        
        # 保存报告
        with open('shushu_tech_tree_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 科技树分析报告已保存到: shushu_tech_tree_report.json")
        
        return report

def main():
    """主函数"""
    analyzer = ShushuTechTreeAnalyzer()
    
    try:
        print("🌟 开始构建中华术数方法论的科技树...")
        
        # 1. 分析方法层级
        hierarchy = analyzer.analyze_method_hierarchy()
        
        # 2. 发现演化链
        evolution_chains, combination_chains = analyzer.discover_method_evolution_chains()
        
        # 3. 构建科技树图
        tech_tree = analyzer.build_tech_tree_graph(hierarchy, evolution_chains, combination_chains)
        
        # 4. 分析方法家族
        analyzer.analyze_method_families(tech_tree)
        
        # 5. 在Neo4j中创建关系
        analyzer.create_tech_tree_in_neo4j(tech_tree)
        
        # 6. 生成报告
        report = analyzer.generate_tech_tree_report(hierarchy, tech_tree)
        
        print(f"\n✨ 中华术数科技树构建完成！")
        print(f"🎯 核心发现:")
        for finding in report["key_findings"]:
            print(f"   🔸 {finding}")
        
        print(f"\n💡 推荐Neo4j查询:")
        for query in report["cypher_queries"][:2]:
            print(f"   {query}")
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
