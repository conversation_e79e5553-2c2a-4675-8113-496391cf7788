#!/usr/bin/env python3
"""
六行星知识图谱 - 术数典籍处理器
处理古代术数典籍，构建术数知识图谱
"""

import os
import re
from neo4j import GraphDatabase
from typing import List, Dict, Set
import chardet

class ShushuProcessor:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        """初始化Neo4j连接"""
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到Neo4j: {uri}")
        
        # 术数相关的实体模式
        self.shushu_patterns = {
            'concepts': [
                r'五行|金木水火土|阴阳|太极|八卦|六十四卦',
                r'天干|地支|甲乙丙丁戊己庚辛壬癸',
                r'子丑寅卯辰巳午未申酉戌亥',
                r'奇门遁甲|六壬|太乙|紫微斗数',
                r'风水|堪舆|相术|命理|占卜',
                r'星宿|二十八宿|北斗|南斗',
                r'九宫|洛书|河图|先天|后天'
            ],
            'methods': [
                r'占[一-龯]*|卜[一-龯]*|算[一-龯]*',
                r'推[一-龯]*|断[一-龯]*|测[一-龯]*',
                r'择[一-龯]*|选[一-龯]*|定[一-龯]*',
                r'布局|排盘|起卦|成卦'
            ],
            'persons': [
                r'[一-龯]{2,4}(?:子|公|先生|道人|真人|仙人)',
                r'(?:太史公|司马|班固|范晔|陈寿)',
                r'(?:孔子|老子|庄子|孟子|荀子)',
                r'(?:张良|诸葛亮|刘伯温|袁天罡|李淳风)'
            ],
            'books': [
                r'易经|周易|易传|系辞|说卦|序卦',
                r'尚书|诗经|礼记|春秋|论语',
                r'史记|汉书|后汉书|三国志',
                r'[一-龯]*经|[一-龯]*传|[一-龯]*书|[一-龯]*志'
            ]
        }
    
    def close(self):
        """关闭连接"""
        self.driver.close()
    
    def detect_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB检测编码
                result = chardet.detect(raw_data)
                return result['encoding'] if result['encoding'] else 'utf-16'
        except:
            return 'utf-16'  # 默认使用UTF-16
    
    def read_shushu_file(self, file_path: str) -> str:
        """读取术数文件"""
        encoding = self.detect_encoding(file_path)
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                # 清理内容
                content = re.sub(r'\x00', '', content)  # 移除空字符
                content = re.sub(r'\r\n', '\n', content)  # 统一换行符
                return content
        except UnicodeDecodeError:
            # 如果UTF-16失败，尝试其他编码
            for enc in ['utf-16le', 'utf-16be', 'gb2312', 'gbk', 'utf-8']:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                        content = re.sub(r'\x00', '', content)
                        content = re.sub(r'\r\n', '\n', content)
                        return content
                except:
                    continue
            return ""
    
    def extract_shushu_entities(self, text: str, book_title: str) -> Dict[str, Set[str]]:
        """从文本中提取术数实体"""
        entities = {
            'concepts': set(),
            'methods': set(), 
            'persons': set(),
            'books': set()
        }
        
        # 提取各类实体
        for category, patterns in self.shushu_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                entities[category].update(matches)
        
        # 添加书名本身
        entities['books'].add(book_title)
        
        # 特殊处理：提取朝代信息
        dynasty_match = re.search(r'([一-龯]+)-([一-龯]+)', book_title)
        if dynasty_match:
            dynasty = dynasty_match.group(1)
            author = dynasty_match.group(2)
            entities['persons'].add(author)
            entities['concepts'].add(dynasty)
        
        return entities
    
    def create_shushu_entities_in_neo4j(self, entities: Dict[str, Set[str]], book_title: str):
        """在Neo4j中创建术数实体"""
        with self.driver.session() as session:
            
            # 创建术数概念节点
            for concept in entities['concepts']:
                session.run("""
                    MERGE (c:ShushuConcept {name: $name})
                    SET c.source = CASE WHEN c.source IS NULL THEN $book
                                   ELSE c.source + '; ' + $book END,
                        c.type = 'shushu_concept',
                        c.books = CASE WHEN c.books IS NULL THEN [$book]
                                  WHEN NOT $book IN c.books THEN c.books + $book
                                  ELSE c.books END
                """, name=concept, book=book_title)
            
            # 创建术数方法节点
            for method in entities['methods']:
                session.run("""
                    MERGE (m:ShushuMethod {name: $name})
                    SET m.source = CASE WHEN m.source IS NULL THEN $book
                                   ELSE m.source + '; ' + $book END,
                        m.type = 'shushu_method',
                        m.books = CASE WHEN m.books IS NULL THEN [$book]
                                  WHEN NOT $book IN m.books THEN m.books + $book
                                  ELSE m.books END
                """, name=method, book=book_title)
            
            # 创建人物节点（如果不存在）
            for person in entities['persons']:
                session.run("""
                    MERGE (p:Person {name: $name})
                    SET p.source = CASE WHEN p.source IS NULL THEN $book
                                   ELSE p.source + '; ' + $book END,
                        p.type = 'person',
                        p.books = CASE WHEN p.books IS NULL THEN [$book]
                                  WHEN NOT $book IN p.books THEN p.books + $book
                                  ELSE p.books END,
                        p.category = CASE WHEN p.category IS NULL THEN 'shushu_master'
                                     ELSE p.category END
                """, name=person, book=book_title)
            
            # 创建典籍节点
            for book in entities['books']:
                session.run("""
                    MERGE (b:ShushuBook {name: $name})
                    SET b.source = $source_book,
                        b.type = 'shushu_book'
                """, name=book, source_book=book_title)
    
    def create_shushu_relationships(self, text: str, entities: Dict[str, Set[str]], book_title: str):
        """创建术数实体间的关系"""
        with self.driver.session() as session:
            
            # 创建概念与方法的关系
            for concept in entities['concepts']:
                for method in entities['methods']:
                    if concept in text and method in text:
                        # 检查是否在同一段落中
                        paragraphs = re.split(r'\n\s*\n', text)
                        for paragraph in paragraphs:
                            if concept in paragraph and method in paragraph:
                                session.run("""
                                    MATCH (c:ShushuConcept {name: $concept})
                                    MATCH (m:ShushuMethod {name: $method})
                                    MERGE (c)-[r:USES_METHOD]->(m)
                                    SET r.confidence = 0.8,
                                        r.source = $book
                                """, concept=concept, method=method, book=book_title)
            
            # 创建人物与典籍的关系
            for person in entities['persons']:
                for book in entities['books']:
                    if person in text and book in text:
                        session.run("""
                            MATCH (p:Person {name: $person})
                            MATCH (b:ShushuBook {name: $book})
                            MERGE (p)-[r:AUTHORED_OR_STUDIED]->(b)
                            SET r.confidence = 0.7,
                                r.source = $source_book
                        """, person=person, book=book, source_book=book_title)
    
    def process_shushu_directory(self, directory: str = "shushubook", max_files: int = 10):
        """处理术数典籍目录"""
        print(f"📚 开始处理术数典籍目录: {directory}")
        
        txt_files = [f for f in os.listdir(directory) if f.endswith('.txt')]
        total_entities = {'concepts': set(), 'methods': set(), 'persons': set(), 'books': set()}
        
        processed_count = 0
        for filename in txt_files[:max_files]:
            if processed_count >= max_files:
                break
                
            file_path = os.path.join(directory, filename)
            book_title = filename.replace('.txt', '')
            
            print(f"📖 处理典籍: {book_title}")
            
            # 读取文件内容
            content = self.read_shushu_file(file_path)
            if not content or len(content) < 100:
                print(f"  ⚠️ 文件内容为空或过短，跳过")
                continue
            
            # 提取实体
            entities = self.extract_shushu_entities(content, book_title)
            
            # 统计实体
            for key in total_entities:
                total_entities[key].update(entities[key])
            
            print(f"  📊 提取实体: 概念{len(entities['concepts'])}个, 方法{len(entities['methods'])}个, 人物{len(entities['persons'])}个, 典籍{len(entities['books'])}个")
            
            # 创建实体和关系
            self.create_shushu_entities_in_neo4j(entities, book_title)
            self.create_shushu_relationships(content, entities, book_title)
            
            processed_count += 1
        
        print(f"\n🎯 术数典籍总计提取:")
        print(f"  🔮 术数概念: {len(total_entities['concepts'])}个")
        print(f"  🛠️ 术数方法: {len(total_entities['methods'])}个")
        print(f"  👤 相关人物: {len(total_entities['persons'])}个")
        print(f"  📚 相关典籍: {len(total_entities['books'])}个")
        
        return total_entities
    
    def query_shushu_sample_data(self):
        """查询术数示例数据"""
        print("\n🔍 查询术数示例数据:")
        
        with self.driver.session() as session:
            # 查询术数概念
            result = session.run("MATCH (c:ShushuConcept) RETURN c.name as name LIMIT 10")
            concepts = [record["name"] for record in result]
            print(f"🔮 术数概念示例: {concepts}")
            
            # 查询术数方法
            result = session.run("MATCH (m:ShushuMethod) RETURN m.name as name LIMIT 5")
            methods = [record["name"] for record in result]
            print(f"🛠️ 术数方法示例: {methods}")
            
            # 查询关系
            result = session.run("""
                MATCH (c:ShushuConcept)-[r]->(m:ShushuMethod) 
                RETURN c.name as concept, type(r) as relation, m.name as method 
                LIMIT 5
            """)
            relations = [(record["concept"], record["relation"], record["method"]) for record in result]
            print(f"🔗 术数关系示例: {relations}")

def main():
    """主函数"""
    processor = ShushuProcessor()
    
    try:
        # 测试连接
        print("🌟 开始术数典籍知识图谱构建...")
        
        # 处理术数典籍
        entities = processor.process_shushu_directory(max_files=5)
        
        # 查询示例数据
        processor.query_shushu_sample_data()
        
        print(f"\n✅ 术数知识图谱构建完成！")
        print(f"🌐 访问Neo4j浏览器: http://localhost:7474")
        print(f"👤 用户名: neo4j")
        print(f"🔑 密码: sixstars123")
        print(f"\n💡 推荐查询:")
        print(f"   MATCH (c:ShushuConcept)-[r]-(other) RETURN c, r, other LIMIT 25")
        print(f"   MATCH (m:ShushuMethod) RETURN m")
        
    except Exception as e:
        print(f"❌ 处理出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        processor.close()

if __name__ == "__main__":
    main()
