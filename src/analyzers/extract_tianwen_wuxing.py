#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取前四史中的天文志、五行志内容
专门为"六行星知识图谱"的天象分析准备数据
"""

import json
import re
from pathlib import Path

def extract_tianwen_wuxing_chapters():
    """提取天文志、五行志相关章节"""
    
    target_chapters = []
    
    # 定义目标关键词
    keywords = ['天文', '五行', '灾异', '符瑞', '天象']
    
    # 处理汉书
    print("=== 处理汉书 ===")
    with open('cleaned_shishu/汉书.json', 'r', encoding='utf-8') as f:
        hanshu = json.load(f)
    
    for chapter in hanshu.get('chapters', []):
        chapter_name = chapter.get('name', '')
        if any(keyword in chapter_name for keyword in keywords):
            print(f"找到汉书章节: {chapter_name}")
            target_chapters.append({
                'source': '汉书',
                'chapter_name': chapter_name,
                'content': chapter
            })
    
    # 处理后汉书
    print("\n=== 处理后汉书 ===")
    with open('cleaned_shishu/后汉书.json', 'r', encoding='utf-8') as f:
        houhanshu = json.load(f)
    
    for chapter in houhanshu.get('chapters', []):
        chapter_name = chapter.get('name', '')
        if any(keyword in chapter_name for keyword in keywords):
            print(f"找到后汉书章节: {chapter_name}")
            target_chapters.append({
                'source': '后汉书',
                'chapter_name': chapter_name,
                'content': chapter
            })
    
    return target_chapters

def extract_tianxiang_records(chapters):
    """从章节中提取具体的天象记录"""
    
    tianxiang_records = []
    
    # 天象相关关键词
    tianxiang_keywords = [
        '彗星', '流星', '日食', '月食', '地震', '洪水', '旱灾', 
        '蝗虫', '异星', '客星', '新星', '超新星', '五星', '荧惑',
        '太白', '辰星', '岁星', '镇星', '雷电', '霜雹', '大风'
    ]
    
    for chapter_info in chapters:
        chapter = chapter_info['content']
        source = chapter_info['source']
        chapter_name = chapter_info['chapter_name']
        
        print(f"\n分析 {source} - {chapter_name}")
        
        for paragraph_info in chapter.get('paragraphs', []):
            paragraph = paragraph_info.get('paragraph', '')
            
            # 查找天象记录
            for keyword in tianxiang_keywords:
                if keyword in paragraph:
                    # 提取包含天象的句子
                    sentences = re.split(r'[。！？]', paragraph)
                    for sentence in sentences:
                        if keyword in sentence and len(sentence.strip()) > 10:
                            tianxiang_records.append({
                                'source': source,
                                'chapter': chapter_name,
                                'tianxiang_type': keyword,
                                'record': sentence.strip(),
                                'full_paragraph': paragraph[:200] + '...' if len(paragraph) > 200 else paragraph
                            })
    
    return tianxiang_records

def create_tianwen_database():
    """创建天文志专用数据"""
    
    print("🌟 开始提取天文志、五行志数据...")
    
    # 提取相关章节
    chapters = extract_tianwen_wuxing_chapters()
    print(f"\n总共找到 {len(chapters)} 个相关章节")
    
    # 提取天象记录
    tianxiang_records = extract_tianxiang_records(chapters)
    print(f"\n总共提取到 {len(tianxiang_records)} 条天象记录")
    
    # 保存结果
    result = {
        'metadata': {
            'total_chapters': len(chapters),
            'total_tianxiang_records': len(tianxiang_records),
            'sources': list(set([ch['source'] for ch in chapters]))
        },
        'chapters': chapters,
        'tianxiang_records': tianxiang_records
    }
    
    # 保存到文件
    with open('tianwen_wuxing_data.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 数据已保存到 tianwen_wuxing_data.json")
    
    # 显示统计信息
    print(f"\n📊 统计信息:")
    for source in result['metadata']['sources']:
        source_records = [r for r in tianxiang_records if r['source'] == source]
        print(f"  {source}: {len(source_records)} 条天象记录")
    
    # 显示天象类型分布
    tianxiang_types = {}
    for record in tianxiang_records:
        tianxiang_type = record['tianxiang_type']
        tianxiang_types[tianxiang_type] = tianxiang_types.get(tianxiang_type, 0) + 1
    
    print(f"\n🌌 天象类型分布:")
    for tianxiang_type, count in sorted(tianxiang_types.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {tianxiang_type}: {count} 次")
    
    return result

if __name__ == "__main__":
    create_tianwen_database()