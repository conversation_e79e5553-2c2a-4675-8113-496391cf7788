#!/usr/bin/env python3
"""
六行星知识图谱 - 高级查询分析器
深度分析史记中的人物关系和历史模式
"""

from neo4j import GraphDatabase
import json
import os
from collections import defaultdict, Counter

class SixStarsAnalyzer:
    def __init__(self, uri=None, user=None, password=None):
        # 使用环境变量，如果没有提供参数的话
        self.uri = uri or os.getenv("NEO4J_URI", "bolt://localhost:7687")
        self.user = user or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD", "sixstars123")
        self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
        print(f"🔗 连接到六行星知识图谱")
    
    def close(self):
        self.driver.close()
    
    def analyze_network_structure(self):
        """分析网络结构"""
        print("\n📊 === 网络结构分析 ===")
        
        with self.driver.session() as session:
            # 统计各类节点数量
            result = session.run("""
                MATCH (n)
                RETURN labels(n)[0] as node_type, count(n) as count
                ORDER BY count DESC
            """)
            
            print("📈 节点类型统计:")
            for record in result:
                print(f"  {record['node_type']}: {record['count']}个")
            
            # 统计关系类型
            result = session.run("""
                MATCH ()-[r]->()
                RETURN type(r) as relation_type, count(r) as count
                ORDER BY count DESC
            """)
            
            print("\n🔗 关系类型统计:")
            for record in result:
                print(f"  {record['relation_type']}: {record['count']}条")
    
    def find_most_connected_entities(self):
        """找出最有影响力的实体"""
        print("\n👑 === 最有影响力的实体 ===")
        
        with self.driver.session() as session:
            # 最有连接的人物
            result = session.run("""
                MATCH (p:Person)-[r]-()
                RETURN p.name as person, count(r) as connections
                ORDER BY connections DESC
                LIMIT 10
            """)
            
            print("👤 最有影响力的人物:")
            for record in result:
                print(f"  {record['person']}: {record['connections']}个连接")
            
            # 最重要的地点
            result = session.run("""
                MATCH (l:Location)-[r]-()
                RETURN l.name as location, count(r) as connections
                ORDER BY connections DESC
                LIMIT 10
            """)
            
            print("\n🏛️ 最重要的地点:")
            for record in result:
                print(f"  {record['location']}: {record['connections']}个连接")
    
    def discover_historical_patterns(self):
        """发现历史模式"""
        print("\n🔍 === 历史模式发现 ===")
        
        with self.driver.session() as session:
            # 战争相关的人物网络
            result = session.run("""
                MATCH (p:Person)-[:PARTICIPATED_IN]->(e:Event)
                WHERE e.name CONTAINS '战' OR e.name CONTAINS '征' OR e.name CONTAINS '伐'
                RETURN p.name as warrior, collect(e.name) as battles
                ORDER BY size(battles) DESC
                LIMIT 5
            """)
            
            print("⚔️ 战争领袖:")
            for record in result:
                battles = ', '.join(record['battles'])
                print(f"  {record['warrior']}: {battles}")
            
            # 地理关联分析
            result = session.run("""
                MATCH (p:Person)-[:ASSOCIATED_WITH]->(l:Location)
                RETURN l.name as location, collect(p.name) as persons
                ORDER BY size(persons) DESC
                LIMIT 5
            """)
            
            print("\n🗺️ 重要地理中心:")
            for record in result:
                persons = ', '.join(record['persons'][:3])  # 只显示前3个
                count = len(record['persons'])
                print(f"  {record['location']}: {persons}等{count}人")
    
    def analyze_narrative_conflicts(self):
        """分析叙事冲突（基础版本）"""
        print("\n🎭 === 叙事冲突分析 ===")
        
        with self.driver.session() as session:
            # 查找同一事件的不同描述
            result = session.run("""
                MATCH (p1:Person)-[:PARTICIPATED_IN]->(e:Event)<-[:PARTICIPATED_IN]-(p2:Person)
                WHERE p1.name <> p2.name
                RETURN e.name as event, collect(DISTINCT p1.name + ', ' + p2.name) as participants
                ORDER BY size(participants) DESC
                LIMIT 5
            """)
            
            print("🤝 多人参与的重大事件:")
            for record in result:
                participants = '; '.join(record['participants'][:3])
                print(f"  {record['event']}: {participants}")
    
    def generate_cypher_queries(self):
        """生成有用的Cypher查询"""
        print("\n💡 === 推荐的Neo4j查询 ===")
        
        queries = [
            ("查看所有人物关系网络", "MATCH (p:Person)-[r]-(other) RETURN p, r, other LIMIT 50"),
            ("查找黄帝相关的所有信息", "MATCH (n) WHERE n.name CONTAINS '黄帝' OR n.name CONTAINS '轩辕' RETURN n"),
            ("查看战争事件网络", "MATCH (p:Person)-[:PARTICIPATED_IN]->(e:Event) WHERE e.name CONTAINS '战' RETURN p, e"),
            ("查找地理位置关联", "MATCH (p:Person)-[:ASSOCIATED_WITH]->(l:Location) RETURN p, l LIMIT 30"),
            ("查看完整的知识图谱", "MATCH (n)-[r]-(m) RETURN n, r, m LIMIT 100")
        ]
        
        for desc, query in queries:
            print(f"\n📝 {desc}:")
            print(f"   {query}")
    
    def export_analysis_report(self):
        """导出分析报告"""
        print("\n📄 === 生成分析报告 ===")
        
        report = {
            "project": "六行星知识图谱",
            "data_source": "史记（前两章）",
            "analysis_time": "2025-07-19",
            "statistics": {},
            "key_findings": []
        }
        
        with self.driver.session() as session:
            # 收集统计数据
            result = session.run("MATCH (n) RETURN labels(n)[0] as type, count(n) as count")
            for record in result:
                report["statistics"][record["type"]] = record["count"]
            
            # 关键发现
            result = session.run("""
                MATCH (p:Person)-[r]-()
                RETURN p.name as person, count(r) as connections
                ORDER BY connections DESC
                LIMIT 3
            """)
            
            top_persons = [f"{r['person']}({r['connections']}连接)" for r in result]
            report["key_findings"].append(f"最有影响力的人物: {', '.join(top_persons)}")
        
        # 保存报告
        with open('sixstars_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 分析报告已保存到: sixstars_analysis_report.json")
    
    def suggest_next_steps(self):
        """建议下一步行动"""
        print("\n🚀 === 下一步建议 ===")
        
        suggestions = [
            "1. 📚 扩展数据源：处理史记的更多章节",
            "2. 🔍 改进实体识别：使用更高级的NLP技术",
            "3. 🌐 添加汉书数据：开始构建'双龙戏珠'对照",
            "4. ⭐ 整合天象数据：实现天文校准法",
            "5. 🎨 开发Web界面：让用户友好地探索图谱",
            "6. 📊 添加时间维度：构建历史时间轴",
            "7. 🔗 建立更多关系类型：师生、敌友、继承等",
            "8. 📈 实现图算法：PageRank、社区发现等"
        ]
        
        for suggestion in suggestions:
            print(f"  {suggestion}")

def main():
    """主函数"""
    analyzer = SixStarsAnalyzer()
    
    try:
        print("🌟 六行星知识图谱深度分析开始...")
        
        # 执行各种分析
        analyzer.analyze_network_structure()
        analyzer.find_most_connected_entities()
        analyzer.discover_historical_patterns()
        analyzer.analyze_narrative_conflicts()
        analyzer.generate_cypher_queries()
        analyzer.export_analysis_report()
        analyzer.suggest_next_steps()
        
        print(f"\n✨ 分析完成！您的历史知识图谱已经活起来了！")
        print(f"🌐 继续在Neo4j浏览器中探索: http://localhost:7474")
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
