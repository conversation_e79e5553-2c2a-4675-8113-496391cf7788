#!/usr/bin/env python3
"""
六行星知识图谱 - 史记数据处理器
连接到远程Neo4j并处理史记数据
"""

import json
import os
import re
from neo4j import GraphDatabase
from typing import List, Dict, Set

class SixStarsProcessor:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        """初始化Neo4j连接"""
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到Neo4j: {uri}")
    
    def close(self):
        """关闭连接"""
        self.driver.close()
    
    def test_connection(self):
        """测试Neo4j连接"""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 'Hello 六行星!' as message")
                message = result.single()["message"]
                print(f"✅ Neo4j连接成功: {message}")
                return True
        except Exception as e:
            print(f"❌ Neo4j连接失败: {e}")
            return False
    
    def clear_database(self):
        """清空数据库（谨慎使用）"""
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
            print("🧹 数据库已清空")
    
    def extract_entities_from_text(self, text: str) -> Dict[str, Set[str]]:
        """从文本中提取实体"""
        entities = {
            'persons': set(),
            'places': set(),
            'events': set(),
            'dynasties': set()
        }
        
        # 简单的正则表达式提取（后续可以用更高级的NLP）
        
        # 提取人名（常见的古代人名模式）
        person_patterns = [
            r'黄帝|炎帝|蚩尤|轩辕|神农氏',
            r'帝[尧舜禹]|[尧舜禹]帝',
            r'[一-龯]{1,3}(?:帝|王|公|侯|伯|子|男)',
            r'(?:太史公|司马迁|班固)'
        ]
        
        for pattern in person_patterns:
            matches = re.findall(pattern, text)
            entities['persons'].update(matches)
        
        # 提取地名
        place_patterns = [
            r'阪泉之野|涿鹿之野|空桐|岱宗|熊山|湘山',
            r'[东西南北]至于[一-龯]+',
            r'[一-龯]{1,4}(?:山|河|江|海|泽|丘|陵)'
        ]
        
        for pattern in place_patterns:
            matches = re.findall(pattern, text)
            entities['places'].update(matches)
        
        # 提取事件
        event_patterns = [
            r'[一-龯]*战于[一-龯]+',
            r'征[一-龯]+|伐[一-龯]+',
            r'[一-龯]*之战|[一-龯]*之役'
        ]
        
        for pattern in event_patterns:
            matches = re.findall(pattern, text)
            entities['events'].update(matches)
        
        # 提取朝代
        dynasty_patterns = [
            r'夏|商|周|秦|汉|唐|宋|元|明|清',
            r'西汉|东汉|西周|东周'
        ]
        
        for pattern in dynasty_patterns:
            matches = re.findall(pattern, text)
            entities['dynasties'].update(matches)
        
        return entities
    
    def create_entities_in_neo4j_with_book(self, entities: Dict[str, Set[str]], source_chapter: str, book_name: str):
        """在Neo4j中创建实体（支持多书籍）"""
        with self.driver.session() as session:

            # 创建人物节点
            for person in entities['persons']:
                session.run("""
                    MERGE (p:Person {name: $name})
                    SET p.source = CASE WHEN p.source IS NULL THEN $source
                                   ELSE p.source + '; ' + $source END,
                        p.type = 'person',
                        p.books = CASE WHEN p.books IS NULL THEN [$book]
                                  WHEN NOT $book IN p.books THEN p.books + $book
                                  ELSE p.books END
                """, name=person, source=source_chapter, book=book_name)

            # 创建地点节点
            for place in entities['places']:
                session.run("""
                    MERGE (l:Location {name: $name})
                    SET l.source = CASE WHEN l.source IS NULL THEN $source
                                   ELSE l.source + '; ' + $source END,
                        l.type = 'location',
                        l.books = CASE WHEN l.books IS NULL THEN [$book]
                                  WHEN NOT $book IN l.books THEN l.books + $book
                                  ELSE l.books END
                """, name=place, source=source_chapter, book=book_name)

            # 创建事件节点
            for event in entities['events']:
                session.run("""
                    MERGE (e:Event {name: $name})
                    SET e.source = CASE WHEN e.source IS NULL THEN $source
                                   ELSE e.source + '; ' + $source END,
                        e.type = 'event',
                        e.books = CASE WHEN e.books IS NULL THEN [$book]
                                  WHEN NOT $book IN e.books THEN e.books + $book
                                  ELSE e.books END
                """, name=event, source=source_chapter, book=book_name)

            # 创建朝代节点
            for dynasty in entities['dynasties']:
                session.run("""
                    MERGE (d:Dynasty {name: $name})
                    SET d.source = CASE WHEN d.source IS NULL THEN $source
                                   ELSE d.source + '; ' + $source END,
                        d.type = 'dynasty',
                        d.books = CASE WHEN d.books IS NULL THEN [$book]
                                  WHEN NOT $book IN d.books THEN d.books + $book
                                  ELSE d.books END
                """, name=dynasty, source=source_chapter, book=book_name)
    
    def create_relationships_with_book(self, text: str, entities: Dict[str, Set[str]], book_name: str):
        """创建实体间的关系（支持多书籍）"""
        with self.driver.session() as session:

            # 创建人物与事件的关系
            for person in entities['persons']:
                for event in entities['events']:
                    if person in text and event in text:
                        # 检查是否在同一句话中
                        sentences = re.split(r'[。！？]', text)
                        for sentence in sentences:
                            if person in sentence and event in sentence:
                                session.run("""
                                    MATCH (p:Person {name: $person})
                                    MATCH (e:Event {name: $event})
                                    MERGE (p)-[r:PARTICIPATED_IN]->(e)
                                    SET r.confidence = 0.8,
                                        r.sources = CASE WHEN r.sources IS NULL THEN [$book]
                                                   WHEN NOT $book IN r.sources THEN r.sources + $book
                                                   ELSE r.sources END
                                """, person=person, event=event, book=book_name)

            # 创建人物与地点的关系
            for person in entities['persons']:
                for place in entities['places']:
                    if person in text and place in text:
                        sentences = re.split(r'[。！？]', text)
                        for sentence in sentences:
                            if person in sentence and place in sentence:
                                session.run("""
                                    MATCH (p:Person {name: $person})
                                    MATCH (l:Location {name: $place})
                                    MERGE (p)-[r:ASSOCIATED_WITH]->(l)
                                    SET r.confidence = 0.7,
                                        r.sources = CASE WHEN r.sources IS NULL THEN [$book]
                                                   WHEN NOT $book IN r.sources THEN r.sources + $book
                                                   ELSE r.sources END
                                """, person=person, place=place, book=book_name)
    
    def process_historical_book(self, json_file: str, book_name: str, max_chapters: int = 3):
        """处理历史书籍JSON数据"""
        print(f"📚 开始处理{book_name}数据: {json_file}")

        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        total_entities = {'persons': set(), 'places': set(), 'events': set(), 'dynasties': set()}

        # 处理每一章
        chapters_to_process = min(len(data['chapters']), max_chapters)
        for i, chapter in enumerate(data['chapters'][:chapters_to_process]):
            chapter_name = chapter['name']
            print(f"📖 处理章节: {chapter_name}")

            # 合并章节中的所有段落
            chapter_text = ""
            for para in chapter['paragraphs']:
                # 跳过标题（type为1）或没有type字段的默认为正文
                if para.get('type', 0) != 1:
                    chapter_text += para['paragraph']

            # 提取实体
            entities = self.extract_entities_from_text(chapter_text)

            # 统计实体
            for key in total_entities:
                total_entities[key].update(entities[key])

            print(f"  📊 提取实体: 人物{len(entities['persons'])}个, 地点{len(entities['places'])}个, 事件{len(entities['events'])}个")

            # 创建实体和关系（添加书籍标识）
            self.create_entities_in_neo4j_with_book(entities, chapter_name, book_name)
            self.create_relationships_with_book(chapter_text, entities, book_name)

        print(f"\n🎯 {book_name}总计提取:")
        print(f"  👤 人物: {len(total_entities['persons'])}个")
        print(f"  🏛️ 地点: {len(total_entities['places'])}个")
        print(f"  ⚔️ 事件: {len(total_entities['events'])}个")
        print(f"  🏰 朝代: {len(total_entities['dynasties'])}个")

        return total_entities
    
    def query_sample_data(self):
        """查询示例数据"""
        print("\n🔍 查询示例数据:")
        
        with self.driver.session() as session:
            # 查询所有人物
            result = session.run("MATCH (p:Person) RETURN p.name as name LIMIT 10")
            persons = [record["name"] for record in result]
            print(f"👤 人物示例: {persons}")
            
            # 查询关系
            result = session.run("""
                MATCH (p:Person)-[r]->(target) 
                RETURN p.name as person, type(r) as relation, target.name as target 
                LIMIT 5
            """)
            relations = [(record["person"], record["relation"], record["target"]) for record in result]
            print(f"🔗 关系示例: {relations}")

def main():
    """主函数"""
    processor = SixStarsProcessor()

    try:
        # 测试连接
        if not processor.test_connection():
            return

        # 清空数据库（可选）
        # processor.clear_database()

        # 要处理的史书列表
        books_to_process = [
            ('cleaned_shishu/史记.json', '史记'),
            ('cleaned_shishu/汉书.json', '汉书'),
            ('cleaned_shishu/后汉书.json', '后汉书'),
            ('cleaned_shishu/三国志.json', '三国志')
        ]

        total_stats = {'persons': 0, 'places': 0, 'events': 0, 'dynasties': 0}

        # 处理每本史书
        for json_file, book_name in books_to_process:
            try:
                print(f"\n{'='*50}")
                entities = processor.process_historical_book(json_file, book_name, max_chapters=2)

                # 累计统计
                for key in total_stats:
                    total_stats[key] += len(entities[key])

            except FileNotFoundError:
                print(f"⚠️ 文件未找到: {json_file}")
                continue
            except Exception as e:
                print(f"❌ 处理{book_name}时出错: {e}")
                continue

        print(f"\n{'='*50}")
        print(f"🎉 所有史书处理完成！")
        print(f"📊 总计统计:")
        print(f"  👤 人物: {total_stats['persons']}个")
        print(f"  🏛️ 地点: {total_stats['places']}个")
        print(f"  ⚔️ 事件: {total_stats['events']}个")
        print(f"  🏰 朝代: {total_stats['dynasties']}个")

        # 查询示例数据
        processor.query_sample_data()

        print(f"\n✅ 六行星知识图谱构建完成！")
        print(f"🌐 访问Neo4j浏览器: http://localhost:7474")
        print(f"👤 用户名: neo4j")
        print(f"🔑 密码: sixstars123")

    except Exception as e:
        print(f"❌ 处理出错: {e}")
    finally:
        processor.close()

if __name__ == "__main__":
    main()
