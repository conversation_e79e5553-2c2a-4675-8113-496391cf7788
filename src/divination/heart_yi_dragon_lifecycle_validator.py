#!/usr/bin/env python3
"""
六行星知识图谱 - 《心易雕龙歌》生命周期验证分析器
用数据验证原创哲学框架的深层智慧
"""

from neo4j import GraphDatabase
import os
import json
import re
from collections import defaultdict, Counter
import numpy as np
from datetime import datetime

class HeartYiDragonLifecycleValidator:
    def __init__(self, uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"), user=os.getenv("NEO4J_USER", "neo4j"), password=os.getenv("NEO4J_PASSWORD", "sixstars123")):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"🔗 连接到六行星知识图谱")
        print(f"🐉 启动《心易雕龙歌》生命周期验证分析")
        print(f"🎯 目标：用数据验证原创哲学框架")
        
        # 定义十二龙子的完整生命周期框架
        self.twelve_dragons_lifecycle = {
            # 【生】阶段 - 创生期
            'creation_phase': {
                'dragons': {
                    '囚牛': {'attribute': '礼乐', 'function': '建立秩序', 'keywords': ['礼', '乐', '制', '序', '建']},
                    '睚眦': {'attribute': '战争', 'function': '开疆拓土', 'keywords': ['战', '征', '伐', '攻', '取']},
                    '狻猊': {'attribute': '智慧', 'function': '创新思想', 'keywords': ['智', '慧', '学', '思', '创']},
                    '蒲牢': {'attribute': '声响', 'function': '传播影响', 'keywords': ['声', '名', '传', '播', '扬']}
                },
                'phase_keywords': ['始', '初', '创', '新', '立', '建', '开', '起']
            },
            # 【住】阶段 - 成长期
            'growth_phase': {
                'dragons': {
                    '嘲风': {'attribute': '风险', 'function': '应对挑战', 'keywords': ['险', '难', '应', '对', '变']},
                    '狴犴': {'attribute': '司法', 'function': '维护正义', 'keywords': ['法', '律', '正', '义', '判']},
                    '负屃': {'attribute': '文学', 'function': '文化繁荣', 'keywords': ['文', '学', '诗', '书', '艺']},
                    '螭吻': {'attribute': '吞噬', 'function': '扩张整合', 'keywords': ['吞', '并', '合', '扩', '大']}
                },
                'phase_keywords': ['长', '大', '盛', '旺', '强', '壮', '兴', '隆']
            },
            # 【坏】阶段 - 衰退期
            'decline_phase': {
                'dragons': {
                    '蚣蝮': {'attribute': '守成', 'function': '保守维持', 'keywords': ['守', '保', '维', '持', '稳']},
                    '饕餮': {'attribute': '储备', 'function': '积累资源', 'keywords': ['积', '储', '藏', '聚', '备']}
                },
                'phase_keywords': ['衰', '弱', '退', '减', '损', '败', '落', '守']
            },
            # 【灭】阶段 - 终结期
            'destruction_phase': {
                'dragons': {
                    '貔貅': {'attribute': '隔绝', 'function': '封闭自守', 'keywords': ['隔', '绝', '闭', '避', '隐']},
                    '椒图': {'attribute': '封闭', 'function': '完全封锁', 'keywords': ['封', '锁', '闭', '绝', '终']}
                },
                'phase_keywords': ['灭', '终', '死', '亡', '绝', '尽', '完', '毕']
            }
        }
        
        # 五行与龙子的对应关系
        self.wuxing_dragons_mapping = {
            '木': ['囚牛', '狻猊'],  # 生长、智慧
            '火': ['睚眦', '蒲牢'],  # 战争、声响
            '土': ['嘲风', '狴犴'],  # 稳定、司法
            '金': ['负屃', '螭吻'],  # 文化、收敛
            '水': ['蚣蝮', '饕餮', '貔貅', '椒图']  # 流动、储藏、终结
        }
        
        # 历史朝代的五德属性
        self.dynasty_wude = {
            '夏': '木',
            '商': '金', 
            '周': '火',
            '秦': '水',
            '汉': '土',
            '魏': '土',
            '晋': '金',
            '隋': '火',
            '唐': '土',
            '宋': '火',
            '元': '金',
            '明': '火',
            '清': '水'
        }
    
    def close(self):
        self.driver.close()
    
    def classify_events_by_dragon_phases(self):
        """问题19: 将历史事件按龙子阶段分类"""
        print("\n🐲 === 问题19: 龙子方法工具箱分析 ===")
        
        with self.driver.session() as session:
            # 获取所有历史事件
            result = session.run("""
                MATCH (e:Event)
                WHERE e.books IS NOT NULL
                RETURN e.name as event_name, e.books as books
            """)
            
            events_by_phase = {
                'creation_phase': [],
                'growth_phase': [],
                'decline_phase': [],
                'destruction_phase': []
            }
            
            for record in result:
                event_name = record['event_name']
                books = record['books']
                
                # 根据事件名称中的关键词分类到不同阶段
                for phase, phase_info in self.twelve_dragons_lifecycle.items():
                    phase_keywords = phase_info['phase_keywords']
                    
                    for keyword in phase_keywords:
                        if keyword in event_name:
                            events_by_phase[phase].append({
                                'event': event_name,
                                'books': books,
                                'matched_keyword': keyword
                            })
                            break
            
            # 统计每个阶段的术数方法
            phase_methods = {}
            for phase, events in events_by_phase.items():
                print(f"\n🐉 {phase} 阶段:")
                print(f"   📊 相关事件: {len(events)}个")
                
                # 获取该阶段相关的术数方法
                phase_info = self.twelve_dragons_lifecycle[phase]
                all_keywords = phase_info['phase_keywords']
                
                # 扩展关键词包括龙子的特定关键词
                for dragon_info in phase_info['dragons'].values():
                    all_keywords.extend(dragon_info['keywords'])
                
                result = session.run("""
                    MATCH (m:ShushuMethod)
                    WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                    RETURN m.name as method, count(*) as relevance
                    ORDER BY relevance DESC
                    LIMIT 10
                """, keywords=all_keywords)
                
                methods = []
                for record in result:
                    methods.append({
                        'method': record['method'],
                        'relevance': record['relevance']
                    })
                
                phase_methods[phase] = methods
                
                print(f"   🔮 相关术数方法:")
                for method in methods[:5]:
                    print(f"      - {method['method']} (相关度: {method['relevance']})")
                
                # 特别分析睚眦阶段与战争占卜的关系
                if phase == 'creation_phase':
                    war_methods = session.run("""
                        MATCH (m:ShushuMethod)
                        WHERE m.name CONTAINS '战' OR m.name CONTAINS '征' OR m.name CONTAINS '伐'
                        RETURN count(m) as war_method_count
                    """).single()['war_method_count']
                    
                    print(f"   ⚔️ 睚眦阶段战争占卜方法: {war_methods}个")
            
            return events_by_phase, phase_methods
    
    def analyze_creation_phase_concepts(self):
        """问题20: 分析【生】阶段的特征概念"""
        print("\n🌱 === 问题20: 【生】阶段特征分析 ===")
        
        with self.driver.session() as session:
            # 查找河图、洛书等基础概念
            foundational_concepts = ['河图', '洛书', '太极', '无极', '先天', '后天']
            
            concept_activity = {}
            for concept in foundational_concepts:
                result = session.run("""
                    MATCH (c:ShushuConcept)
                    WHERE c.name CONTAINS $concept
                    OPTIONAL MATCH (c)-[r]-(m:ShushuMethod)
                    RETURN c.name as concept_name, count(m) as method_connections
                """, concept=concept)
                
                for record in result:
                    if record['concept_name']:
                        concept_activity[record['concept_name']] = record['method_connections']
            
            # 分析囚牛（礼乐）和狻猊（智慧）相关的概念
            creation_keywords = ['礼', '乐', '智', '慧', '序', '制']
            
            result = session.run("""
                MATCH (c:ShushuConcept)
                WHERE any(keyword IN $keywords WHERE c.name CONTAINS keyword)
                OPTIONAL MATCH (c)-[r]-(m:ShushuMethod)
                RETURN c.name as concept, count(m) as connections
                ORDER BY connections DESC
                LIMIT 10
            """, keywords=creation_keywords)
            
            creation_concepts = []
            for record in result:
                creation_concepts.append({
                    'concept': record['concept'],
                    'connections': record['connections']
                })
            
            print(f"📊 【生】阶段概念活跃度分析:")
            print(f"   🏛️ 基础宇宙概念:")
            for concept, activity in sorted(concept_activity.items(), key=lambda x: x[1], reverse=True):
                print(f"      {concept}: {activity}个方法连接")
            
            print(f"   🎭 囚牛/狻猊相关概念:")
            for concept_info in creation_concepts[:5]:
                print(f"      {concept_info['concept']}: {concept_info['connections']}个连接")
            
            return concept_activity, creation_concepts
    
    def analyze_growth_phase_methods(self):
        """问题21: 分析【住】阶段的特征方法"""
        print("\n🌳 === 问题21: 【住】阶段特征分析 ===")
        
        with self.driver.session() as session:
            # 分析嘲风（风险）和狴犴（司法）相关的方法
            growth_keywords = ['吉', '凶', '利', '害', '避', '趋', '法', '律', '判', '断']
            
            result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN m.name as method, m.books as books
                ORDER BY size(m.books) DESC
                LIMIT 15
            """, keywords=growth_keywords)
            
            growth_methods = []
            for record in result:
                growth_methods.append({
                    'method': record['method'],
                    'books': record['books'] if record['books'] else []
                })
            
            # 统计吉凶占卜的频率
            jixiong_count = session.run("""
                MATCH (m:ShushuMethod)
                WHERE m.name CONTAINS '吉' OR m.name CONTAINS '凶'
                RETURN count(m) as jixiong_methods
            """).single()['jixiong_methods']
            
            # 统计趋利避害的频率
            quli_count = session.run("""
                MATCH (m:ShushuMethod)
                WHERE m.name CONTAINS '利' OR m.name CONTAINS '避' OR m.name CONTAINS '趋'
                RETURN count(m) as quli_methods
            """).single()['quli_methods']
            
            print(f"📊 【住】阶段方法特征:")
            print(f"   ⚖️ 吉凶占卜方法: {jixiong_count}个")
            print(f"   🎯 趋利避害方法: {quli_count}个")
            print(f"   🔮 主要【住】阶段方法:")
            
            for method in growth_methods[:8]:
                books_str = ', '.join(method['books'][:2]) if method['books'] else '无'
                print(f"      - {method['method']} ({books_str})")
            
            return growth_methods, jixiong_count, quli_count
    
    def analyze_decline_phase_characteristics(self):
        """问题22: 分析【坏】阶段的特征"""
        print("\n📉 === 问题22: 【坏】阶段特征分析 ===")
        
        with self.driver.session() as session:
            # 分析"守"vs"攻"的方法倾向
            defensive_keywords = ['守', '保', '维', '持', '稳', '藏', '聚']
            offensive_keywords = ['攻', '进', '取', '征', '伐', '扩', '张']
            
            defensive_count = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN count(m) as defensive_methods
            """, keywords=defensive_keywords).single()['defensive_methods']
            
            offensive_count = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN count(m) as offensive_methods
            """, keywords=offensive_keywords).single()['offensive_methods']
            
            # 查找风水中的"藏风聚气"类方法
            fengshui_methods = session.run("""
                MATCH (m:ShushuMethod)
                WHERE (m.name CONTAINS '风水' OR m.name CONTAINS '堪舆')
                AND (m.name CONTAINS '藏' OR m.name CONTAINS '聚' OR m.name CONTAINS '守')
                RETURN m.name as method, m.books as books
                LIMIT 10
            """)
            
            fengshui_defensive = []
            for record in fengshui_methods:
                fengshui_defensive.append({
                    'method': record['method'],
                    'books': record['books'] if record['books'] else []
                })
            
            defensive_ratio = defensive_count / (defensive_count + offensive_count) if (defensive_count + offensive_count) > 0 else 0
            
            print(f"📊 【坏】阶段守攻倾向分析:")
            print(f"   🛡️ 守成方法: {defensive_count}个")
            print(f"   ⚔️ 进攻方法: {offensive_count}个")
            print(f"   📈 守成倾向比例: {defensive_ratio:.2%}")
            print(f"   🌬️ 风水藏风聚气方法: {len(fengshui_defensive)}个")
            
            for method in fengshui_defensive[:5]:
                books_str = ', '.join(method['books'][:2]) if method['books'] else '无'
                print(f"      - {method['method']} ({books_str})")
            
            return defensive_count, offensive_count, fengshui_defensive
    
    def analyze_destruction_phase_characteristics(self):
        """问题23: 分析【灭】阶段的特征"""
        print("\n💀 === 问题23: 【灭】阶段特征分析 ===")
        
        with self.driver.session() as session:
            # 查找归隐、避世相关的方法
            hermit_keywords = ['隐', '避', '退', '遁', '归', '隔', '绝', '闭']
            
            result = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN m.name as method, m.books as books
                LIMIT 10
            """, keywords=hermit_keywords)
            
            hermit_methods = []
            for record in result:
                hermit_methods.append({
                    'method': record['method'],
                    'books': record['books'] if record['books'] else []
                })
            
            # 查找终结、完结相关的理论
            ending_keywords = ['终', '完', '毕', '尽', '绝', '死', '亡']
            
            result = session.run("""
                MATCH (c:ShushuConcept)
                WHERE any(keyword IN $keywords WHERE c.name CONTAINS keyword)
                RETURN c.name as concept, c.books as books
                LIMIT 5
            """, keywords=ending_keywords)
            
            ending_concepts = []
            for record in result:
                ending_concepts.append({
                    'concept': record['concept'],
                    'books': record['books'] if record['books'] else []
                })
            
            print(f"📊 【灭】阶段特征分析:")
            print(f"   🏔️ 归隐避世方法: {len(hermit_methods)}个")
            for method in hermit_methods[:5]:
                books_str = ', '.join(method['books'][:2]) if method['books'] else '无'
                print(f"      - {method['method']} ({books_str})")
            
            print(f"   💀 终结相关概念: {len(ending_concepts)}个")
            for concept in ending_concepts:
                books_str = ', '.join(concept['books'][:2]) if concept['books'] else '无'
                print(f"      - {concept['concept']} ({books_str})")
            
            return hermit_methods, ending_concepts
    
    def match_dragons_with_wuxing(self):
        """问题24: 龙子与五行的匹配验证"""
        print("\n🌟 === 问题24: 龙子与五行匹配验证 ===")
        
        with self.driver.session() as session:
            wuxing_validation = {}
            
            for wuxing, dragons in self.wuxing_dragons_mapping.items():
                print(f"\n🔥 {wuxing}行龙子: {', '.join(dragons)}")
                
                # 查找该五行相关的术数方法
                result = session.run("""
                    MATCH (m:ShushuMethod)
                    WHERE m.name CONTAINS $wuxing
                    RETURN count(m) as method_count
                """, wuxing=wuxing)
                
                method_count = result.single()['method_count']
                
                # 查找该五行相关的概念
                result = session.run("""
                    MATCH (c:ShushuConcept)
                    WHERE c.name CONTAINS $wuxing
                    RETURN count(c) as concept_count
                """, wuxing=wuxing)
                
                concept_count = result.single()['concept_count']
                
                wuxing_validation[wuxing] = {
                    'dragons': dragons,
                    'method_count': method_count,
                    'concept_count': concept_count,
                    'total_influence': method_count + concept_count
                }
                
                print(f"   📊 相关方法: {method_count}个")
                print(f"   💡 相关概念: {concept_count}个")
                print(f"   🎯 总影响力: {method_count + concept_count}")
            
            # 验证历史朝代的五德特征
            print(f"\n👑 历史朝代五德验证:")
            for dynasty, wude in self.dynasty_wude.items():
                if wude in wuxing_validation:
                    influence = wuxing_validation[wude]['total_influence']
                    dragons = wuxing_validation[wude]['dragons']
                    print(f"   {dynasty}朝({wude}德): 对应龙子{dragons}, 术数影响力{influence}")
            
            return wuxing_validation
    
    def find_lifecycle_turning_points(self):
        """问题25: 寻找生命周期拐点"""
        print("\n🔄 === 问题25: 生命周期拐点分析 ===")
        
        # 定义历史上的重要拐点事件
        turning_points = {
            '秦统一六国': {'year': -221, 'type': 'creation_to_growth'},
            '汉武帝即位': {'year': -140, 'type': 'growth_peak'},
            '王莽篡汉': {'year': 9, 'type': 'growth_to_decline'},
            '东汉末年': {'year': 184, 'type': 'decline_to_destruction'},
            '三国鼎立': {'year': 220, 'type': 'destruction_to_creation'}
        }
        
        with self.driver.session() as session:
            turning_point_analysis = {}
            
            for event, info in turning_points.items():
                print(f"\n🎯 拐点事件: {event} ({info['year']}年)")
                
                # 查找该时期相关的术数概念爆发
                # 这里用简化的方法，通过事件名称关键词查找
                event_keywords = event.split('·')[0]  # 取主要关键词
                
                result = session.run("""
                    MATCH (c:ShushuConcept)
                    WHERE any(word IN split($event, '') WHERE c.name CONTAINS word)
                    RETURN c.name as concept, c.books as books
                    LIMIT 5
                """, event=event_keywords)
                
                related_concepts = []
                for record in result:
                    related_concepts.append({
                        'concept': record['concept'],
                        'books': record['books'] if record['books'] else []
                    })
                
                turning_point_analysis[event] = {
                    'year': info['year'],
                    'transition_type': info['type'],
                    'related_concepts': related_concepts
                }
                
                print(f"   🔄 转换类型: {info['type']}")
                print(f"   💡 相关概念爆发:")
                for concept in related_concepts:
                    books_str = ', '.join(concept['books'][:2]) if concept['books'] else '无'
                    print(f"      - {concept['concept']} ({books_str})")
            
            return turning_point_analysis
    
    def calculate_lifecycle_duration(self):
        """问题26: 计算周期长度"""
        print("\n⏰ === 问题26: 生命周期长度分析 ===")
        
        # 主要朝代的存续时间
        dynasty_durations = {
            '夏': 471,    # 约公元前2070-1600年
            '商': 554,    # 约公元前1600-1046年  
            '周': 791,    # 公元前1046-256年
            '秦': 15,     # 公元前221-206年
            '汉': 426,    # 公元前206年-220年（包括新朝间断）
            '魏': 45,     # 220-265年
            '晋': 155,    # 265-420年
            '隋': 37,     # 581-618年
            '唐': 289,    # 618-907年
            '宋': 319,    # 960-1279年
            '元': 97,     # 1271-1368年
            '明': 276,    # 1368-1644年
            '清': 268     # 1644-1912年
        }
        
        # 计算平均周期长度
        total_duration = sum(dynasty_durations.values())
        dynasty_count = len(dynasty_durations)
        average_duration = total_duration / dynasty_count
        
        # 分析不同类型的周期
        major_dynasties = ['夏', '商', '周', '汉', '唐', '宋', '明', '清']  # 主要朝代
        minor_dynasties = ['秦', '魏', '晋', '隋', '元']  # 过渡朝代
        
        major_avg = sum(dynasty_durations[d] for d in major_dynasties) / len(major_dynasties)
        minor_avg = sum(dynasty_durations[d] for d in minor_dynasties) / len(minor_dynasties)
        
        print(f"📊 朝代生命周期统计:")
        print(f"   📈 总体平均周期: {average_duration:.1f}年")
        print(f"   👑 主要朝代平均: {major_avg:.1f}年")
        print(f"   🔄 过渡朝代平均: {minor_avg:.1f}年")
        
        # 寻找康波周期模式
        long_cycles = [d for d, duration in dynasty_durations.items() if duration > 250]
        short_cycles = [d for d, duration in dynasty_durations.items() if duration < 100]
        
        print(f"   📏 长周期朝代 (>250年): {', '.join(long_cycles)}")
        print(f"   ⚡ 短周期朝代 (<100年): {', '.join(short_cycles)}")
        
        # 计算标准差
        durations = list(dynasty_durations.values())
        std_dev = np.std(durations)
        print(f"   📊 周期标准差: {std_dev:.1f}年")
        
        return dynasty_durations, average_duration, std_dev
    
    def design_conservatism_index(self):
        """问题27: 设计保守性/内卷度指标"""
        print("\n📐 === 问题27: 保守性内卷度指标设计 ===")
        
        with self.driver.session() as session:
            # 设计保守性指标的组成要素
            conservatism_factors = {
                'defensive_methods_ratio': 0,      # 守成方法比例
                'repetitive_concepts_ratio': 0,    # 重复概念比例  
                'innovation_decline_ratio': 0,     # 创新衰减比例
                'isolation_tendency': 0            # 隔绝倾向
            }
            
            # 1. 计算守成方法比例
            defensive_keywords = ['守', '保', '维', '持', '稳', '藏']
            total_methods = session.run("MATCH (m:ShushuMethod) RETURN count(m) as total").single()['total']
            
            defensive_methods = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN count(m) as defensive_count
            """, keywords=defensive_keywords).single()['defensive_count']
            
            conservatism_factors['defensive_methods_ratio'] = defensive_methods / total_methods if total_methods > 0 else 0
            
            # 2. 计算重复概念比例（简化版本）
            total_concepts = session.run("MATCH (c:ShushuConcept) RETURN count(c) as total").single()['total']
            unique_concepts = session.run("MATCH (c:ShushuConcept) RETURN count(DISTINCT c.name) as unique").single()['unique']
            
            conservatism_factors['repetitive_concepts_ratio'] = 1 - (unique_concepts / total_concepts) if total_concepts > 0 else 0
            
            # 3. 计算隔绝倾向
            isolation_keywords = ['隔', '绝', '闭', '避', '隐', '遁']
            isolation_methods = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN count(m) as isolation_count
            """, keywords=isolation_keywords).single()['isolation_count']
            
            conservatism_factors['isolation_tendency'] = isolation_methods / total_methods if total_methods > 0 else 0
            
            # 4. 创新衰减比例（基于创新相关方法的稀少程度）
            innovation_keywords = ['新', '创', '变', '革', '改']
            innovation_methods = session.run("""
                MATCH (m:ShushuMethod)
                WHERE any(keyword IN $keywords WHERE m.name CONTAINS keyword)
                RETURN count(m) as innovation_count
            """, keywords=innovation_keywords).single()['innovation_count']
            
            conservatism_factors['innovation_decline_ratio'] = 1 - (innovation_methods / total_methods) if total_methods > 0 else 1
            
            # 计算综合保守性指数
            conservatism_index = (
                conservatism_factors['defensive_methods_ratio'] * 0.3 +
                conservatism_factors['repetitive_concepts_ratio'] * 0.2 +
                conservatism_factors['innovation_decline_ratio'] * 0.3 +
                conservatism_factors['isolation_tendency'] * 0.2
            )
            
            print(f"📊 保守性/内卷度指标分析:")
            print(f"   🛡️ 守成方法比例: {conservatism_factors['defensive_methods_ratio']:.3f}")
            print(f"   🔄 重复概念比例: {conservatism_factors['repetitive_concepts_ratio']:.3f}")
            print(f"   📉 创新衰减比例: {conservatism_factors['innovation_decline_ratio']:.3f}")
            print(f"   🏝️ 隔绝倾向指数: {conservatism_factors['isolation_tendency']:.3f}")
            print(f"   📐 综合保守性指数: {conservatism_index:.3f}")
            
            # 验证"九子归边"假说
            if conservatism_index > 0.6:
                conclusion = "高度内卷，系统趋向'气数殁'"
            elif conservatism_index > 0.4:
                conclusion = "中度保守，需要警惕内卷风险"
            else:
                conclusion = "相对开放，系统仍有活力"
            
            print(f"   🎯 系统状态判断: {conclusion}")
            
            return conservatism_factors, conservatism_index

def main():
    """主函数"""
    validator = HeartYiDragonLifecycleValidator()
    
    try:
        print("🌟 开始《心易雕龙歌》生命周期验证分析...")
        
        # 回答9个核心问题
        results = {}
        
        # 问题19-27
        events_by_phase, phase_methods = validator.classify_events_by_dragon_phases()
        results['dragon_phase_classification'] = {'events': events_by_phase, 'methods': phase_methods}
        
        concept_activity, creation_concepts = validator.analyze_creation_phase_concepts()
        results['creation_phase_analysis'] = {'foundational_concepts': concept_activity, 'creation_concepts': creation_concepts}
        
        growth_methods, jixiong_count, quli_count = validator.analyze_growth_phase_methods()
        results['growth_phase_analysis'] = {'methods': growth_methods, 'jixiong_count': jixiong_count, 'quli_count': quli_count}
        
        defensive_count, offensive_count, fengshui_defensive = validator.analyze_decline_phase_characteristics()
        results['decline_phase_analysis'] = {'defensive_count': defensive_count, 'offensive_count': offensive_count, 'fengshui_methods': fengshui_defensive}
        
        hermit_methods, ending_concepts = validator.analyze_destruction_phase_characteristics()
        results['destruction_phase_analysis'] = {'hermit_methods': hermit_methods, 'ending_concepts': ending_concepts}
        
        wuxing_validation = validator.match_dragons_with_wuxing()
        results['wuxing_dragon_matching'] = wuxing_validation
        
        turning_points = validator.find_lifecycle_turning_points()
        results['lifecycle_turning_points'] = turning_points
        
        dynasty_durations, avg_duration, std_dev = validator.calculate_lifecycle_duration()
        results['lifecycle_duration'] = {'durations': dynasty_durations, 'average': avg_duration, 'std_dev': std_dev}
        
        conservatism_factors, conservatism_index = validator.design_conservatism_index()
        results['conservatism_analysis'] = {'factors': conservatism_factors, 'index': conservatism_index}
        
        # 保存验证结果
        with open('heart_yi_dragon_lifecycle_validation.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✨ 《心易雕龙歌》生命周期验证分析完成！")
        print(f"📄 详细结果已保存到: heart_yi_dragon_lifecycle_validation.json")
        print(f"🎯 保守性指数: {conservatism_index:.3f}")
        
    except Exception as e:
        print(f"❌ 验证分析出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        validator.close()

if __name__ == "__main__":
    main()
