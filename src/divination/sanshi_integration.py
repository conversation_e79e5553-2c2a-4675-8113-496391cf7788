#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六行星知识图谱 - 三式占卜系统集成
整合六壬、奇门、太乙三式占卜系统到六行星框架中
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

# 添加shushu目录到Python路径
project_root = Path(__file__).parent.parent.parent
shushu_path = project_root / "shushu"
sys.path.insert(0, str(shushu_path))

class SanshiDivinationSystem:
    """三式占卜系统集成类"""
    
    def __init__(self):
        self.available_systems = {}
        self.initialize_systems()
    
    def initialize_systems(self):
        """初始化三式系统"""
        print("🔮 初始化三式占卜系统...")
        
        # 初始化六壬系统
        try:
            from kinliuren.kinliuren import kinliuren
            from kinliuren.jieqi import *
            self.available_systems['liuren'] = True
            print("✅ 六壬系统加载成功")
        except ImportError as e:
            self.available_systems['liuren'] = False
            print(f"❌ 六壬系统加载失败: {e}")
        
        # 初始化奇门系统
        try:
            from kinqimen import kinqimen
            from kinqimen import config as qimen_config
            self.available_systems['qimen'] = True
            print("✅ 奇门系统加载成功")
        except ImportError as e:
            self.available_systems['qimen'] = False
            print(f"❌ 奇门系统加载失败: {e}")
        
        # 初始化太乙系统
        try:
            from kintaiyi import kintaiyi
            from kintaiyi import config as taiyi_config
            from kintaiyi.taiyidict import tengan_shiji, su_dist
            from kintaiyi.taiyimishu import taiyi_yingyang
            from kintaiyi.historytext import chistory
            self.available_systems['taiyi'] = True
            print("✅ 太乙系统加载成功")
        except ImportError as e:
            self.available_systems['taiyi'] = False
            print(f"❌ 太乙系统加载失败: {e}")
        
        print(f"🎯 可用系统: {[k for k, v in self.available_systems.items() if v]}")
    
    def get_available_systems(self) -> List[str]:
        """获取可用的占卜系统列表"""
        return [system for system, available in self.available_systems.items() if available]
    
    def divine_liuren(self, question: str, datetime_obj: datetime = None) -> Dict[str, Any]:
        """六壬占卜"""
        if not self.available_systems.get('liuren', False):
            return {"error": "六壬系统不可用"}
        
        try:
            from kinliuren.kinliuren import kinliuren
            
            if datetime_obj is None:
                datetime_obj = datetime.now()
            
            # 调用六壬排盘
            result = kinliuren(
                year=datetime_obj.year,
                month=datetime_obj.month,
                day=datetime_obj.day,
                hour=datetime_obj.hour,
                minute=datetime_obj.minute
            )
            
            return {
                "system": "六壬",
                "question": question,
                "datetime": datetime_obj.isoformat(),
                "result": result,
                "interpretation": self._interpret_liuren_result(result)
            }
        except Exception as e:
            return {"error": f"六壬占卜失败: {str(e)}"}
    
    def divine_qimen(self, question: str, datetime_obj: datetime = None) -> Dict[str, Any]:
        """奇门遁甲占卜"""
        if not self.available_systems.get('qimen', False):
            return {"error": "奇门系统不可用"}
        
        try:
            from kinqimen import kinqimen
            
            if datetime_obj is None:
                datetime_obj = datetime.now()
            
            # 调用奇门排盘
            result = kinqimen.qimen_paipan(
                year=datetime_obj.year,
                month=datetime_obj.month,
                day=datetime_obj.day,
                hour=datetime_obj.hour,
                minute=datetime_obj.minute
            )
            
            return {
                "system": "奇门遁甲",
                "question": question,
                "datetime": datetime_obj.isoformat(),
                "result": result,
                "interpretation": self._interpret_qimen_result(result)
            }
        except Exception as e:
            return {"error": f"奇门占卜失败: {str(e)}"}
    
    def divine_taiyi(self, question: str, datetime_obj: datetime = None) -> Dict[str, Any]:
        """太乙神数占卜"""
        if not self.available_systems.get('taiyi', False):
            return {"error": "太乙系统不可用"}
        
        try:
            from kintaiyi import kintaiyi
            
            if datetime_obj is None:
                datetime_obj = datetime.now()
            
            # 调用太乙排盘
            result = kintaiyi.taiyi_paipan(
                year=datetime_obj.year,
                month=datetime_obj.month,
                day=datetime_obj.day,
                hour=datetime_obj.hour,
                minute=datetime_obj.minute
            )
            
            return {
                "system": "太乙神数",
                "question": question,
                "datetime": datetime_obj.isoformat(),
                "result": result,
                "interpretation": self._interpret_taiyi_result(result)
            }
        except Exception as e:
            return {"error": f"太乙占卜失败: {str(e)}"}
    
    def divine_comprehensive(self, question: str, datetime_obj: datetime = None) -> Dict[str, Any]:
        """综合三式占卜"""
        if datetime_obj is None:
            datetime_obj = datetime.now()
        
        results = {}
        
        # 依次进行三式占卜
        if self.available_systems.get('liuren', False):
            results['liuren'] = self.divine_liuren(question, datetime_obj)
        
        if self.available_systems.get('qimen', False):
            results['qimen'] = self.divine_qimen(question, datetime_obj)
        
        if self.available_systems.get('taiyi', False):
            results['taiyi'] = self.divine_taiyi(question, datetime_obj)
        
        # 综合分析
        comprehensive_analysis = self._comprehensive_analysis(results, question)
        
        return {
            "question": question,
            "datetime": datetime_obj.isoformat(),
            "individual_results": results,
            "comprehensive_analysis": comprehensive_analysis,
            "six_stars_integration": self._integrate_with_six_stars(results)
        }
    
    def _interpret_liuren_result(self, result: Any) -> str:
        """解释六壬结果"""
        # 这里可以添加更详细的六壬解释逻辑
        return "六壬占卜结果解释（待完善）"
    
    def _interpret_qimen_result(self, result: Any) -> str:
        """解释奇门结果"""
        # 这里可以添加更详细的奇门解释逻辑
        return "奇门遁甲结果解释（待完善）"
    
    def _interpret_taiyi_result(self, result: Any) -> str:
        """解释太乙结果"""
        # 这里可以添加更详细的太乙解释逻辑
        return "太乙神数结果解释（待完善）"
    
    def _comprehensive_analysis(self, results: Dict[str, Any], question: str) -> str:
        """综合分析三式结果"""
        available_results = [k for k, v in results.items() if 'error' not in v]
        
        if not available_results:
            return "无可用的占卜结果进行综合分析"
        
        analysis = f"基于{len(available_results)}个占卜系统的综合分析：\n"
        
        for system in available_results:
            analysis += f"- {results[system]['system']}: {results[system]['interpretation']}\n"
        
        analysis += "\n综合判断：三式占卜结果需要结合具体情况进行深入分析。"
        
        return analysis
    
    def _integrate_with_six_stars(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """与六行星理论集成"""
        integration = {
            "six_stars_correlation": "正在分析与六行星理论的关联...",
            "historical_dark_matter": "正在检测历史暗物质信号...",
            "dragon_pearl_analysis": "正在进行双龙戏珠分析..."
        }
        
        # 这里可以添加与六行星知识图谱的深度集成逻辑
        # 例如：查询相关历史事件、天象记录等
        
        return integration
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "available_systems": self.available_systems,
            "total_systems": len(self.available_systems),
            "active_systems": len([v for v in self.available_systems.values() if v]),
            "integration_status": "已集成到六行星知识图谱系统"
        }
