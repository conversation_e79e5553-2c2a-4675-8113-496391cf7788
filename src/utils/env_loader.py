#!/usr/bin/env python3
"""
环境变量加载器
确保项目中的环境变量正确加载
"""

import os
from pathlib import Path
from dotenv import load_dotenv

def load_project_env():
    """加载项目环境变量"""
    # 查找.env文件
    env_file = Path('.env')
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量: {env_file}")
    else:
        print(f"⚠️  .env文件不存在: {env_file}")
    
    # 验证关键环境变量
    required_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 所有必需的环境变量都已设置")
    return True

def get_neo4j_config():
    """获取Neo4j配置"""
    return {
        'uri': os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
        'user': os.getenv('NEO4J_USER', 'neo4j'),
        'password': os.getenv('NEO4J_PASSWORD', 'sixstars123')
    }

def get_api_config():
    """获取API配置"""
    return {
        'gemini_api_key': os.getenv('GEMINI_API_KEY'),
        'openai_api_key': os.getenv('OPENAI_API_KEY'),
        'anthropic_api_key': os.getenv('ANTHROPIC_API_KEY')
    }

if __name__ == "__main__":
    load_project_env()
