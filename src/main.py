#!/usr/bin/env python3
"""
六行星知识图谱项目主入口
"""

import argparse
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.neo4j_shiji_processor import SixStarsProcessor
from src.analyzers.historical_dark_matter_ai_predictor import HistoricalDarkMatterAIPredictor
from src.divination.sanshi_integration import SanshiDivinationSystem

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="六行星知识图谱系统")
    parser.add_argument("--mode", choices=["web", "analyze", "process", "test", "divination"],
                       default="web", help="运行模式")
    parser.add_argument("--neo4j-uri", default="bolt://localhost:7687", 
                       help="Neo4j数据库URI")
    parser.add_argument("--neo4j-user", default="neo4j", 
                       help="Neo4j用户名")
    parser.add_argument("--neo4j-password", default="sixstars123", 
                       help="Neo4j密码")
    parser.add_argument("--port", type=int, default=5000, 
                       help="Web服务端口")
    
    args = parser.parse_args()
    
    print("🌟 六行星知识图谱系统启动")
    print(f"📊 运行模式: {args.mode}")
    
    if args.mode == "test":
        # 测试Neo4j连接
        processor = SixStarsProcessor(args.neo4j_uri, args.neo4j_user, args.neo4j_password)
        if processor.test_connection():
            print("✅ 系统测试通过")
        else:
            print("❌ 系统测试失败")
        processor.close()
        
    elif args.mode == "process":
        # 数据处理模式
        processor = SixStarsProcessor(args.neo4j_uri, args.neo4j_user, args.neo4j_password)
        print("🔄 开始处理史记数据...")
        # 这里可以添加具体的数据处理逻辑
        processor.close()
        
    elif args.mode == "analyze":
        # 分析模式
        predictor = HistoricalDarkMatterAIPredictor(args.neo4j_uri, args.neo4j_user, args.neo4j_password)
        print("🤖 启动AI分析系统...")
        # 这里可以添加具体的分析逻辑
        predictor.close()

    elif args.mode == "divination":
        # 占卜模式
        print("🔮 启动三式占卜系统...")
        sanshi = SanshiDivinationSystem()

        # 显示系统状态
        status = sanshi.get_system_status()
        print(f"📊 系统状态: {status}")

        # 交互式占卜
        while True:
            question = input("\n请输入您的问题 (输入'quit'退出): ")
            if question.lower() == 'quit':
                break

            if question.strip():
                print("🔮 正在进行综合占卜...")
                result = sanshi.divine_comprehensive(question)
                print(f"\n📋 占卜结果:")
                print(f"问题: {result['question']}")
                print(f"时间: {result['datetime']}")
                print(f"综合分析: {result['comprehensive_analysis']}")
            else:
                print("❌ 问题不能为空")
        
    elif args.mode == "web":
        # Web服务模式
        try:
            from src.web.app import create_app
            app = create_app()
            print(f"🌐 启动Web服务，端口: {args.port}")
            app.run(host="0.0.0.0", port=args.port, debug=True)
        except ImportError:
            print("❌ Web模块未实现，请先实现src/web/app.py")
    
    print("👋 六行星知识图谱系统关闭")

if __name__ == "__main__":
    main()
