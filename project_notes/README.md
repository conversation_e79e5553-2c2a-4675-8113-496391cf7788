# 六行星知识图谱项目笔记

## 📁 目录结构

```
project_notes/
├── README.md                 # 本文件，项目笔记总览
├── work_log_20250719.md     # 2025-07-19 工作日志（胎阶段）
├── theory_framework.md      # 理论框架文档（待创建）
├── data_analysis/           # 数据分析报告（待创建）
├── technical_docs/          # 技术文档（待创建）
└── insights/               # 重要洞察记录（待创建）
```

## 🌱 项目发展阶段（十二长生）

| 阶段 | 状态 | 文档 | 说明 |
|------|------|------|------|
| 1. 胎 | ✅ 当前 | work_log_20250719.md | 基础数据建立，理论雏形 |
| 2. 养 | ⏳ 计划中 | work_log_YYYYMMDD.md | 术数书200本灌入 |
| 3. 长生 | ⏳ 未来 | | 道藏文本整合 |
| ... | | | |

## 📋 工作日志索引

- **2025-07-19**: [胎阶段工作日志](work_log_20250719.md) - 项目启动，前四史数据建立

## 🔑 关键概念

### 核心理论
- **六行星模型**: 五大行星 + 地球本身
- **双龙戏珠**: 文官(赤龙) vs 星官(玄龙) 围绕史权(龙珠)的博弈
- **历史暗物质**: 真正驱动历史但不被正史记录的力量
- **命理学主键**: 命理学作为跨学科连接的通用语言

### 数据基础
- **前四史**: 史记、汉书、后汉书、三国志 (18MB JSON数据)
- **天象记录**: 512条来自天文志、五行志的记录
- **知识图谱**: 1,486节点，516关系的历史网络

## 🎯 项目目标

### 短期目标
- 建立完整的历史文献知识图谱
- 验证古代天象与政治事件的关联
- 开发基于五行理论的历史分析工具

### 长期愿景
- 构建新的史学方法论
- 重新发现古代智慧的现代价值
- 为中华文明研究提供数字化基础设施

## 📊 项目指标追踪

### 数据规模
- 文本数据: 19.2MB
- 天象记录: 512条
- 知识图谱: 1,486节点
- 时间跨度: 3000年

### 技术指标
- 数据处理速度: 69章节/10分钟
- 抓取成功率: 53%
- 查询响应: <1秒

## 🔗 相关资源

### 数据源
- [ctext.org](https://ctext.org) - 中国哲学书电子化计划
- chinese-philosophy项目 - 前四史JSON数据
- Neo4j数据库 - 知识图谱存储

### 技术栈
- Python 3.13 + BeautifulSoup4
- Neo4j Community Edition
- Docker容器化部署

---

*最后更新: 2025-07-19*  
*项目状态: 胎阶段 (1/12)*