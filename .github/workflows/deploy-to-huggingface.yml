name: Deploy to Hugging Face Spaces

on:
  push:
    branches: [ main, master ]
    paths:
      - 'shushu/**'
      - 'tianquan-haystack/**'
      - '.github/workflows/deploy-to-huggingface.yml'
  workflow_dispatch:

jobs:
  deploy-streamlit:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        lfs: true

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install huggingface_hub

    - name: Prepare Streamlit app for HF Spaces
      run: |
        # 创建HF Spaces目录结构
        mkdir -p hf-spaces-streamlit
        
        # 复制Streamlit应用
        cp -r shushu/* hf-spaces-streamlit/
        
        # 创建HF Spaces配置文件
        cat > hf-spaces-streamlit/README.md << 'EOF'
        ---
        title: 六行星知识图谱 - 太公心易兜率宫
        emoji: 🏛️
        colorFrom: gold
        colorTo: red
        sdk: streamlit
        sdk_version: 1.28.0
        app_file: app.py
        pinned: false
        license: mit
        short_description: 中华传统术数排盘系统 - 六壬、奇门、太乙、易经、梅花易数
        ---
        
        # 六行星知识图谱 - 太公心易兜率宫
        
        🏛️ **中华传统术数排盘系统**
        
        ## 功能特色
        
        - 🔮 **大六壬排盘** - 使用kinliuren官方算法
        - ⭐ **奇门遁甲排盘** - 支持时家奇门和刻家奇门  
        - 🌟 **太乙神数排盘** - 古代三式之一
        - 📖 **易经占卜** - 传统六爻和梅花易数
        - 🎯 **智能解读** - 基于古籍的专业分析
        
        ## 技术架构
        
        - **前端**: Streamlit Web应用
        - **算法**: 官方PyPI包 (kinliuren, kinqimen, kintaiyi等)
        - **数据**: 200+本古代术数典籍
        - **AI**: 集成Gemini等大语言模型
        
        ## 使用说明
        
        1. 选择排盘类型
        2. 输入时间信息
        3. 获取排盘结果和解读
        
        ---
        
        **开发者**: 六行星团队  
        **项目**: 基于真实古籍的术数数字化平台
        EOF
        
        # 创建简化的requirements.txt (HF Spaces兼容)
        cat > hf-spaces-streamlit/requirements.txt << 'EOF'
        streamlit>=1.28.0
        pandas>=1.5.0
        numpy>=1.24.0
        kinliuren>=1.0.0
        kinqimen>=1.0.0
        kintaiyi>=1.0.0
        ichingshifa>=1.0.0
        sxtwl>=1.0.0
        python-dateutil>=2.8.0
        pytz>=2023.3
        EOF
        
        # 创建简化的app.py (移除本地依赖)
        cat > hf-spaces-streamlit/app_hf.py << 'EOF'
        import streamlit as st
        import datetime
        import sys
        from pathlib import Path
        
        # 页面配置
        st.set_page_config(
            page_title="太公心易兜率宫",
            page_icon="🏛️",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 检查模块可用性
        try:
            import kinliuren
            KINLIUREN_AVAILABLE = True
        except ImportError:
            KINLIUREN_AVAILABLE = False
            
        try:
            import kinqimen
            KINQIMEN_AVAILABLE = True
        except ImportError:
            KINQIMEN_AVAILABLE = False
            
        try:
            import kintaiyi
            KINTAIYI_AVAILABLE = True
        except ImportError:
            KINTAIYI_AVAILABLE = False
            
        try:
            import ichingshifa
            ICHINGSHIFA_AVAILABLE = True
        except ImportError:
            ICHINGSHIFA_AVAILABLE = False
        
        # 主应用
        def main():
            st.title("🏛️ 太公心易兜率宫")
            st.markdown("### 中华传统术数排盘系统")
            
            # 侧边栏
            with st.sidebar:
                st.header("📅 时间设置")
                
                # 时间输入
                date_input = st.date_input("选择日期", datetime.date.today())
                time_input = st.time_input("选择时间", datetime.time(12, 0))
                
                year = date_input.year
                month = date_input.month
                day = date_input.day
                hour = time_input.hour
                minute = time_input.minute
                
                st.markdown("---")
                st.header("🔮 排盘选择")
                
                # 排盘类型选择
                paiPan_type = st.selectbox(
                    "选择排盘类型",
                    ["大六壬", "奇门遁甲", "太乙神数", "易经占卜"]
                )
            
            # 主内容区
            if paiPan_type == "大六壬":
                render_liuren_section(year, month, day, hour, minute)
            elif paiPan_type == "奇门遁甲":
                render_qimen_section(year, month, day, hour, minute)
            elif paiPan_type == "太乙神数":
                render_taiyi_section(year, month, day, hour, minute)
            elif paiPan_type == "易经占卜":
                render_yijing_section(year, month, day, hour, minute)
        
        def render_liuren_section(year, month, day, hour, minute):
            st.header("📊 大六壬占卜")
            st.markdown("大六壬是中国古代占卜术之一，与奇门遁甲、太乙神数并称为'三式'。")
            
            if KINLIUREN_AVAILABLE:
                if st.button("开始六壬排盘", key="liuren_calc"):
                    with st.spinner("正在计算六壬排盘..."):
                        try:
                            # 这里需要实现具体的六壬算法
                            st.success("六壬排盘计算完成！")
                            st.info("详细的排盘结果将在这里显示")
                        except Exception as e:
                            st.error(f"计算错误: {str(e)}")
            else:
                st.error("kinliuren模块未安装，无法进行六壬排盘")
        
        def render_qimen_section(year, month, day, hour, minute):
            st.header("🌟 奇门遁甲占卜")
            st.markdown("奇门遁甲是中华民族的精典著作，也是奇门、六壬、太乙三大秘宝中的第一大秘术。")
            
            if KINQIMEN_AVAILABLE:
                if st.button("开始奇门排盘", key="qimen_calc"):
                    with st.spinner("正在计算奇门排盘..."):
                        try:
                            st.success("奇门排盘计算完成！")
                            st.info("详细的排盘结果将在这里显示")
                        except Exception as e:
                            st.error(f"计算错误: {str(e)}")
            else:
                st.error("kinqimen模块未安装，无法进行奇门排盘")
        
        def render_taiyi_section(year, month, day, hour, minute):
            st.header("⭐ 太乙神数占卜")
            st.markdown("太乙神数是中国古代占卜术之一，与奇门遁甲、大六壬并称为'三式'。")
            
            if KINTAIYI_AVAILABLE:
                if st.button("开始太乙排盘", key="taiyi_calc"):
                    with st.spinner("正在计算太乙排盘..."):
                        try:
                            st.success("太乙排盘计算完成！")
                            st.info("详细的排盘结果将在这里显示")
                        except Exception as e:
                            st.error(f"计算错误: {str(e)}")
            else:
                st.error("kintaiyi模块未安装，无法进行太乙排盘")
        
        def render_yijing_section(year, month, day, hour, minute):
            st.header("📖 易经占卜")
            st.markdown("易经是中华文化的源头活水，包含了深刻的哲学思想和占卜智慧。")
            
            if ICHINGSHIFA_AVAILABLE:
                if st.button("开始易经占卜", key="yijing_calc"):
                    with st.spinner("正在进行易经占卜..."):
                        try:
                            st.success("易经占卜完成！")
                            st.info("详细的占卜结果将在这里显示")
                        except Exception as e:
                            st.error(f"占卜错误: {str(e)}")
            else:
                st.error("ichingshifa模块未安装，无法进行易经占卜")
        
        if __name__ == "__main__":
            main()
        EOF
        
        # 重命名为app.py
        mv hf-spaces-streamlit/app_hf.py hf-spaces-streamlit/app.py

    - name: Deploy to Hugging Face Spaces
      env:
        HF_TOKEN: ${{ secrets.HF_TOKEN }}
      run: |
        cd hf-spaces-streamlit
        
        # 配置git
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Action"
        
        # 初始化git仓库
        git init
        git add .
        git commit -m "Deploy Streamlit app to HF Spaces"
        
        # 推送到HuggingFace Spaces
        git remote add origin https://huggingface.co/spaces/${{ secrets.HF_SPACE_NAME }}
        git push --force https://user:${{ secrets.HF_TOKEN }}@huggingface.co/spaces/${{ secrets.HF_SPACE_NAME }} main

  deploy-demo:
    runs-on: ubuntu-latest
    needs: deploy-streamlit
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Create Gemini Demo for HF
      run: |
        mkdir -p hf-spaces-demo
        
        # 复制Gemini演示
        cp -r tianquan-haystack/* hf-spaces-demo/
        
        # 创建HF Spaces配置
        cat > hf-spaces-demo/README.md << 'EOF'
        ---
        title: 六行星知识图谱 - Gemini AI古代大师
        emoji: 🤖
        colorFrom: blue
        colorTo: purple
        sdk: streamlit
        sdk_version: 1.28.0
        app_file: demo_app.py
        pinned: false
        license: mit
        short_description: 基于真实历史数据的AI古代学者对话系统
        ---
        
        # 六行星知识图谱 - Gemini AI古代大师
        
        🤖 **基于真实历史数据的AI古代学者对话系统**
        
        ## 功能特色
        
        - 🏛️ **古代学者角色扮演** - 太公望、邵雍、司马迁等
        - 📚 **真实历史数据** - 基于前四史和200+术数典籍
        - 🔍 **智能检索** - 人物关系、历史事件查询
        - 💬 **古雅对话** - 使用古代文言文风格
        - 🎯 **术数分析** - 结合历史人物进行术数解读
        
        ## 数据来源
        
        - **史记、汉书、后汉书、三国志** - 前四史完整数据
        - **200+术数典籍** - 六壬、奇门、太乙、易经等
        - **22万+关系** - 人物、事件、地理关系网络
        
        ---
        
        **技术栈**: Streamlit + Gemini AI + Neo4j知识图谱
        EOF
        
        # 创建简化的demo应用
        cat > hf-spaces-demo/demo_app.py << 'EOF'
        import streamlit as st
        import os
        
        st.set_page_config(
            page_title="六行星知识图谱 - Gemini AI古代大师",
            page_icon="🤖",
            layout="wide"
        )
        
        st.title("🤖 六行星知识图谱 - Gemini AI古代大师")
        st.markdown("### 基于真实历史数据的AI古代学者对话系统")
        
        # 检查API密钥
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            st.error("请配置GEMINI_API_KEY环境变量")
            st.stop()
        
        # 简化的演示界面
        st.markdown("---")
        st.markdown("## 🏛️ 古代大师对话")
        
        # 选择古代学者
        scholar = st.selectbox(
            "选择要对话的古代学者",
            ["太公望", "司马迁", "邵雍", "刘伯温", "诸葛亮"]
        )
        
        # 输入问题
        question = st.text_input("请输入您的问题", placeholder="例如：请分析一下三国时期的人物关系")
        
        if st.button("开始对话"):
            if question:
                with st.spinner(f"正在请教{scholar}..."):
                    # 这里应该调用Gemini API
                    st.success("对话功能正在开发中...")
                    st.info("完整版本请访问我们的GitHub仓库")
            else:
                st.warning("请输入问题")
        
        st.markdown("---")
        st.markdown("## 📊 项目统计")
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("史书数量", "24部")
        with col2:
            st.metric("术数典籍", "200+本")
        with col3:
            st.metric("人物关系", "22万+")
        with col4:
            st.metric("数据字数", "4000万+")
        
        st.markdown("---")
        st.markdown("## 🔗 相关链接")
        st.markdown("- [GitHub仓库](https://github.com/your-username/5-planets)")
        st.markdown("- [完整排盘系统](https://huggingface.co/spaces/your-username/sixstars-streamlit)")
        st.markdown("- [项目文档](https://github.com/your-username/5-planets/blob/main/README.md)")
        EOF
        
        # 创建requirements.txt
        cat > hf-spaces-demo/requirements.txt << 'EOF'
        streamlit>=1.28.0
        google-generativeai>=0.3.0
        pandas>=1.5.0
        numpy>=1.24.0
        EOF

    - name: Deploy Demo to HF Spaces
      env:
        HF_TOKEN: ${{ secrets.HF_TOKEN }}
      run: |
        cd hf-spaces-demo
        
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Action"
        
        git init
        git add .
        git commit -m "Deploy Gemini demo to HF Spaces"
        
        git remote add origin https://huggingface.co/spaces/${{ secrets.HF_DEMO_SPACE_NAME }}
        git push --force https://user:${{ secrets.HF_TOKEN }}@huggingface.co/spaces/${{ secrets.HF_DEMO_SPACE_NAME }} main
