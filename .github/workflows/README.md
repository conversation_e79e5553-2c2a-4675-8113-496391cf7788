# GitHub Actions 工作流配置

本目录包含GitHub Actions工作流配置，用于自动化部署六行星知识图谱项目到Hugging Face Spaces。

## 工作流说明

### `deploy-to-huggingface.yml`

这个工作流会在以下情况触发：
- 推送到`main`或`master`分支
- 修改`shushu/`或`tianquan-haystack/`目录下的文件
- 手动触发工作流

工作流会执行以下任务：
1. 部署Streamlit排盘应用到Hugging Face Spaces
2. 部署Gemini AI演示应用到另一个Hugging Face Space

## 配置说明

### 必要的Secrets

需要在GitHub仓库设置中添加以下Secrets：

- `HF_TOKEN`: Hugging Face API令牌
- `HF_SPACE_NAME`: Streamlit应用的Space名称 (格式: username/space-name)
- `HF_DEMO_SPACE_NAME`: Gemini演示的Space名称 (格式: username/space-name)
- `GEMINI_API_KEY`: (可选) Gemini API密钥，用于演示应用

### 如何设置Secrets

1. 在GitHub仓库页面，点击"Settings"
2. 在左侧菜单中选择"Secrets and variables" > "Actions"
3. 点击"New repository secret"
4. 添加上述Secrets

## 使用说明

### 手动触发部署

1. 在GitHub仓库页面，点击"Actions"
2. 选择"Deploy to Hugging Face Spaces"工作流
3. 点击"Run workflow"
4. 选择分支，然后点击"Run workflow"

### 自动部署

只需将更改推送到`main`或`master`分支，工作流将自动触发。

## 部署后访问

部署完成后，可以通过以下URL访问应用：

- Streamlit排盘应用: `https://huggingface.co/spaces/{HF_SPACE_NAME}`
- Gemini AI演示: `https://huggingface.co/spaces/{HF_DEMO_SPACE_NAME}`

## 故障排除

如果部署失败，请检查：

1. Secrets是否正确设置
2. Hugging Face API令牌是否有效
3. Space名称是否正确
4. 应用代码是否有错误

查看工作流运行日志以获取详细错误信息。
