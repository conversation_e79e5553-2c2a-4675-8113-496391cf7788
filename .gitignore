# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
*.log
temp/
tmp/
*.backup

# Neo4j
neo4j/
*.db

# Large data files
*.ttl
*.rdf
*.xml.gz

# Streamlit
.streamlit/

# 占卜系统临时文件
shushu/kinliuren/__pycache__/
shushu/kinqimen/__pycache__/
shushu/kintaiyi/__pycache__/
shushu/kintaiyi/data.pkl
shushu/kintaiyi/history.pkl

# 发布文件
RELEASE_*.md
RELEASE_INFO.json
PROJECT_SUMMARY.md

# Hugging Face Spaces
hf-spaces-streamlit/
hf-spaces-demo/

# NUC12 Setup
nuc12-setup/data/
nuc12-setup/ssl/
nuc12-setup/.env
