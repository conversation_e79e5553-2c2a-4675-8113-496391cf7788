# 🚀 腾讯Agent团队快速启动指南

## 📋 测试准备清单

### ✅ 环境要求
- [ ] Docker 20.10+ 已安装
- [ ] Docker Compose 2.0+ 已安装  
- [ ] Python 3.8+ 已安装
- [ ] 8GB+ 可用内存
- [ ] 20GB+ 可用磁盘空间

### ✅ 网络要求
- [ ] 稳定的互联网连接
- [ ] 端口5000可用 (API服务)
- [ ] 端口7474、7687可用 (Neo4j)
- [ ] 端口6379可用 (Redis)

---

## 🏛️ 一键部署兜率宫

### 1. 克隆项目
```bash
git clone https://github.com/your-org/taigong-xinyi-doushuai.git
cd taigong-xinyi-doushuai
```

### 2. 启动服务栈
```bash
# 一键启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f doushuai-api
```

### 3. 验证部署
```bash
# 等待服务启动（约2分钟）
sleep 120

# 健康检查
curl http://localhost:5000/api/v1/health

# 期望输出：
# {
#   "status": "healthy",
#   "timestamp": "2025-01-20T10:30:00Z",
#   "version": "1.0.0"
# }
```

---

## 🧪 运行测试套件

### 自动化测试
```bash
# 运行完整测试套件
./tests/run_tests.sh

# 或者指定API地址
./tests/run_tests.sh -u http://your-server:5000
```

### 手动测试
```bash
# 1. 健康检查
curl http://localhost:5000/api/v1/health

# 2. 服务状态
curl http://localhost:5000/api/v1/services

# 3. 占卜系统状态
curl http://localhost:5000/api/v1/divination/status

# 4. 测试大六壬占卜
curl -X POST http://localhost:5000/api/v1/divination/liuren \
  -H "Content-Type: application/json" \
  -d '{
    "question": "今日运势如何？",
    "datetime": "2025-01-20T10:30:00Z"
  }'

# 5. 测试综合占卜
curl -X POST http://localhost:5000/api/v1/divination/comprehensive \
  -H "Content-Type: application/json" \
  -d '{
    "question": "事业发展方向",
    "systems": ["liuren", "qimen", "dragon"]
  }'
```

---

## 🔌 Agent集成示例

### N8N工作流集成
```json
{
  "nodes": [
    {
      "name": "太公心易占卜",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "http://localhost:5000/api/v1/divination/comprehensive",
        "method": "POST",
        "body": {
          "question": "{{$json.user_question}}",
          "systems": ["liuren", "qimen", "dragon"]
        },
        "options": {
          "timeout": 10000
        }
      }
    },
    {
      "name": "处理占卜结果",
      "type": "n8n-nodes-base.function",
      "parameters": {
        "functionCode": "const result = items[0].json;\nreturn [{\n  json: {\n    analysis: result.comprehensive_analysis,\n    confidence: result.confidence_score\n  }\n}];"
        }
    }
  ]
}
```

### DIFY工具集成
```python
import requests

def divine_with_taigong(question: str, systems: list = None) -> dict:
    """
    调用太公心易兜率宫进行占卜分析
    
    Args:
        question: 占卜问题
        systems: 占卜系统列表，如 ["liuren", "qimen", "dragon"]
    
    Returns:
        占卜结果字典
    """
    if systems is None:
        systems = ["dragon"]  # 默认使用龙子系统
    
    try:
        response = requests.post(
            "http://localhost:5000/api/v1/divination/comprehensive",
            json={
                "question": question,
                "systems": systems
            },
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"API调用失败: {response.status_code}"}
            
    except Exception as e:
        return {"error": f"请求异常: {str(e)}"}

# 在DIFY中注册工具
@tool
def taigong_divination(question: str) -> str:
    """使用太公心易进行占卜分析"""
    result = divine_with_taigong(question, ["liuren", "qimen", "dragon"])
    
    if "error" in result:
        return f"占卜失败: {result['error']}"
    
    return result.get("comprehensive_analysis", "占卜结果解析失败")
```

### LangChain工具集成
```python
from langchain.tools import BaseTool
from typing import Optional, Type
from pydantic import BaseModel, Field
import requests

class TaigongDivinationInput(BaseModel):
    question: str = Field(description="要占卜的问题")
    systems: str = Field(default="dragon", description="占卜系统，可选: liuren,qimen,taiyi,dragon")

class TaigongDivinationTool(BaseTool):
    name = "taigong_divination"
    description = """
    使用太公心易兜率宫进行传统占卜分析。
    支持大六壬、奇门遁甲、太乙神数、十二龙子四种占卜系统。
    适用于决策支持、运势分析、时机判断等场景。
    """
    args_schema: Type[BaseModel] = TaigongDivinationInput
    
    def _run(self, question: str, systems: str = "dragon") -> str:
        """执行占卜"""
        try:
            system_list = systems.split(",")
            
            response = requests.post(
                "http://localhost:5000/api/v1/divination/comprehensive",
                json={
                    "question": question,
                    "systems": system_list
                },
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("comprehensive_analysis", "占卜结果解析失败")
            else:
                return f"占卜服务异常: HTTP {response.status_code}"
                
        except Exception as e:
            return f"占卜请求失败: {str(e)}"
    
    async def _arun(self, question: str, systems: str = "dragon") -> str:
        """异步执行占卜"""
        return self._run(question, systems)

# 使用示例
tool = TaigongDivinationTool()
result = tool.run("今天适合做重要决策吗？")
print(result)
```

---

## 📊 性能基准测试

### 并发测试
```bash
# 使用Apache Bench测试
ab -n 1000 -c 10 -T 'application/json' \
   -p test_payload.json \
   http://localhost:5000/api/v1/divination/dragon

# 期望结果：
# - 成功处理1000个请求
# - 平均响应时间 < 200ms
# - 错误率 < 1%
```

### Python并发测试
```python
import asyncio
import aiohttp
import time

async def test_concurrent_requests(num_requests=100):
    """测试并发请求"""
    async def make_request(session, request_id):
        async with session.post(
            'http://localhost:5000/api/v1/divination/dragon',
            json={'question': f'并发测试{request_id}'}
        ) as response:
            return await response.json()
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        tasks = [make_request(session, i) for i in range(num_requests)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
    
    success_count = sum(1 for r in results if r.get('status') == 'success')
    print(f"成功率: {success_count/num_requests*100:.1f}%")
    print(f"QPS: {num_requests/(end_time-start_time):.1f}")

# 运行测试
asyncio.run(test_concurrent_requests(100))
```

---

## 🔍 监控和调试

### 查看系统状态
```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 查看API日志
docker-compose logs -f doushuai-api

# 查看数据库日志
docker-compose logs -f neo4j
```

### 访问监控界面
- **Grafana监控**: http://localhost:3000 (admin/doushuai123)
- **Prometheus指标**: http://localhost:9090
- **Neo4j浏览器**: http://localhost:7474 (neo4j/doushuai123)

### 常见问题排查
```bash
# 1. 检查端口占用
netstat -tulpn | grep :5000

# 2. 检查Docker网络
docker network ls
docker network inspect 5-planets_doushuai-network

# 3. 重启服务
docker-compose restart doushuai-api

# 4. 查看详细日志
docker-compose logs --tail=100 doushuai-api
```

---

## 📞 技术支持

### 联系方式
- **技术支持**: <EMAIL>
- **GitHub Issues**: https://github.com/your-org/taigong-xinyi-doushuai/issues
- **API文档**: https://docs.doushuai-palace.com

### 测试反馈
请将测试结果和问题反馈发送至：
- **邮箱**: <EMAIL>
- **主题**: [腾讯Agent测试] 测试结果反馈

### 反馈模板
```markdown
## 测试环境
- 操作系统: [如 Ubuntu 20.04]
- Docker版本: [如 20.10.8]
- 测试时间: [如 2025-01-20]

## 测试结果
- [ ] 部署成功
- [ ] 基础功能正常
- [ ] 性能满足要求
- [ ] Agent集成成功

## 发现问题
1. [问题描述]
2. [复现步骤]

## 改进建议
1. [建议1]
2. [建议2]

## 总体评价
[对系统的总体评价]
```

---

## 🎯 测试重点

### 必测功能
1. ✅ 系统部署和启动
2. ✅ 基础API功能
3. ✅ 四种占卜系统
4. ✅ 综合占卜分析
5. ✅ 错误处理机制

### 重点关注
1. **响应时间**: 单次占卜 < 200ms
2. **并发能力**: 支持100+并发用户
3. **稳定性**: 长时间运行无异常
4. **集成性**: 易于集成到Agent系统
5. **可扩展性**: 支持水平扩展

### 成功标准
- ✅ 所有API接口正常响应
- ✅ 占卜结果格式正确
- ✅ 性能指标达到要求
- ✅ Agent集成测试通过
- ✅ 系统稳定运行24小时

---

**祝测试顺利！兜率宫期待与腾讯Agent的深度合作！** 🎉

*快速启动指南 v1.0*  
*更新时间: 2025年7月20日*
