# 🧪 太公心易兜率宫测试计划

## 📋 测试概述

本测试计划专为腾讯Agent团队设计，验证兜率宫作为AI Agent基础设施的可用性和稳定性。

- **测试目标**: 验证API服务的功能性、性能和集成能力
- **测试环境**: 生产模拟环境
- **测试周期**: 7天
- **测试负责人**: 腾讯Agent团队

---

## 🎯 测试目标

### 主要目标
1. **功能验证**: 确保所有占卜API正常工作
2. **性能测试**: 验证并发处理能力和响应时间
3. **集成测试**: 验证与Agent系统的集成效果
4. **稳定性测试**: 长时间运行的稳定性验证
5. **用户体验**: 评估API易用性和文档完整性

### 成功标准
- ✅ API响应时间 < 200ms (95%请求)
- ✅ 系统可用性 > 99.5%
- ✅ 并发处理 > 100 QPS
- ✅ 错误率 < 0.1%
- ✅ Agent集成成功率 > 95%

---

## 🚀 快速部署指南

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/your-org/taigong-xinyi-doushuai.git
cd taigong-xinyi-doushuai

# 一键启动服务栈
docker-compose up -d

# 等待服务启动（约2分钟）
sleep 120

# 验证服务状态
curl http://localhost:5000/api/v1/health
```

### 2. 服务验证

```bash
# 检查所有服务状态
docker-compose ps

# 查看API服务日志
docker-compose logs doushuai-api

# 验证占卜系统状态
curl http://localhost:5000/api/v1/divination/status
```

---

## 🔧 测试环境配置

### 推荐硬件配置
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- curl (用于API测试)
- Python 3.8+ (可选，用于脚本测试)

### 环境变量配置
```bash
# 复制环境配置
cp .env.example .env

# 根据需要修改配置
vim .env
```

---

## 📊 测试用例

### 阶段一：基础功能测试 (Day 1-2)

#### 1.1 健康检查测试
```bash
# 测试目标：验证系统基础状态
curl -X GET http://localhost:5000/api/v1/health

# 期望结果：
# - HTTP 200状态码
# - 返回系统健康状态
# - 所有服务状态为"healthy"
```

#### 1.2 大六壬占卜测试
```bash
# 测试目标：验证六壬占卜功能
curl -X POST http://localhost:5000/api/v1/divination/liuren \
  -H "Content-Type: application/json" \
  -d '{
    "question": "今日运势如何？",
    "datetime": "2025-01-20T10:30:00Z"
  }'

# 期望结果：
# - HTTP 200状态码
# - 返回完整的六壬分析结果
# - 包含天盘、地盘、神将信息
# - 响应时间 < 200ms
```

#### 1.3 奇门遁甲占卜测试
```bash
# 测试目标：验证奇门占卜功能
curl -X POST http://localhost:5000/api/v1/divination/qimen \
  -H "Content-Type: application/json" \
  -d '{
    "question": "投资决策建议",
    "datetime": "2025-01-20T14:00:00Z",
    "location": {
      "latitude": 39.9042,
      "longitude": 116.4074
    }
  }'

# 期望结果：
# - HTTP 200状态码
# - 返回奇门遁甲分析结果
# - 包含九宫格局信息
# - 响应时间 < 200ms
```

#### 1.4 太乙神数占卜测试
```bash
# 测试目标：验证太乙占卜功能
curl -X POST http://localhost:5000/api/v1/divination/taiyi \
  -H "Content-Type: application/json" \
  -d '{
    "question": "国运分析",
    "datetime": "2025-01-20T12:00:00Z",
    "scope": "national"
  }'

# 期望结果：
# - HTTP 200状态码
# - 返回太乙神数分析结果
# - 包含神数推演信息
# - 响应时间 < 200ms
```

#### 1.5 十二龙子占卜测试
```bash
# 测试目标：验证龙子占卜功能
curl -X POST http://localhost:5000/api/v1/divination/dragon \
  -H "Content-Type: application/json" \
  -d '{
    "question": "感情运势",
    "questioner_info": {
      "birth_year": 1990,
      "gender": "male"
    }
  }'

# 期望结果：
# - HTTP 200状态码
# - 返回龙子占卜结果
# - 包含主龙、副龙信息
# - 响应时间 < 200ms
```

#### 1.6 综合占卜测试
```bash
# 测试目标：验证综合占卜功能
curl -X POST http://localhost:5000/api/v1/divination/comprehensive \
  -H "Content-Type: application/json" \
  -d '{
    "question": "事业发展方向",
    "datetime": "2025-01-20T09:00:00Z",
    "systems": ["liuren", "qimen", "dragon"]
  }'

# 期望结果：
# - HTTP 200状态码
# - 返回综合分析结果
# - 包含多系统交叉验证
# - 响应时间 < 500ms
```

### 阶段二：性能压力测试 (Day 3-4)

#### 2.1 并发测试
```bash
# 使用Apache Bench进行并发测试
ab -n 1000 -c 10 -T 'application/json' \
   -p test_data.json \
   http://localhost:5000/api/v1/divination/liuren

# 期望结果：
# - 成功处理1000个请求
# - 并发10个连接
# - 平均响应时间 < 200ms
# - 错误率 < 1%
```

#### 2.2 负载测试
```python
# 使用Python脚本进行负载测试
import asyncio
import aiohttp
import time

async def test_load():
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(100):
            task = session.post(
                'http://localhost:5000/api/v1/divination/comprehensive',
                json={'question': f'测试问题{i}'}
            )
            tasks.append(task)
        
        start_time = time.time()
        responses = await asyncio.gather(*tasks)
        end_time = time.time()
        
        print(f"处理100个请求耗时: {end_time - start_time:.2f}秒")
        print(f"平均响应时间: {(end_time - start_time) / 100:.3f}秒")

# 期望结果：
# - 100个并发请求全部成功
# - 总耗时 < 20秒
# - 平均响应时间 < 200ms
```

### 阶段三：Agent集成测试 (Day 5-6)

#### 3.1 模拟N8N集成
```javascript
// N8N工作流节点测试
const workflow = {
  "nodes": [
    {
      "name": "太公心易占卜",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "http://localhost:5000/api/v1/divination/comprehensive",
        "method": "POST",
        "body": {
          "question": "{{$json.user_question}}",
          "systems": ["liuren", "qimen"]
        }
      }
    }
  ]
};

// 期望结果：
// - 工作流正常执行
// - 占卜结果正确返回
// - 可以在后续节点中使用结果
```

#### 3.2 模拟DIFY集成
```python
# DIFY工具集成测试
import requests

def test_dify_integration():
    """测试DIFY工具集成"""
    
    def divine_with_taigong(question: str) -> str:
        """使用太公心易进行占卜分析"""
        response = requests.post(
            "http://localhost:5000/api/v1/divination/comprehensive",
            json={
                "question": question,
                "systems": ["liuren", "qimen", "dragon"]
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get("comprehensive_analysis", "占卜失败")
        else:
            return f"API调用失败: {response.status_code}"
    
    # 测试用例
    test_questions = [
        "今天适合投资吗？",
        "这个项目会成功吗？",
        "团队合作会顺利吗？"
    ]
    
    for question in test_questions:
        result = divine_with_taigong(question)
        print(f"问题: {question}")
        print(f"结果: {result}")
        print("-" * 50)

# 期望结果：
# - 所有问题都能得到合理回答
# - 返回结果格式正确
# - 可以集成到DIFY对话流程中
```

### 阶段四：稳定性测试 (Day 7)

#### 4.1 长时间运行测试
```bash
# 24小时持续运行测试
#!/bin/bash
start_time=$(date +%s)
success_count=0
error_count=0

while [ $(($(date +%s) - start_time)) -lt 86400 ]; do
    response=$(curl -s -w "%{http_code}" \
        -X POST http://localhost:5000/api/v1/divination/liuren \
        -H "Content-Type: application/json" \
        -d '{"question": "稳定性测试"}')
    
    if [[ $response == *"200" ]]; then
        ((success_count++))
    else
        ((error_count++))
    fi
    
    sleep 60  # 每分钟一次请求
done

echo "成功请求: $success_count"
echo "失败请求: $error_count"
echo "成功率: $(echo "scale=2; $success_count * 100 / ($success_count + $error_count)" | bc)%"

# 期望结果：
# - 成功率 > 99.5%
# - 系统持续稳定运行
# - 内存使用无明显泄漏
```

---

## 📈 监控指标

### 关键指标监控
```bash
# 查看系统资源使用
docker stats

# 查看API响应时间
curl -w "@curl-format.txt" -s -o /dev/null \
  http://localhost:5000/api/v1/health

# 查看Prometheus指标
curl http://localhost:9090/api/v1/query?query=up

# 查看Grafana仪表板
open http://localhost:3000
```

### 性能基准
| 指标 | 目标值 | 实际值 | 状态 |
|------|--------|--------|------|
| API响应时间 | < 200ms | _待测试_ | ⏳ |
| 并发处理能力 | > 100 QPS | _待测试_ | ⏳ |
| 系统可用性 | > 99.5% | _待测试_ | ⏳ |
| 内存使用 | < 1GB | _待测试_ | ⏳ |
| CPU使用率 | < 70% | _待测试_ | ⏳ |

---

## 🐛 问题反馈

### 反馈渠道
- **技术问题**: <EMAIL>
- **GitHub Issues**: https://github.com/your-org/taigong-xinyi-doushuai/issues
- **微信群**: [测试专用群]
- **电话支持**: 400-XXX-XXXX

### 反馈模板
```markdown
## 问题描述
[详细描述遇到的问题]

## 复现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

## 期望结果
[描述期望的正确结果]

## 实际结果
[描述实际发生的结果]

## 环境信息
- 操作系统: [如 macOS 12.0]
- Docker版本: [如 20.10.8]
- 测试时间: [如 2025-01-20 10:30]

## 附加信息
[其他相关信息，如日志、截图等]
```

---

## 🎯 测试总结

### 测试报告模板
```markdown
# 太公心易兜率宫测试报告

## 测试概况
- 测试时间: [开始时间] - [结束时间]
- 测试环境: [环境描述]
- 测试人员: [腾讯Agent团队]

## 测试结果
- 功能测试: ✅/❌
- 性能测试: ✅/❌
- 集成测试: ✅/❌
- 稳定性测试: ✅/❌

## 关键指标
- API响应时间: [实际值]
- 并发处理能力: [实际值]
- 系统可用性: [实际值]
- 错误率: [实际值]

## 问题清单
1. [问题1描述]
2. [问题2描述]

## 改进建议
1. [建议1]
2. [建议2]

## 总体评价
[对系统的总体评价和建议]
```

---

## 🚀 下一步计划

### 测试完成后
1. **问题修复**: 根据测试反馈修复发现的问题
2. **性能优化**: 针对性能瓶颈进行优化
3. **文档完善**: 根据测试经验完善文档
4. **正式发布**: 准备v1.0.0正式版本发布

### 长期合作
1. **技术支持**: 提供持续的技术支持服务
2. **功能定制**: 根据腾讯需求定制特殊功能
3. **深度集成**: 探索更深层次的技术集成
4. **商业合作**: 讨论商业合作模式

---

**感谢腾讯Agent团队的测试支持！** 🙏

*测试计划版本: v1.0*  
*创建时间: 2025年7月20日*  
*联系人: [您的联系方式]*
