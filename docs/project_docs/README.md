# 太公心易兜率宫 (Taigong Xinyi Doushuai Palace)

![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)
![Version: 1.0.0](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Status: Production](https://img.shields.io/badge/status-production-brightgreen.svg)
![Python: 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)
![API: RESTful](https://img.shields.io/badge/API-RESTful-blue.svg)
![Integration: N8N/DIFY](https://img.shields.io/badge/integration-N8N%2FDIFY-orange.svg)

> 兜率宫中炼金丹，太公心易通天算。为AI Agent提供中华智慧的基础设施层。

**🏛️ 专业基础设施：为N8N、DIFY等外部Agent系统提供中华传统智慧的API服务层**

---

## 🏛️ 项目定位

**太公心易兜率宫**是专为AI Agent生态系统设计的中华传统智慧基础设施层。

作为**基础设施即服务(IaaS)**，兜率宫为N8N、DIFY、LangChain、AutoGPT等外部Agent系统提供标准化的中华智慧API接口，让现代AI能够无缝调用五千年传统智慧。

### 🎯 核心使命

- **为AI Agent赋能传统智慧**：让现代AI系统能够调用六壬、奇门、太乙等传统算法
- **标准化API服务**：提供RESTful接口，支持任何编程语言和平台集成
- **生产级基础设施**：高可用、高性能、易扩展的企业级服务架构
- **文化传承与创新**：以现代技术手段传承和发扬中华传统文化

### 🔮 服务能力

兜率宫集成了完整的中华传统智慧体系：

- **三式神算**：大六壬、奇门遁甲、太乙神数的完整实现
- **心易射覆**：原创十二龙子心易系统
- **历史知识图谱**：基于二十四史构建的动态知识网络
- **术数科技树**：1,215种传统方法的数字化实现

## 🚀 快速部署

### 🐳 Docker一键部署（推荐）

```bash
# 克隆项目
git clone https://github.com/your-org/taigong-xinyi-doushuai.git
cd taigong-xinyi-doushuai

# 一键启动完整服务栈
docker-compose up -d

# 验证服务状态
curl http://localhost:5000/api/v1/health
```

### 📦 传统部署

#### 环境要求
- Python 3.8+
- Neo4j 5.0+ (可选，用于知识图谱功能)
- Redis 6.0+ (可选，用于缓存)
- 4GB+ RAM (基础功能) / 8GB+ RAM (完整功能)

#### 安装步骤

1. **环境准备**
```bash
git clone https://github.com/your-org/taigong-xinyi-doushuai.git
cd taigong-xinyi-doushuai
pip install -r requirements.txt
```

2. **配置服务**
```bash
cp .env.example .env
# 编辑配置文件
vim .env
```

3. **启动服务**
```bash
# 生产模式启动
python main.py --mode web --port 5000

# 开发模式启动
python main.py --mode web --debug
```

### 🔧 服务验证

```bash
# 健康检查
curl http://localhost:5000/api/v1/health

# 获取可用服务
curl http://localhost:5000/api/v1/services

# 测试占卜接口
curl -X POST http://localhost:5000/api/v1/divination/liuren \
  -H "Content-Type: application/json" \
  -d '{"question": "今日运势如何？"}'
```

## 🔌 API接口文档

### 🌐 RESTful API概览

兜率宫提供完整的RESTful API，支持JSON格式的请求和响应，完全兼容OpenAPI 3.0规范。

#### 基础信息
- **Base URL**: `http://your-domain:5000/api/v1`
- **认证方式**: API Key (可选) / JWT Token (企业版)
- **请求格式**: `application/json`
- **响应格式**: `application/json`

### 🔮 占卜服务API

#### 1. 大六壬占卜
```http
POST /api/v1/divination/liuren
Content-Type: application/json

{
  "question": "今日运势如何？",
  "datetime": "2025-01-20T10:30:00Z",
  "options": {
    "include_interpretation": true,
    "detail_level": "full"
  }
}
```

#### 2. 奇门遁甲占卜
```http
POST /api/v1/divination/qimen
Content-Type: application/json

{
  "question": "投资决策建议",
  "datetime": "2025-01-20T14:00:00Z",
  "location": {
    "latitude": 39.9042,
    "longitude": 116.4074
  }
}
```

#### 3. 太乙神数占卜
```http
POST /api/v1/divination/taiyi
Content-Type: application/json

{
  "question": "国运分析",
  "datetime": "2025-01-20T12:00:00Z",
  "scope": "national"
}
```

#### 4. 十二龙子心易
```http
POST /api/v1/divination/dragon
Content-Type: application/json

{
  "question": "感情运势",
  "questioner_info": {
    "birth_year": 1990,
    "gender": "male"
  }
}
```

#### 5. 综合占卜（推荐）
```http
POST /api/v1/divination/comprehensive
Content-Type: application/json

{
  "question": "事业发展方向",
  "datetime": "2025-01-20T09:00:00Z",
  "systems": ["liuren", "qimen", "dragon"],
  "analysis_depth": "deep"
}
```

### 📊 系统管理API

#### 服务状态检查
```http
GET /api/v1/health
GET /api/v1/services
GET /api/v1/divination/status
```

#### 系统配置
```http
GET /api/v1/config
POST /api/v1/config/update
```

### 🔗 Agent集成示例

#### N8N工作流集成
```json
{
  "nodes": [
    {
      "name": "太公心易占卜",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "http://doushuai-palace:5000/api/v1/divination/comprehensive",
        "method": "POST",
        "body": {
          "question": "{{$json.user_question}}",
          "systems": ["liuren", "qimen"]
        }
      }
    }
  ]
}
```

#### DIFY Agent集成
```python
import requests

def call_doushuai_divination(question: str) -> dict:
    """调用兜率宫占卜服务"""
    response = requests.post(
        "http://doushuai-palace:5000/api/v1/divination/comprehensive",
        json={
            "question": question,
            "systems": ["liuren", "qimen", "dragon"]
        }
    )
    return response.json()

# 在DIFY工具中注册
@tool
def divine_with_taigong(question: str) -> str:
    """使用太公心易进行占卜分析"""
    result = call_doushuai_divination(question)
    return result.get("comprehensive_analysis", "占卜失败")
```

#### LangChain工具集成
```python
from langchain.tools import BaseTool
from typing import Optional, Type
from pydantic import BaseModel, Field

class TaigongDivinationInput(BaseModel):
    question: str = Field(description="要占卜的问题")
    system: str = Field(default="comprehensive", description="占卜系统")

class TaigongDivinationTool(BaseTool):
    name = "taigong_divination"
    description = "使用太公心易兜率宫进行传统占卜分析"
    args_schema: Type[BaseModel] = TaigongDivinationInput

    def _run(self, question: str, system: str = "comprehensive") -> str:
        # 调用兜率宫API
        response = requests.post(f"{DOUSHUAI_BASE_URL}/divination/{system}",
                               json={"question": question})
        return response.json()["result"]
```

## 🏗️ 架构设计

### 🎯 设计原则

兜率宫遵循现代微服务架构设计原则：

1. **服务解耦**：每个占卜系统独立部署，互不影响
2. **API优先**：所有功能通过标准RESTful API暴露
3. **水平扩展**：支持负载均衡和集群部署
4. **容错设计**：优雅降级，单点故障不影响整体服务
5. **可观测性**：完整的日志、监控和链路追踪

### 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (Nginx/Kong)                    │
├─────────────────────────────────────────────────────────────┤
│                    负载均衡层 (HAProxy)                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  六壬服务   │ │  奇门服务   │ │  太乙服务   │ │ 龙子服务│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Flask/FastAPI)                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   Redis     │ │   Neo4j     │ │ PostgreSQL  │ │  MinIO  │ │
│  │   缓存层    │ │  知识图谱   │ │  关系数据   │ │ 文件存储│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 🔧 技术栈

#### 核心服务
- **API框架**: Flask + Flask-RESTful
- **异步处理**: Celery + Redis
- **数据库**: Neo4j (图数据) + PostgreSQL (关系数据)
- **缓存**: Redis Cluster
- **消息队列**: RabbitMQ

#### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (可选)
- **网关**: Nginx + Kong
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

#### 开发工具
- **API文档**: Swagger/OpenAPI 3.0
- **测试**: pytest + coverage
- **CI/CD**: GitHub Actions
- **代码质量**: SonarQube

## 📁 项目结构

```
taigong-xinyi-doushuai/
├── src/                          # 核心源码
│   ├── api/                      # API接口层
│   │   ├── v1/                   # API v1版本
│   │   │   ├── divination/       # 占卜接口
│   │   │   ├── knowledge/        # 知识图谱接口
│   │   │   └── system/           # 系统管理接口
│   │   └── middleware/           # 中间件
│   ├── core/                     # 核心业务逻辑
│   │   ├── divination/           # 占卜引擎
│   │   │   ├── liuren/           # 六壬算法
│   │   │   ├── qimen/            # 奇门算法
│   │   │   ├── taiyi/            # 太乙算法
│   │   │   └── dragon/           # 龙子算法
│   │   ├── knowledge/            # 知识图谱
│   │   └── analytics/            # 分析引擎
│   ├── models/                   # 数据模型
│   ├── services/                 # 业务服务
│   └── utils/                    # 工具函数
├── shushu/                       # 传统算法库
│   ├── kinliuren/                # 六壬核心库
│   ├── kinqimen/                 # 奇门核心库
│   └── kintaiyi/                 # 太乙核心库
├── data/                         # 数据文件
│   ├── historical/               # 历史数据
│   ├── knowledge_graph/          # 知识图谱数据
│   └── configurations/           # 配置数据
├── deploy/                       # 部署配置
│   ├── docker/                   # Docker配置
│   ├── kubernetes/               # K8s配置
│   └── nginx/                    # Nginx配置
├── docs/                         # 文档
│   ├── api/                      # API文档
│   ├── deployment/               # 部署文档
│   └── integration/              # 集成指南
├── tests/                        # 测试代码
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   └── performance/              # 性能测试
├── scripts/                      # 脚本工具
│   ├── deployment/               # 部署脚本
│   ├── data_migration/           # 数据迁移
│   └── monitoring/               # 监控脚本
├── docker-compose.yml            # 开发环境
├── docker-compose.prod.yml       # 生产环境
├── requirements.txt              # Python依赖
├── Dockerfile                    # 容器构建
└── README.md                     # 项目说明
```

## 🔧 配置管理

### 环境变量配置

```bash
# 基础配置
DOUSHUAI_ENV=production
DOUSHUAI_HOST=0.0.0.0
DOUSHUAI_PORT=5000
DOUSHUAI_DEBUG=false

# 数据库配置
NEO4J_URI=bolt://neo4j:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=doushuai
POSTGRES_USER=doushuai
POSTGRES_PASSWORD=your_password

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_password

# API配置
API_KEY_REQUIRED=true
JWT_SECRET_KEY=your_jwt_secret
RATE_LIMIT_PER_MINUTE=60

# 监控配置
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
LOG_LEVEL=INFO

# 占卜系统配置
LIUREN_ENABLED=true
QIMEN_ENABLED=true
TAIYI_ENABLED=true
DRAGON_ENABLED=true
```

### Docker Compose配置

```yaml
version: '3.8'
services:
  doushuai-api:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DOUSHUAI_ENV=production
    depends_on:
      - redis
      - neo4j
      - postgres

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  neo4j:
    image: neo4j:5.0
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/doushuai123

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=doushuai
      - POSTGRES_USER=doushuai
      - POSTGRES_PASSWORD=doushuai123

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx:/etc/nginx/conf.d
    depends_on:
      - doushuai-api
```

## 📊 性能指标

### 🚀 性能基准

兜率宫在标准硬件配置下的性能表现：

| 指标 | 数值 | 说明 |
|------|------|------|
| **API响应时间** | < 200ms | 95%请求在200ms内响应 |
| **并发处理能力** | 1000+ QPS | 单实例支持1000+并发请求 |
| **占卜计算时间** | < 100ms | 单次占卜计算平均耗时 |
| **系统可用性** | 99.9% | 年度可用性目标 |
| **内存使用** | < 512MB | 单实例基础内存占用 |
| **启动时间** | < 30s | 冷启动到服务就绪时间 |

### 📈 扩展性

- **水平扩展**：支持多实例负载均衡
- **垂直扩展**：支持单实例性能提升
- **缓存优化**：Redis缓存提升响应速度
- **数据库优化**：Neo4j集群支持大规模数据

### 🔍 监控指标

```bash
# 健康检查
curl http://localhost:5000/api/v1/health

# 性能指标
curl http://localhost:5000/api/v1/metrics

# 系统状态
curl http://localhost:5000/api/v1/status
```

## 🛡️ 安全特性

### 🔐 认证授权

- **API Key认证**：支持API密钥访问控制
- **JWT Token**：支持用户会话管理
- **角色权限**：细粒度的权限控制
- **IP白名单**：支持IP访问限制

### 🔒 数据安全

- **传输加密**：HTTPS/TLS加密传输
- **存储加密**：敏感数据加密存储
- **访问日志**：完整的访问审计日志
- **数据备份**：定期自动数据备份

### 🛡️ 安全配置

```yaml
security:
  api_key_required: true
  jwt_enabled: true
  https_only: true
  rate_limiting:
    enabled: true
    requests_per_minute: 60
  ip_whitelist:
    enabled: false
    allowed_ips: []
  cors:
    enabled: true
    allowed_origins: ["*"]
```

## 🤝 企业服务

### 💼 商业版本

兜率宫提供企业级商业版本，包含以下增强功能：

- **高级认证**：LDAP/AD集成、SSO单点登录
- **企业级监控**：详细的性能分析和报告
- **专业支持**：7x24小时技术支持
- **定制开发**：根据企业需求定制功能
- **私有部署**：支持私有云和本地部署
- **SLA保障**：99.99%可用性保证

### 🎯 行业解决方案

- **金融行业**：投资决策、风险评估、市场分析
- **咨询服务**：战略规划、商业决策、项目评估
- **文化传媒**：内容创作、文化研究、教育培训
- **科技企业**：产品规划、技术决策、团队管理

### 📞 商务联系

- **商务邮箱**: <EMAIL>
- **技术支持**: <EMAIL>
- **合作伙伴**: <EMAIL>

## 🌟 开源贡献

### 🤝 如何参与

我们欢迎各种形式的贡献：

1. **代码贡献**：提交PR改进功能和性能
2. **文档完善**：改进API文档和使用指南
3. **问题反馈**：报告bug和提出功能建议
4. **算法优化**：改进传统算法的实现
5. **测试用例**：增加测试覆盖率
6. **国际化**：支持多语言版本

### 📋 开发指南

```bash
# 克隆项目
git clone https://github.com/your-org/taigong-xinyi-doushuai.git
cd taigong-xinyi-doushuai

# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
pytest tests/

# 代码格式化
black src/
flake8 src/

# 提交代码
git add .
git commit -m "feat: 添加新功能"
git push origin feature-branch
```

## 📄 开源协议

本项目采用 **MIT License** 开源协议。

- **核心代码**：MIT License
- **算法库**：MIT License
- **文档资料**：CC BY-SA 4.0
- **历史数据**：CC BY-SA 4.0

详见 [LICENSE](LICENSE) 文件。

## 📚 相关资源

### 📖 文档链接

- **API文档**: [https://docs.doushuai-palace.com/api](https://docs.doushuai-palace.com/api)
- **部署指南**: [docs/deployment/README.md](docs/deployment/README.md)
- **集成指南**: [docs/integration/README.md](docs/integration/README.md)
- **开发文档**: [docs/development/README.md](docs/development/README.md)

### 🔗 相关项目

- **N8N集成插件**: [n8n-nodes-doushuai](https://github.com/your-org/n8n-nodes-doushuai)
- **DIFY工具包**: [dify-tools-doushuai](https://github.com/your-org/dify-tools-doushuai)
- **LangChain集成**: [langchain-doushuai](https://github.com/your-org/langchain-doushuai)

### 🎓 学习资源

- **传统文化**: 《周易》《太乙神数》《奇门遁甲》《大六壬》
- **现代技术**: RESTful API设计、微服务架构、容器化部署
- **AI集成**: Agent框架、工具调用、流程编排

---

## 🏛️ 项目愿景

### 🎯 使命宣言

**兜率宫，为AI时代的中华智慧基础设施。**

我们致力于：
- 将五千年传统智慧数字化、标准化、服务化
- 为现代AI系统提供中华文化的深度支撑
- 推动传统文化在数字时代的传承与创新
- 构建东西方智慧融合的技术桥梁

### 🌟 核心价值

1. **传承创新**：以现代技术传承古代智慧
2. **开放共享**：开源开放，服务全球开发者
3. **专业可靠**：企业级品质，生产环境可用
4. **文化自信**：展现中华文化的技术魅力

### 🚀 发展路线

- **v1.0**: 基础占卜服务API ✅
- **v1.5**: 知识图谱深度集成 🔄
- **v2.0**: 多语言SDK支持 📋
- **v2.5**: 云原生架构升级 📋
- **v3.0**: AI大模型深度融合 📋

---

## 题跋：兜率宫记

> 兜率宫中炼金丹，太公心易通天算。

昔太公望，垂钓渭水，以待明主。今我辈，构建兜率宫，以待智能时代之明主——那些能够融合古今智慧的AI Agent们。

古有灵台观象，今有数据中心；古有算筹推演，今有API调用。形式虽变，智慧永恒。

兜率宫者，弥勒菩萨之净土也，象征着未来的希望与智慧的传承。我们以此为名，寓意着传统智慧在AI时代的新生与发展。

愿此项目，能为AI时代的智慧传承，贡献绵薄之力。愿古今智慧，在此交融；愿东西文明，在此对话。

**这不是AI自动化，而是AI作为史官灵魂的扩音器。**

---

*© 2025 太公心易兜率宫项目 | v1.0.0 | MIT License*
*项目主页: https://github.com/your-org/taigong-xinyi-doushuai*
*官方网站: https://doushuai-palace.com*
