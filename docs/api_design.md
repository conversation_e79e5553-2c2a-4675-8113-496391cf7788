# 六行星知识图谱API接口设计规范

## API架构概览

### RESTful API设计原则
- 资源导向的URL设计
- 标准HTTP方法使用
- 统一的响应格式
- 版本控制策略
- 完善的错误处理

### API版本控制
```
Base URL: https://api.sixstars.cn/v1/
版本策略: URL路径版本控制
当前版本: v1
```

## 核心API接口

### 1. 实体查询接口

#### 获取实体详情
```http
GET /api/v1/entities/{entity_id}
```

**请求参数**:
```json
{
  "include_relations": true,
  "relation_depth": 2,
  "include_sources": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "person_001",
    "name": "司马迁",
    "type": "person",
    "properties": {
      "courtesy_name": "子长",
      "birth_year": -145,
      "death_year": -86,
      "dynasty": "汉",
      "occupation": "史学家"
    },
    "relations": [
      {
        "relation_type": "authored",
        "target_entity": {
          "id": "text_001",
          "name": "史记",
          "type": "text"
        },
        "confidence": 0.95
      }
    ],
    "sources": [
      {
        "text_id": "hanshu_001",
        "reference": "卷六十二·司马迁传",
        "reliability": 0.9
      }
    ]
  }
}
```

#### 实体搜索
```http
GET /api/v1/entities/search
```

**查询参数**:
- `q`: 搜索关键词
- `type`: 实体类型过滤
- `dynasty`: 朝代过滤
- `limit`: 返回数量限制
- `offset`: 分页偏移

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 156,
    "entities": [
      {
        "id": "person_001",
        "name": "司马迁",
        "type": "person",
        "relevance_score": 0.95,
        "highlight": "史学家<em>司马迁</em>著《史记》"
      }
    ]
  }
}
```

### 2. 关系查询接口

#### 获取实体关系
```http
GET /api/v1/entities/{entity_id}/relations
```

**查询参数**:
- `relation_type`: 关系类型过滤
- `direction`: 关系方向 (incoming/outgoing/both)
- `depth`: 关系深度
- `limit`: 返回数量限制

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "relations": [
      {
        "id": "rel_001",
        "type": "contemporary_with",
        "source": "person_001",
        "target": "person_002",
        "properties": {
          "overlap_years": 30,
          "interaction_type": "colleague"
        },
        "confidence": 0.8
      }
    ]
  }
}
```

#### 路径查询
```http
GET /api/v1/paths
```

**请求参数**:
```json
{
  "source_id": "person_001",
  "target_id": "person_002",
  "max_depth": 3,
  "relation_types": ["contemporary_with", "influenced_by"]
}
```

### 3. 文本分析接口

#### 叙事冲突检测
```http
POST /api/v1/analysis/narrative-conflicts
```

**请求体**:
```json
{
  "time_range": {
    "start_year": -206,
    "end_year": -195
  },
  "entities": ["person_001", "event_001"],
  "source_types": ["imperial", "official", "celestial"]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conflicts": [
      {
        "id": "conflict_001",
        "type": "sentiment_conflict",
        "severity": 0.8,
        "description": "对刘邦的评价存在明显分歧",
        "sources": [
          {
            "text_id": "shiji_001",
            "sentiment": 0.7,
            "perspective": "imperial"
          },
          {
            "text_id": "wuxingzhi_001",
            "sentiment": -0.3,
            "perspective": "celestial"
          }
        ]
      }
    ]
  }
}
```

#### 天象关联分析
```http
POST /api/v1/analysis/celestial-correlation
```

**请求体**:
```json
{
  "event_id": "event_001",
  "time_tolerance": 365,
  "celestial_types": ["eclipse", "comet", "supernova"]
}
```

### 4. 时间轴接口

#### 获取时间线
```http
GET /api/v1/timeline
```

**查询参数**:
- `start_year`: 开始年份
- `end_year`: 结束年份
- `entity_ids`: 相关实体ID列表
- `event_types`: 事件类型过滤
- `granularity`: 时间粒度 (year/month/day)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "timeline": [
      {
        "date": "-206-02-28",
        "events": [
          {
            "id": "event_001",
            "name": "刘邦称帝",
            "type": "political",
            "participants": ["person_001"],
            "location": "location_001"
          }
        ],
        "celestial_events": [
          {
            "id": "celestial_001",
            "type": "lunar_eclipse",
            "visibility": "visible"
          }
        ]
      }
    ]
  }
}
```

### 5. 统计分析接口

#### 实体统计
```http
GET /api/v1/statistics/entities
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_entities": 50000,
    "by_type": {
      "person": 15000,
      "event": 12000,
      "location": 8000,
      "text": 10000,
      "celestial": 5000
    },
    "by_dynasty": {
      "汉": 12000,
      "唐": 15000,
      "宋": 10000
    }
  }
}
```

#### 关系统计
```http
GET /api/v1/statistics/relations
```

## GraphQL API接口

### Schema定义
```graphql
type Query {
  entity(id: ID!): Entity
  entities(
    search: String
    type: EntityType
    dynasty: String
    limit: Int = 10
    offset: Int = 0
  ): EntityConnection
  
  narrativeConflicts(
    timeRange: TimeRange!
    entities: [ID!]
    sourceTypes: [SourceType!]
  ): [NarrativeConflict!]!
  
  timeline(
    startYear: Int!
    endYear: Int!
    entityIds: [ID!]
    eventTypes: [EventType!]
  ): Timeline!
}

type Entity {
  id: ID!
  name: String!
  type: EntityType!
  properties: JSON
  relations(
    type: RelationType
    direction: RelationDirection = BOTH
    limit: Int = 10
  ): [Relation!]!
  sources: [Source!]!
}

type Relation {
  id: ID!
  type: RelationType!
  source: Entity!
  target: Entity!
  properties: JSON
  confidence: Float!
}

type NarrativeConflict {
  id: ID!
  type: ConflictType!
  severity: Float!
  description: String!
  timePoint: DateTime!
  sources: [ConflictSource!]!
}

enum EntityType {
  PERSON
  EVENT
  LOCATION
  TEXT
  CELESTIAL
}

enum ConflictType {
  SENTIMENT_CONFLICT
  FACTUAL_CONFLICT
  TEMPORAL_CONFLICT
  CAUSAL_CONFLICT
}
```

### 查询示例
```graphql
query GetPersonWithRelations($id: ID!) {
  entity(id: $id) {
    id
    name
    type
    properties
    relations(limit: 5) {
      type
      target {
        id
        name
        type
      }
      confidence
    }
    sources {
      textId
      reference
      reliability
    }
  }
}
```

## 认证与授权

### JWT Token认证
```http
Authorization: Bearer <jwt_token>
```

### API Key认证
```http
X-API-Key: <api_key>
```

### 权限级别
- **Public**: 基础查询功能
- **Academic**: 高级分析功能
- **Premium**: 完整功能访问
- **Admin**: 数据管理权限

## 错误处理

### 标准错误响应
```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "error": {
    "type": "ValidationError",
    "details": [
      {
        "field": "entity_id",
        "message": "Entity ID is required"
      }
    ]
  },
  "timestamp": "2025-01-19T10:30:00Z",
  "request_id": "req_123456"
}
```

### 错误码定义
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误

## 限流策略

### 频率限制
- **免费用户**: 1000次/小时
- **学术用户**: 5000次/小时
- **付费用户**: 10000次/小时
- **企业用户**: 无限制

### 限流响应头
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642608000
```

## 数据格式规范

### 时间格式
- 标准格式: ISO 8601
- 示例: `2025-01-19T10:30:00Z`
- 历史年份: 负数表示公元前

### 坐标格式
```json
{
  "latitude": 39.9042,
  "longitude": 116.4074,
  "precision": "city"
}
```

### 可信度评分
- 范围: 0.0 - 1.0
- 0.0: 完全不可信
- 1.0: 完全可信

## SDK支持

### Python SDK
```python
from sixstars_api import SixStarsClient

client = SixStarsClient(api_key="your_api_key")
entity = client.get_entity("person_001")
relations = client.get_entity_relations("person_001", depth=2)
```

### JavaScript SDK
```javascript
import { SixStarsAPI } from 'sixstars-api';

const api = new SixStarsAPI({ apiKey: 'your_api_key' });
const entity = await api.getEntity('person_001');
const conflicts = await api.analyzeNarrativeConflicts({
  timeRange: { startYear: -206, endYear: -195 }
});
```

这个API设计规范为"六行星知识图谱"提供了完整的数据访问接口，支持各种复杂的查询和分析需求。
