# 六行星知识图谱数据模型设计

## 核心实体类型

### 1. 时间实体 (Temporal)
```
节点类型: TimeNode
属性:
  - id: 唯一标识
  - year: 年份 (公元纪年)
  - month: 月份 (1-12)
  - day: 日期 (1-31)
  - dynasty: 朝代
  - reign_era: 年号
  - lunar_date: 农历日期
  - julian_day: 儒略日
  - confidence: 时间精确度 (1-5级)
```

### 2. 人物实体 (Person)
```
节点类型: PersonNode
属性:
  - id: 唯一标识
  - name: 姓名
  - courtesy_name: 字
  - posthumous_name: 谥号
  - title: 官职/爵位
  - birth_year: 出生年份
  - death_year: 去世年份
  - dynasty: 所属朝代
  - social_class: 社会阶层 (emperor/noble/official/scholar/common)
```

### 3. 地理实体 (Location)
```
节点类型: LocationNode
属性:
  - id: 唯一标识
  - name: 地名
  - ancient_name: 古代地名
  - modern_name: 现代地名
  - type: 地理类型 (capital/city/county/mountain/river)
  - coordinates: 经纬度坐标
  - administrative_level: 行政级别
```

### 4. 事件实体 (Event)
```
节点类型: EventNode
属性:
  - id: 唯一标识
  - name: 事件名称
  - type: 事件类型 (political/military/natural/cultural)
  - start_time: 开始时间
  - end_time: 结束时间
  - location: 发生地点
  - importance: 重要程度 (1-5级)
  - description: 事件描述
```

### 5. 典籍实体 (Text)
```
节点类型: TextNode
属性:
  - id: 唯一标识
  - title: 书名
  - author: 作者
  - dynasty: 成书朝代
  - category: 典籍类别 (history/philosophy/literature/science)
  - volume: 卷数
  - chapter: 章节
  - reliability: 史料可靠性 (1-5级)
```

### 6. 天象实体 (CelestialEvent)
```
节点类型: CelestialNode
属性:
  - id: 唯一标识
  - type: 天象类型 (eclipse/comet/supernova/conjunction)
  - date: 发生日期
  - visibility: 可见性
  - duration: 持续时间
  - astronomical_data: 天文数据
  - modern_verification: 现代验证结果
```

## 关系类型定义

### 1. 时间关系
```
关系类型: OCCURS_AT
起点: EventNode
终点: TimeNode
属性:
  - precision: 时间精确度
  - source: 记录来源
```

```
关系类型: CONTEMPORARY_WITH
起点: PersonNode
终点: PersonNode
属性:
  - overlap_years: 重叠年份
  - interaction_type: 交往类型
```

### 2. 空间关系
```
关系类型: LOCATED_AT
起点: EventNode/PersonNode
终点: LocationNode
属性:
  - relationship_type: 关系类型 (born_at/died_at/ruled_at/occurred_at)
  - duration: 持续时间
```

```
关系类型: ADJACENT_TO
起点: LocationNode
终点: LocationNode
属性:
  - distance: 距离
  - direction: 方向
```

### 3. 社会关系
```
关系类型: FAMILY_RELATION
起点: PersonNode
终点: PersonNode
属性:
  - relation_type: 关系类型 (father/mother/son/daughter/spouse)
  - confidence: 可信度
```

```
关系类型: POLITICAL_RELATION
起点: PersonNode
终点: PersonNode
属性:
  - relation_type: 关系类型 (ruler/minister/ally/enemy)
  - start_time: 关系开始时间
  - end_time: 关系结束时间
```

### 4. 文献关系
```
关系类型: RECORDED_IN
起点: EventNode/PersonNode
终点: TextNode
属性:
  - page_reference: 页面引用
  - reliability: 记录可靠性
  - narrative_perspective: 叙事视角 (imperial/official/celestial)
```

```
关系类型: CITES
起点: TextNode
终点: TextNode
属性:
  - citation_type: 引用类型 (direct_quote/paraphrase/reference)
  - context: 引用语境
```

### 5. 天象关系
```
关系类型: COINCIDES_WITH
起点: CelestialNode
终点: EventNode
属性:
  - temporal_proximity: 时间接近度
  - cultural_interpretation: 文化解读
  - modern_analysis: 现代分析
```

## 叙事冲突模型

### 冲突实体 (Conflict)
```
节点类型: ConflictNode
属性:
  - id: 唯一标识
  - conflict_type: 冲突类型 (sentiment/factual/temporal/causal)
  - severity: 严重程度 (1-5级)
  - time_point: 冲突时间点
  - description: 冲突描述
  - resolution_status: 解决状态 (unresolved/partially_resolved/resolved)
```

### 冲突关系
```
关系类型: CONFLICTS_WITH
起点: TextNode
终点: TextNode
属性:
  - conflict_aspect: 冲突方面 (facts/interpretation/emphasis)
  - evidence: 冲突证据
  - analysis: 分析结果
```

## 数据质量模型

### 可信度评估
```
属性模型: CredibilityScore
适用实体: 所有实体
属性:
  - source_reliability: 来源可靠性 (1-5)
  - cross_validation: 交叉验证结果 (1-5)
  - modern_verification: 现代验证 (1-5)
  - expert_consensus: 专家共识 (1-5)
  - overall_score: 综合评分 (1-5)
```

### 数据溯源
```
属性模型: DataProvenance
适用实体: 所有实体
属性:
  - original_source: 原始来源
  - extraction_method: 提取方法
  - processing_steps: 处理步骤
  - last_updated: 最后更新时间
  - version: 数据版本
```

## 索引策略

### 图数据库索引
```sql
-- 创建复合索引
CREATE INDEX person_dynasty_index FOR (p:PersonNode) ON (p.dynasty, p.name)
CREATE INDEX event_time_index FOR (e:EventNode) ON (e.start_time, e.type)
CREATE INDEX text_category_index FOR (t:TextNode) ON (t.category, t.dynasty)

-- 创建全文索引
CREATE FULLTEXT INDEX entity_search FOR (n:PersonNode|EventNode|LocationNode) ON EACH [n.name, n.description]
```

### 文档数据库索引
```javascript
// MongoDB索引设计
db.texts.createIndex({ "title": "text", "content": "text" })
db.texts.createIndex({ "dynasty": 1, "category": 1 })
db.texts.createIndex({ "author": 1, "year": 1 })

// 地理空间索引
db.locations.createIndex({ "coordinates": "2dsphere" })
```

## 数据验证规则

### 实体验证
```python
class EntityValidator:
    def validate_person(self, person_data):
        """验证人物实体数据"""
        rules = [
            self.check_birth_death_order,
            self.check_dynasty_consistency,
            self.check_title_validity
        ]
        return self.apply_rules(person_data, rules)
    
    def validate_event(self, event_data):
        """验证事件实体数据"""
        rules = [
            self.check_time_consistency,
            self.check_location_validity,
            self.check_participant_existence
        ]
        return self.apply_rules(event_data, rules)
```

### 关系验证
```python
class RelationValidator:
    def validate_temporal_relation(self, relation_data):
        """验证时间关系"""
        # 检查时间逻辑一致性
        pass
    
    def validate_family_relation(self, relation_data):
        """验证家族关系"""
        # 检查家族关系的合理性
        pass
```

## 数据导入流程

### 1. 原始数据预处理
```python
def preprocess_text_data(raw_text):
    """预处理原始文本数据"""
    # 文本清洗
    cleaned_text = clean_text(raw_text)
    
    # 实体识别
    entities = extract_entities(cleaned_text)
    
    # 关系抽取
    relations = extract_relations(cleaned_text, entities)
    
    return {
        'entities': entities,
        'relations': relations,
        'metadata': extract_metadata(raw_text)
    }
```

### 2. 数据质量检查
```python
def quality_check(processed_data):
    """数据质量检查"""
    issues = []
    
    # 实体完整性检查
    issues.extend(check_entity_completeness(processed_data['entities']))
    
    # 关系一致性检查
    issues.extend(check_relation_consistency(processed_data['relations']))
    
    # 时间逻辑检查
    issues.extend(check_temporal_logic(processed_data))
    
    return issues
```

### 3. 图数据库导入
```python
def import_to_graph_db(validated_data):
    """导入到图数据库"""
    with graph_db.session() as session:
        # 创建实体节点
        for entity in validated_data['entities']:
            session.run(create_entity_query(entity))
        
        # 创建关系
        for relation in validated_data['relations']:
            session.run(create_relation_query(relation))
```

这个数据模型设计为"六行星知识图谱"提供了完整的数据结构基础，支持复杂的历史数据分析和叙事冲突检测。
