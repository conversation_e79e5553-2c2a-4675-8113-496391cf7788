# 六行星知识图谱项目文档规范

## 📁 文档目录结构

```
docs/
├── README.md                           # 本文档规范说明
├── index.md                           # 文档索引导航
│
├── 📚 核心理论文档/
│   ├── core_insights.md              # 核心洞察记录 ⭐
│   ├── chat.md                       # 项目思想缘起
│   ├── chat2.md                      # 五行志价值重估
│   ├── wuxingzhi_analysis.md         # 二十四史五行志梳理
│   ├── astronomical_correlation_method.md  # 天文校准法
│   └── historiography_innovation.md   # 史学方法论创新
│
├── 🔧 技术实现文档/
│   ├── system_architecture.md        # 系统架构设计
│   ├── data_model.md                 # 数据模型设计
│   ├── api_design.md                 # API接口设计
│   ├── neo4j_local_hardware_requirements.md  # 硬件需求
│   └── neo4j_aura_free_tier_limits.md       # 云服务方案
│
├── 🚀 项目实施文档/
│   ├── beijing_strategy.md           # 北京实施攻略 ⭐
│   ├── target_analysis.md            # 目标机构分析
│   ├── beijing_implementation_plan.md # 具体执行方案
│   ├── team_building.md              # 团队组建方案
│   └── funding_plan.md               # 资金筹措计划
│
└── 💼 商业化文档/
    ├── startup_competitions_and_benefits.md  # 创业扶持政策
    ├── 2025_latest_intelligence.md           # 最新市场情报
    └── 2025_july_current_opportunities.md    # 当前机遇分析
```

## 📝 文档编写规范

### 1. 文件命名规范
- 使用小写字母和下划线：`file_name.md`
- 英文名称优先，中文用拼音：`wuxingzhi_analysis.md`
- 避免空格和特殊字符

### 2. 文档结构规范
```markdown
# 文档标题

## 概述/简介
- 文档目的
- 主要内容
- 适用范围

## 主要内容
### 二级标题
#### 三级标题

## 总结/结论

---
*最后更新时间：YYYY-MM-DD*
```

### 3. 内容编写规范

#### Markdown语法
- 使用标准Markdown语法
- 代码块使用三个反引号包围
- 表格使用标准表格语法
- 链接使用相对路径

#### 中英文混排
- 中英文之间加空格：`这是 English 混排`
- 数字与中文之间加空格：`共 24 史`
- 标点符号使用中文标点

#### 专业术语
- 首次出现时给出完整说明
- 统一术语表达方式
- 重要概念用**粗体**标注

### 4. 图表规范

#### 代码块
```python
# Python代码示例
def example_function():
    return "Hello World"
```

#### 表格
| 列标题1 | 列标题2 | 列标题3 |
|---------|---------|---------|
| 内容1   | 内容2   | 内容3   |

#### 引用
> 重要的引用内容使用引用格式

## 🏷️ 文档标签系统

### 重要程度标记
- ⭐ 核心文档：项目最重要的文档
- 🔥 热点文档：当前重点关注的文档
- 📋 参考文档：辅助参考的文档

### 状态标记
- ✅ 已完成：内容完整，无需修改
- 🚧 进行中：正在编写或更新
- 📝 待完善：需要补充内容
- 🔄 需更新：内容过时，需要更新

### 类型标记
- 📚 理论文档：概念、方法论、学术分析
- 🔧 技术文档：架构、代码、实现细节
- 🚀 实施文档：计划、策略、执行方案
- 💼 商业文档：市场、资金、商业模式

## 📋 文档维护流程

### 1. 新建文档
1. 确定文档类型和目录位置
2. 按照命名规范创建文件
3. 使用标准模板编写内容
4. 更新 `index.md` 索引

### 2. 更新文档
1. 修改文档内容
2. 更新文档底部的时间戳
3. 如有重大变更，更新版本号
4. 通知相关人员

### 3. 文档审核
1. 内容准确性检查
2. 格式规范性检查
3. 链接有效性检查
4. 术语一致性检查

## 🔗 文档间关联规范

### 内部链接
- 使用相对路径：`[链接文本](./other_doc.md)`
- 链接到特定章节：`[链接文本](./other_doc.md#章节标题)`
- 保持链接的有效性

### 交叉引用
- 相关文档在文末列出
- 重要概念提供链接
- 避免循环引用

### 版本控制
- 重要变更记录在文档中
- 保持向后兼容性
- 废弃内容标明状态

## 📊 文档质量标准

### 内容质量
- 信息准确完整
- 逻辑清晰连贯
- 语言简洁明了
- 实用性强

### 格式质量
- 遵循Markdown规范
- 排版美观整齐
- 结构层次清晰
- 标签使用恰当

### 维护质量
- 及时更新内容
- 修复失效链接
- 响应用户反馈
- 持续改进完善

## 🎯 特殊文档说明

### 核心洞察记录 (core_insights.md) ⭐
- 项目最重要的文档
- 记录所有重要的思想突破
- 包含项目的哲学基础和文化意义
- 需要特别维护和更新

### 北京实施攻略 (beijing_strategy.md) ⭐
- 基于"十二神将"的行动指南
- 包含具体的实施策略
- 需要根据实际情况及时调整
- 涉及敏感信息，注意保密

### 技术文档
- 保持技术细节的准确性
- 及时更新技术栈变化
- 提供完整的实现指南
- 包含必要的示例代码

### 商业文档
- 关注市场动态变化
- 及时更新政策信息
- 保护商业敏感信息
- 提供可操作的建议

---

*本文档规范将根据项目发展持续更新和完善*

**遵循规范，让文档成为项目成功的重要基石！** 📚✨
