# 太公心易兜率宫 API 参考文档

## 概述

兜率宫提供完整的RESTful API，为N8N、DIFY等Agent系统提供中华传统智慧服务。

- **Base URL**: `https://api.doushuai-palace.com/v1`
- **认证方式**: API Key / JWT Token
- **请求格式**: `application/json`
- **响应格式**: `application/json`

## 认证

### API Key认证
```http
GET /api/v1/divination/status
Authorization: Bearer YOUR_API_KEY
```

### JWT Token认证
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

## 占卜服务API

### 1. 大六壬占卜

**端点**: `POST /api/v1/divination/liuren`

**请求示例**:
```json
{
  "question": "今日运势如何？",
  "datetime": "2025-01-20T10:30:00Z",
  "options": {
    "include_interpretation": true,
    "detail_level": "full",
    "language": "zh-CN"
  }
}
```

**响应示例**:
```json
{
  "status": "success",
  "system": "大六壬",
  "question": "今日运势如何？",
  "datetime": "2025-01-20T10:30:00Z",
  "result": {
    "tianpan": {
      "year_gan": "甲",
      "month_zhi": "寅",
      "day_gan": "丙",
      "hour_zhi": "午"
    },
    "dipan": {
      "positions": [...]
    },
    "shenwill": {
      "primary": "贵人",
      "secondary": "腾蛇",
      "analysis": "..."
    },
    "interpretation": "今日运势较佳，宜主动出击...",
    "confidence": 0.85
  },
  "processing_time": 120,
  "request_id": "req_123456789"
}
```

### 2. 奇门遁甲占卜

**端点**: `POST /api/v1/divination/qimen`

**请求示例**:
```json
{
  "question": "投资决策建议",
  "datetime": "2025-01-20T14:00:00Z",
  "location": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "timezone": "Asia/Shanghai"
  },
  "options": {
    "qimen_type": "时家奇门",
    "analysis_focus": "财运"
  }
}
```

### 3. 太乙神数占卜

**端点**: `POST /api/v1/divination/taiyi`

**请求示例**:
```json
{
  "question": "国运分析",
  "datetime": "2025-01-20T12:00:00Z",
  "scope": "national",
  "options": {
    "time_range": "1_year",
    "focus_areas": ["economy", "politics", "society"]
  }
}
```

### 4. 十二龙子心易

**端点**: `POST /api/v1/divination/dragon`

**请求示例**:
```json
{
  "question": "感情运势",
  "questioner_info": {
    "birth_year": 1990,
    "birth_month": 5,
    "gender": "male",
    "zodiac": "马"
  },
  "options": {
    "dragon_selection": "auto",
    "interpretation_style": "modern"
  }
}
```

### 5. 综合占卜

**端点**: `POST /api/v1/divination/comprehensive`

**请求示例**:
```json
{
  "question": "事业发展方向",
  "datetime": "2025-01-20T09:00:00Z",
  "systems": ["liuren", "qimen", "dragon"],
  "options": {
    "analysis_depth": "deep",
    "cross_validation": true,
    "include_recommendations": true
  }
}
```

**响应示例**:
```json
{
  "status": "success",
  "question": "事业发展方向",
  "datetime": "2025-01-20T09:00:00Z",
  "individual_results": {
    "liuren": {...},
    "qimen": {...},
    "dragon": {...}
  },
  "comprehensive_analysis": {
    "consensus": "三式结果显示事业运势上升...",
    "divergence": "在时机选择上存在分歧...",
    "recommendations": [
      "建议在春季启动新项目",
      "注意与合作伙伴的沟通",
      "财务规划需要更加谨慎"
    ]
  },
  "confidence_score": 0.78,
  "processing_time": 350
}
```

## 系统管理API

### 健康检查

**端点**: `GET /api/v1/health`

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-20T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "api": "healthy",
    "database": "healthy",
    "cache": "healthy",
    "divination_engines": {
      "liuren": "healthy",
      "qimen": "healthy",
      "taiyi": "healthy",
      "dragon": "healthy"
    }
  },
  "performance": {
    "response_time": "45ms",
    "memory_usage": "256MB",
    "cpu_usage": "15%"
  }
}
```

### 服务状态

**端点**: `GET /api/v1/services`

**响应示例**:
```json
{
  "available_services": [
    {
      "name": "大六壬",
      "endpoint": "/api/v1/divination/liuren",
      "status": "available",
      "description": "天地盘推演，预测吉凶祸福"
    },
    {
      "name": "奇门遁甲",
      "endpoint": "/api/v1/divination/qimen", 
      "status": "available",
      "description": "九宫八卦布局，分析时空变化"
    }
  ],
  "total_services": 4,
  "active_services": 4
}
```

## 错误处理

### 错误响应格式

```json
{
  "status": "error",
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数不完整",
    "details": "缺少必需参数: question",
    "timestamp": "2025-01-20T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_REQUEST` | 400 | 请求参数错误 |
| `UNAUTHORIZED` | 401 | 认证失败 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `RATE_LIMITED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务暂时不可用 |

## 限流规则

- **免费用户**: 60次/分钟
- **付费用户**: 600次/分钟  
- **企业用户**: 6000次/分钟

## SDK支持

### Python SDK
```python
from doushuai_client import DoushuaiClient

client = DoushuaiClient(api_key="your_api_key")
result = client.divination.comprehensive(
    question="今日运势如何？",
    systems=["liuren", "qimen"]
)
```

### JavaScript SDK
```javascript
import { DoushuaiClient } from '@doushuai/client';

const client = new DoushuaiClient({ apiKey: 'your_api_key' });
const result = await client.divination.comprehensive({
  question: '今日运势如何？',
  systems: ['liuren', 'qimen']
});
```

## 支持与反馈

- **技术支持**: <EMAIL>
- **API文档**: https://docs.doushuai-palace.com
- **GitHub Issues**: https://github.com/your-org/taigong-xinyi-doushuai/issues
