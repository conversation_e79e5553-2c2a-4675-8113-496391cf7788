# 六行星知识图谱系统架构设计

## 总体架构概览

### 系统分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│                    应用服务层 (Service Layer)                 │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Layer)                │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access Layer)             │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (Storage Layer)                 │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件设计

### 1. 数据存储层 (Storage Layer)

#### 知识图谱数据库
- **技术选型**: Neo4j / ArangoDB
- **存储内容**: 实体、关系、属性
- **索引策略**: 全文检索 + 图结构索引

#### 文档数据库
- **技术选型**: MongoDB / Elasticsearch
- **存储内容**: 原始典籍文本、标注数据
- **检索能力**: 全文搜索、语义检索

#### 时序数据库
- **技术选型**: InfluxDB
- **存储内容**: 天象数据、历史事件时间线
- **查询能力**: 时间范围查询、趋势分析

### 2. 数据访问层 (Data Access Layer)

#### 图数据库访问模块
```python
class GraphDBAccess:
    def __init__(self, connection_string):
        self.driver = neo4j.GraphDatabase.driver(connection_string)
    
    def find_entity_relations(self, entity_id, relation_type=None):
        """查找实体的关联关系"""
        pass
    
    def execute_cypher_query(self, query, parameters=None):
        """执行Cypher查询"""
        pass
    
    def find_narrative_conflicts(self, time_range, entities):
        """查找叙事冲突"""
        pass
```

#### 文档检索模块
```python
class DocumentRetrieval:
    def __init__(self, es_client):
        self.es_client = es_client
    
    def semantic_search(self, query, filters=None):
        """语义搜索"""
        pass
    
    def full_text_search(self, keywords, source_filter=None):
        """全文检索"""
        pass
```

### 3. 业务逻辑层 (Business Layer)

#### 天文校准引擎
```python
class AstronomicalCorrelation:
    def __init__(self):
        self.astronomical_calculator = AstronomicalCalculator()
        self.historical_records = HistoricalRecords()
    
    def correlate_celestial_events(self, historical_event):
        """将历史事件与天象进行关联"""
        celestial_events = self.find_celestial_events(
            historical_event.time_range
        )
        return self.analyze_correlation(historical_event, celestial_events)
    
    def detect_narrative_conflicts(self, time_point):
        """检测叙事冲突"""
        imperial_narrative = self.get_imperial_narrative(time_point)
        celestial_narrative = self.get_celestial_narrative(time_point)
        return self.compare_narratives(imperial_narrative, celestial_narrative)
```

#### 叙事分析引擎
```python
class NarrativeAnalysis:
    def __init__(self):
        self.sentiment_analyzer = SentimentAnalyzer()
        self.entity_extractor = EntityExtractor()
    
    def analyze_triple_narrative(self, time_point):
        """三层叙事分析：帝王层、臣子层、天道层"""
        return {
            'imperial': self.analyze_imperial_layer(time_point),
            'official': self.analyze_official_layer(time_point),
            'celestial': self.analyze_celestial_layer(time_point)
        }
    
    def detect_bias_patterns(self, source_type, time_range):
        """检测史料偏向模式"""
        pass
```

#### 知识推理引擎
```python
class KnowledgeReasoning:
    def __init__(self, graph_db):
        self.graph_db = graph_db
        self.reasoning_rules = self.load_reasoning_rules()
    
    def infer_hidden_relations(self, entity1, entity2):
        """推理隐含关系"""
        paths = self.graph_db.find_paths(entity1, entity2)
        return self.apply_reasoning_rules(paths)
    
    def temporal_reasoning(self, events):
        """时序推理"""
        pass
```

### 4. 应用服务层 (Service Layer)

#### RESTful API服务
```python
from flask import Flask, jsonify, request

app = Flask(__name__)

@app.route('/api/v1/entities/<entity_id>/relations')
def get_entity_relations(entity_id):
    """获取实体关系"""
    pass

@app.route('/api/v1/search/semantic')
def semantic_search():
    """语义搜索接口"""
    query = request.args.get('q')
    results = search_service.semantic_search(query)
    return jsonify(results)

@app.route('/api/v1/analysis/narrative-conflict')
def analyze_narrative_conflict():
    """叙事冲突分析接口"""
    time_point = request.args.get('time')
    conflicts = narrative_service.detect_conflicts(time_point)
    return jsonify(conflicts)
```

#### GraphQL API服务
```graphql
type Query {
  entity(id: ID!): Entity
  searchEntities(query: String!, limit: Int): [Entity]
  narrativeConflicts(timeRange: TimeRange!): [NarrativeConflict]
  celestialEvents(timeRange: TimeRange!): [CelestialEvent]
}

type Entity {
  id: ID!
  name: String!
  type: EntityType!
  properties: [Property]
  relations: [Relation]
}

type NarrativeConflict {
  timePoint: DateTime!
  conflictType: ConflictType!
  sources: [Source]
  description: String!
}
```

### 5. 用户界面层 (UI Layer)

#### Web前端架构
- **框架**: React.js + TypeScript
- **状态管理**: Redux Toolkit
- **UI组件库**: Ant Design
- **图可视化**: D3.js + Cytoscape.js

#### 移动端架构
- **跨平台方案**: React Native
- **原生优化**: 针对iOS/Android的性能优化

## 技术栈选型

### 后端技术栈
- **编程语言**: Python 3.9+
- **Web框架**: Flask / FastAPI
- **图数据库**: Neo4j
- **文档数据库**: MongoDB + Elasticsearch
- **缓存**: Redis
- **消息队列**: RabbitMQ / Apache Kafka

### 前端技术栈
- **框架**: React.js 18+
- **构建工具**: Vite
- **CSS框架**: Tailwind CSS
- **图表库**: ECharts + D3.js

### 基础设施
- **容器化**: Docker + Kubernetes
- **CI/CD**: GitLab CI / GitHub Actions
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 性能优化策略

### 1. 数据库优化
- 图数据库索引优化
- 查询语句优化
- 数据分片策略

### 2. 缓存策略
- Redis缓存热点数据
- CDN缓存静态资源
- 应用层缓存查询结果

### 3. 并发处理
- 异步任务处理
- 连接池管理
- 负载均衡

## 安全设计

### 1. 认证授权
- JWT Token认证
- RBAC权限控制
- OAuth2.0集成

### 2. 数据安全
- 数据加密存储
- 传输层加密(HTTPS)
- 敏感数据脱敏

### 3. 系统安全
- API限流
- SQL注入防护
- XSS攻击防护

## 部署架构

### 生产环境部署
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│   Web Servers   │────│   App Servers   │
│    (Nginx)      │    │   (Nginx)       │    │   (Gunicorn)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Graph DB      │    │   Document DB   │
                       │   (Neo4j)       │    │   (MongoDB)     │
                       └─────────────────┘    └─────────────────┘
```

### 开发环境部署
- Docker Compose一键部署
- 本地开发环境配置
- 测试数据初始化脚本
