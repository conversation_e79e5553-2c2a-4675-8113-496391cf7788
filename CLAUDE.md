# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "太公心易兜率宫" - a **simple Streamlit application** for traditional Chinese divination (术数). It provides interactive interfaces for various Chinese divination methods using local Python implementations.

## Key Technologies

- **Frontend**: Streamlit (only interface needed)
- **Language**: Python 3.9+
- **Chinese Text Processing**: jieba, pypinyin, opencc-python-reimplemented, sxtwl
- **Divination Systems**: Local Python modules in `/core/` directory

## Project Structure

```
5-planets/
├── shushu/                    # Main Streamlit application - THE CORE APP
│   ├── app.py                # Main application entry point
│   ├── core/                 # Local divination algorithms
│   │   ├── qimen.py         # 奇门遁甲 calculations  
│   │   ├── taiyi.py         # 太乙神数 implementations
│   │   └── meihua.py        # 梅花易数 algorithms
│   ├── pages/               # Streamlit pages
│   └── requirements.txt     # App dependencies
└── shushubook/              # 200+ traditional Chinese divination texts
```

## Development Commands

### Quick Start (Only What You Need!)
```bash
# 1. Set up environment
python3 -m venv .venv
source .venv/bin/activate  # Linux/Mac

# 2. Install dependencies
cd shushu
pip install -r requirements.txt

# 3. Run the app
streamlit run app.py --server.port 8501
# OR use the provided script:
./start_8501.sh
```

### Access the Application
Open your browser to: `http://localhost:8501`

## Key Components

### Divination Systems (占卜系统)
- **大六壬 (Da Liu Ren)**: Using kinliuren package (if available)
- **奇门遁甲 (Qi Men Dun Jia)**: Local module `core.qimen`
- **太乙神数 (Tai Yi Shen Shu)**: Local module `core.taiyi`  
- **梅花心易 (Meihua Yi)**: Local module `core.meihua`

### Module Loading System
The app automatically checks module availability on startup:
- Shows ✅ for available modules
- Shows ❌ for unavailable modules
- Gracefully handles missing dependencies

## Important Notes

### Module Import Management
- `app.py` handles all path configuration
- Local modules are loaded from `/core/` directory
- Import errors are caught and displayed to user

### Chinese Text Features
- Traditional/Simplified conversion (opencc)
- Chinese calendar calculations (sxtwl)
- Pinyin processing (pypinyin)
- Chinese text segmentation (jieba)

### Dependencies
Core dependencies in `shushu/requirements.txt`:
- `streamlit` - Web interface
- `sxtwl` - Chinese calendar
- `jieba` - Chinese text processing
- `pypinyin` - Pinyin conversion
- `kinliuren` - 大六壬 (optional PyPI package)

## Troubleshooting

### Common Issues
- **Module import errors**: Check that files exist in `shushu/core/` directory
- **Chinese encoding**: Ensure UTF-8 environment
- **Package missing**: Install from `shushu/requirements.txt`

### Development Tips
- Run `python app.py` to test module imports without starting Streamlit
- Check module status in sidebar when app starts
- Missing modules will show error messages but won't crash the app

## That's It!

This is a **standalone Streamlit application** - no databases, no Docker, no APIs needed. Just install Python dependencies and run!