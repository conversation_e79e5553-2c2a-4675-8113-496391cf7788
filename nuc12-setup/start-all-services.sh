#!/bin/bash
# NUC12 六行星知识图谱服务启动脚本
# 一键启动所有Docker服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查系统资源
check_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    total_mem=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$total_mem" -lt 32 ]; then
        log_warning "系统内存少于32GB，可能影响性能"
    else
        log_success "内存充足: ${total_mem}GB"
    fi
    
    # 检查磁盘空间
    available_space=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$available_space" -lt 100 ]; then
        log_warning "可用磁盘空间少于100GB，请清理磁盘"
    else
        log_success "磁盘空间充足: ${available_space}GB"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录..."
    
    directories=(
        "data/neo4j/data"
        "data/neo4j/logs"
        "data/neo4j/import"
        "data/neo4j/plugins"
        "data/mongodb"
        "data/postgresql"
        "data/elasticsearch"
        "data/redis"
        "data/grafana"
        "data/prometheus"
        "data/portainer"
        "data/pgadmin"
        "data/nginx/logs"
        "data/streamlit-cache"
        "jupyter-notebooks"
        "ssl"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
    
    # 设置权限
    chmod -R 755 data/
    
    log_success "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_warning ".env文件不存在，从.env.example复制"
            cp .env.example .env
            log_warning "请编辑.env文件，设置正确的配置"
        else
            log_error ".env.example文件不存在"
            exit 1
        fi
    fi
    
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
    
    log_success "配置文件检查完成"
}

# 拉取Docker镜像
pull_images() {
    log_info "拉取Docker镜像..."
    
    # 预先拉取镜像以避免启动时等待
    images=(
        "neo4j:5.15-community"
        "mongo:7.0"
        "mongo-express:1.0.0"
        "postgres:16"
        "dpage/pgadmin4:8.0"
        "docker.elastic.co/elasticsearch/elasticsearch:8.11.0"
        "docker.elastic.co/kibana/kibana:8.11.0"
        "redis:7.2-alpine"
        "jupyter/datascience-notebook:latest"
        "prom/prometheus:v2.47.0"
        "grafana/grafana:10.2.0"
        "portainer/portainer-ce:2.19.0"
        "nginx:1.25-alpine"
    )
    
    for image in "${images[@]}"; do
        log_info "拉取镜像: $image"
        docker pull "$image" || log_warning "拉取镜像失败: $image"
    done
    
    log_success "镜像拉取完成"
}

# 启动核心数据库服务
start_databases() {
    log_info "启动核心数据库服务..."
    
    # 按顺序启动数据库服务
    services=("neo4j" "mongodb" "postgresql" "redis")
    
    for service in "${services[@]}"; do
        log_info "启动服务: $service"
        docker-compose up -d "$service"
        
        # 等待服务启动
        sleep 10
        
        # 检查服务状态
        if docker-compose ps "$service" | grep -q "Up"; then
            log_success "$service 启动成功"
        else
            log_error "$service 启动失败"
            docker-compose logs "$service"
        fi
    done
}

# 启动搜索引擎
start_search_engines() {
    log_info "启动搜索引擎..."
    
    docker-compose up -d elasticsearch
    sleep 15  # Elasticsearch需要更长时间启动
    
    if docker-compose ps elasticsearch | grep -q "Up"; then
        log_success "Elasticsearch 启动成功"
        docker-compose up -d kibana
    else
        log_error "Elasticsearch 启动失败"
        docker-compose logs elasticsearch
    fi
}

# 启动应用服务
start_applications() {
    log_info "启动应用服务..."
    
    # 启动管理界面
    docker-compose up -d mongo-express pgadmin
    
    # 启动Jupyter
    docker-compose up -d jupyter
    
    # 构建并启动Streamlit应用
    log_info "构建Streamlit应用..."
    docker-compose up -d streamlit-app
    
    log_success "应用服务启动完成"
}

# 启动监控服务
start_monitoring() {
    log_info "启动监控服务..."
    
    docker-compose up -d prometheus
    sleep 5
    docker-compose up -d grafana
    docker-compose up -d portainer
    
    log_success "监控服务启动完成"
}

# 启动网关服务
start_gateway() {
    log_info "启动API网关..."
    
    docker-compose up -d nginx
    
    log_success "API网关启动完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态概览:"
    echo
    docker-compose ps
    echo
    
    log_info "服务访问地址:"
    echo -e "${GREEN}核心服务:${NC}"
    echo "  Neo4j Browser: http://$(hostname -I | awk '{print $1}'):7474"
    echo "  MongoDB Express: http://$(hostname -I | awk '{print $1}'):8081"
    echo "  pgAdmin: http://$(hostname -I | awk '{print $1}'):5050"
    echo "  Elasticsearch: http://$(hostname -I | awk '{print $1}'):9200"
    echo
    echo -e "${GREEN}应用服务:${NC}"
    echo "  Streamlit排盘: http://$(hostname -I | awk '{print $1}'):8501"
    echo "  Jupyter Lab: http://$(hostname -I | awk '{print $1}'):8888"
    echo
    echo -e "${GREEN}监控服务:${NC}"
    echo "  Grafana: http://$(hostname -I | awk '{print $1}'):3000"
    echo "  Prometheus: http://$(hostname -I | awk '{print $1}'):9090"
    echo "  Portainer: http://$(hostname -I | awk '{print $1}'):9000"
    echo
    echo -e "${YELLOW}默认登录信息:${NC}"
    echo "  用户名: admin"
    echo "  密码: sixstars123"
}

# 主函数
main() {
    echo "🚀 NUC12 六行星知识图谱服务启动脚本"
    echo "================================================"
    
    # 检查环境
    check_docker
    check_resources
    
    # 准备环境
    create_directories
    check_config
    
    # 拉取镜像
    pull_images
    
    # 分阶段启动服务
    start_databases
    start_search_engines
    start_applications
    start_monitoring
    start_gateway
    
    # 显示状态
    show_status
    
    echo
    log_success "🎉 所有服务启动完成!"
    log_info "请等待2-3分钟让所有服务完全启动"
    log_info "使用 './check-services.sh' 检查服务健康状态"
}

# 执行主函数
main "$@"
