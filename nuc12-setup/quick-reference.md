# NUC12 快速参考卡片

## 🚀 一键命令

```bash
# 启动所有服务
./start-all-services.sh

# 检查服务状态  
./check-services.sh

# 停止所有服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启特定服务
docker-compose restart <服务名>
```

## 🔗 服务访问地址

| 服务 | 地址 | 用户名 | 密码 |
|------|------|--------|------|
| Neo4j | http://IP:7474 | neo4j | sixstars123 |
| MongoDB Express | http://IP:8081 | admin | sixstars123 |
| pgAdmin | http://IP:5050 | <EMAIL> | sixstars123 |
| Streamlit | http://IP:8501 | - | - |
| Jupyter | http://IP:8888 | - | sixstars123 |
| Grafana | http://IP:3000 | admin | sixstars123 |
| Portainer | http://IP:9000 | admin | sixstars123 |

## 📊 资源分配

```
内存分配 (64GB总量):
├── Neo4j: 8GB
├── MongoDB: 4GB  
├── PostgreSQL: 2GB
├── Elasticsearch: 4GB
├── 其他服务: 8GB
└── 系统预留: 38GB

存储分配 (1TB SSD):
├── 系统: 100GB
├── Docker: 50GB
├── 数据库: 20GB
└── 可用: 830GB
```

## 🛠️ 常用维护命令

```bash
# 查看容器状态
docker ps -a

# 查看资源使用
docker stats

# 清理无用镜像
docker system prune -f

# 备份数据库
./backup-databases.sh

# 更新服务
docker-compose pull
docker-compose up -d
```

## 🔧 故障排除

```bash
# 服务无法启动
docker-compose logs <服务名>

# 端口被占用
sudo netstat -tulpn | grep <端口>

# 内存不足
free -h
docker stats

# 磁盘空间不足
df -h
docker system df
```

## 📱 手机访问

1. 确保NUC12和手机在同一网络
2. 使用NUC12的IP地址访问服务
3. 推荐使用Chrome浏览器

## 🎯 性能监控

- **Grafana**: http://IP:3000 (系统监控)
- **Portainer**: http://IP:9000 (容器管理)
- **命令行**: `htop`, `docker stats`

---
**保存这个文件到手机，随时查看！** 📱
