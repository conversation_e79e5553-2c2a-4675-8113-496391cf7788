#!/bin/bash
# NUC12 六行星知识图谱服务健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取本机IP
HOST_IP=$(hostname -I | awk '{print $1}')

# 服务配置
declare -A SERVICES=(
    ["neo4j"]="7474:/browser/ Neo4j图数据库"
    ["mongodb"]="27017: MongoDB文档数据库"
    ["postgresql"]="5432: PostgreSQL关系数据库"
    ["elasticsearch"]="9200:/ Elasticsearch搜索引擎"
    ["redis"]="6379: Redis缓存"
    ["streamlit-app"]="8501: Streamlit排盘应用"
    ["jupyter"]="8888: Jupyter数据分析"
    ["grafana"]="3000: Grafana监控面板"
    ["prometheus"]="9090: Prometheus指标收集"
    ["portainer"]="9000: Portainer容器管理"
    ["mongo-express"]="8081: MongoDB管理界面"
    ["pgadmin"]="5050: PostgreSQL管理界面"
    ["kibana"]="5601: Kibana搜索界面"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查Docker容器状态
check_container_status() {
    log_info "检查Docker容器状态..."
    echo
    
    local all_running=true
    
    for service in "${!SERVICES[@]}"; do
        local status=$(docker-compose ps -q "$service" 2>/dev/null)
        
        if [ -n "$status" ]; then
            local container_status=$(docker inspect --format='{{.State.Status}}' "$status" 2>/dev/null)
            local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$status" 2>/dev/null)
            
            if [ "$container_status" = "running" ]; then
                if [ "$health_status" = "healthy" ] || [ "$health_status" = "<no value>" ]; then
                    log_success "$service: 运行中"
                else
                    log_warning "$service: 运行中但健康检查失败 ($health_status)"
                    all_running=false
                fi
            else
                log_error "$service: 未运行 ($container_status)"
                all_running=false
            fi
        else
            log_error "$service: 容器不存在"
            all_running=false
        fi
    done
    
    echo
    if [ "$all_running" = true ]; then
        log_success "所有容器运行正常"
    else
        log_warning "部分容器存在问题"
    fi
    
    return $all_running
}

# 检查端口连通性
check_port_connectivity() {
    log_info "检查端口连通性..."
    echo
    
    local ports=(7474 27017 5432 9200 6379 8501 8888 3000 9090 9000 8081 5050 5601)
    local all_accessible=true
    
    for port in "${ports[@]}"; do
        if timeout 3 bash -c "</dev/tcp/$HOST_IP/$port" 2>/dev/null; then
            log_success "端口 $port: 可访问"
        else
            log_error "端口 $port: 不可访问"
            all_accessible=false
        fi
    done
    
    echo
    if [ "$all_accessible" = true ]; then
        log_success "所有端口连通正常"
    else
        log_warning "部分端口不可访问"
    fi
    
    return $all_accessible
}

# 检查HTTP服务响应
check_http_services() {
    log_info "检查HTTP服务响应..."
    echo
    
    local http_services=(
        "7474:/browser/ Neo4j"
        "8081:/ MongoDB-Express"
        "5050:/ pgAdmin"
        "9200:/ Elasticsearch"
        "8501:/ Streamlit"
        "8888:/ Jupyter"
        "3000:/ Grafana"
        "9090:/ Prometheus"
        "9000:/ Portainer"
        "5601:/ Kibana"
    )
    
    local all_responding=true
    
    for service_info in "${http_services[@]}"; do
        local port=$(echo "$service_info" | cut -d: -f1)
        local path=$(echo "$service_info" | cut -d: -f2 | cut -d' ' -f1)
        local name=$(echo "$service_info" | cut -d' ' -f2)
        
        local url="http://$HOST_IP:$port$path"
        local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$url" 2>/dev/null || echo "000")
        
        if [ "$response" -ge 200 ] && [ "$response" -lt 400 ]; then
            log_success "$name ($port): HTTP $response"
        elif [ "$response" -ge 400 ] && [ "$response" -lt 500 ]; then
            log_warning "$name ($port): HTTP $response (可能需要认证)"
        else
            log_error "$name ($port): 无响应或错误 ($response)"
            all_responding=false
        fi
    done
    
    echo
    if [ "$all_responding" = true ]; then
        log_success "所有HTTP服务响应正常"
    else
        log_warning "部分HTTP服务无响应"
    fi
    
    return $all_responding
}

# 检查数据库连接
check_database_connections() {
    log_info "检查数据库连接..."
    echo
    
    # 检查Neo4j
    local neo4j_status=$(curl -s -u neo4j:sixstars123 "http://$HOST_IP:7474/db/data/" | jq -r '.neo4j_version' 2>/dev/null || echo "error")
    if [ "$neo4j_status" != "error" ] && [ "$neo4j_status" != "null" ]; then
        log_success "Neo4j: 连接正常 (版本: $neo4j_status)"
    else
        log_error "Neo4j: 连接失败"
    fi
    
    # 检查MongoDB
    if docker exec sixstars-mongodb mongosh --eval "db.adminCommand('ping')" --quiet >/dev/null 2>&1; then
        log_success "MongoDB: 连接正常"
    else
        log_error "MongoDB: 连接失败"
    fi
    
    # 检查PostgreSQL
    if docker exec sixstars-postgresql pg_isready -U sixstars >/dev/null 2>&1; then
        log_success "PostgreSQL: 连接正常"
    else
        log_error "PostgreSQL: 连接失败"
    fi
    
    # 检查Redis
    if docker exec sixstars-redis redis-cli -a sixstars123 ping 2>/dev/null | grep -q "PONG"; then
        log_success "Redis: 连接正常"
    else
        log_error "Redis: 连接失败"
    fi
    
    # 检查Elasticsearch
    local es_status=$(curl -s "http://$HOST_IP:9200/_cluster/health" | jq -r '.status' 2>/dev/null || echo "error")
    if [ "$es_status" = "green" ] || [ "$es_status" = "yellow" ]; then
        log_success "Elasticsearch: 连接正常 (状态: $es_status)"
    else
        log_error "Elasticsearch: 连接失败"
    fi
    
    echo
}

# 检查系统资源使用
check_resource_usage() {
    log_info "检查系统资源使用..."
    echo
    
    # 内存使用
    local mem_info=$(free -h | grep "Mem:")
    local mem_used=$(echo "$mem_info" | awk '{print $3}')
    local mem_total=$(echo "$mem_info" | awk '{print $2}')
    log_info "内存使用: $mem_used / $mem_total"
    
    # 磁盘使用
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}')
    log_info "磁盘使用: $disk_usage"
    
    # Docker资源使用
    log_info "Docker容器资源使用:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -10
    
    echo
}

# 显示服务访问信息
show_access_info() {
    log_info "服务访问信息:"
    echo
    
    echo -e "${GREEN}🔗 核心数据库服务:${NC}"
    echo "  Neo4j Browser:    http://$HOST_IP:7474"
    echo "  MongoDB Express:  http://$HOST_IP:8081"
    echo "  pgAdmin:          http://$HOST_IP:5050"
    echo "  Kibana:           http://$HOST_IP:5601"
    echo
    
    echo -e "${GREEN}📱 应用服务:${NC}"
    echo "  Streamlit排盘:    http://$HOST_IP:8501"
    echo "  Jupyter Lab:      http://$HOST_IP:8888"
    echo
    
    echo -e "${GREEN}📊 监控服务:${NC}"
    echo "  Grafana:          http://$HOST_IP:3000"
    echo "  Prometheus:       http://$HOST_IP:9090"
    echo "  Portainer:        http://$HOST_IP:9000"
    echo
    
    echo -e "${YELLOW}🔐 默认登录信息:${NC}"
    echo "  用户名: admin"
    echo "  密码: sixstars123"
    echo "  Neo4j: neo4j/sixstars123"
    echo "  Jupyter Token: sixstars123"
    echo
}

# 生成健康报告
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="health-report-$(date '+%Y%m%d-%H%M%S').txt"
    
    {
        echo "NUC12 六行星知识图谱服务健康报告"
        echo "生成时间: $timestamp"
        echo "主机IP: $HOST_IP"
        echo "========================================"
        echo
        
        echo "容器状态:"
        docker-compose ps
        echo
        
        echo "资源使用:"
        free -h
        echo
        df -h
        echo
        
        echo "Docker统计:"
        docker stats --no-stream
        echo
        
    } > "$report_file"
    
    log_success "健康报告已生成: $report_file"
}

# 主函数
main() {
    echo "🔍 NUC12 六行星知识图谱服务健康检查"
    echo "================================================"
    echo "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "主机IP: $HOST_IP"
    echo
    
    # 执行各项检查
    check_container_status
    check_port_connectivity
    check_http_services
    check_database_connections
    check_resource_usage
    
    # 显示访问信息
    show_access_info
    
    # 生成报告
    if [ "$1" = "--report" ]; then
        generate_health_report
    fi
    
    echo
    log_success "🎉 健康检查完成!"
    log_info "如需生成详细报告，请运行: $0 --report"
}

# 执行主函数
main "$@"
