# NUC12 硬件规格与性能分析

## 🖥️ 硬件配置

### 基础配置
- **型号**: Intel NUC12 (具体型号待确认)
- **CPU**: Intel 12代处理器 (具体型号待确认)
- **内存**: 64GB DDR4/DDR5
- **存储**: 1TB NVMe SSD
- **网络**: 千兆以太网 + Wi-Fi 6
- **扩展存储**: 8TB NAS

### 预估性能指标
```
CPU性能: 
- 多核处理能力强，适合并发数据库操作
- 支持虚拟化，Docker容器运行效率高

内存性能:
- 64GB大内存，支持多个数据库同时运行
- 足够的缓存空间，提升查询性能

存储性能:
- NVMe SSD随机读写性能优秀
- 适合数据库频繁的随机访问模式
- 8TB NAS提供海量备份空间
```

## 📊 性能基准测试计划

### 1. CPU基准测试
```bash
# 安装测试工具
sudo apt update
sudo apt install sysbench stress-ng

# CPU多核性能测试
sysbench cpu --cpu-max-prime=20000 --threads=8 run

# CPU压力测试
stress-ng --cpu 8 --timeout 60s --metrics-brief
```

### 2. 内存基准测试
```bash
# 内存带宽测试
sysbench memory --memory-block-size=1M --memory-total-size=10G run

# 内存延迟测试
sudo apt install mbw
mbw -n 5 1000
```

### 3. 存储基准测试
```bash
# 安装存储测试工具
sudo apt install fio

# 随机读写测试 (模拟数据库负载)
fio --name=random-rw --ioengine=libaio --iodepth=32 --rw=randrw \
    --rwmixread=70 --bs=4k --direct=1 --size=1G --numjobs=4 \
    --runtime=60 --group_reporting

# 顺序读写测试
fio --name=sequential-rw --ioengine=libaio --iodepth=32 --rw=rw \
    --rwmixread=70 --bs=1M --direct=1 --size=1G --numjobs=1 \
    --runtime=60 --group_reporting
```

### 4. 网络基准测试
```bash
# 安装网络测试工具
sudo apt install iperf3

# 网络带宽测试 (需要另一台机器作为服务端)
iperf3 -c <server-ip> -t 60

# 网络延迟测试
ping -c 100 <target-ip>
```

## 🎯 数据库性能优化配置

### Neo4j优化配置
```properties
# neo4j.conf 优化参数
dbms.memory.heap.initial_size=8G
dbms.memory.heap.max_size=8G
dbms.memory.pagecache.size=16G
dbms.tx_log.rotation.retention_policy=1G size

# 并发配置
dbms.threads.worker_count=8
dbms.connector.bolt.thread_pool_min_size=5
dbms.connector.bolt.thread_pool_max_size=400

# 查询优化
cypher.default_language_version=5
cypher.hints_error=true
cypher.lenient_create_relationship=false
```

### MongoDB优化配置
```yaml
# mongod.conf 优化参数
storage:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 8
      journalCompressor: snappy
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp

net:
  maxIncomingConnections: 1000
  compression:
    compressors: snappy,zstd
```

### PostgreSQL优化配置
```postgresql
# postgresql.conf 优化参数
shared_buffers = 4GB
effective_cache_size = 12GB
maintenance_work_mem = 1GB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 64MB
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4
```

## 🔧 系统级优化

### 1. 内核参数优化
```bash
# /etc/sysctl.conf 添加以下参数

# 内存管理
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.overcommit_memory = 1

# 网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr

# 文件系统优化
fs.file-max = 2097152
fs.nr_open = 1048576

# 应用生效
sudo sysctl -p
```

### 2. 文件系统优化
```bash
# SSD优化
sudo systemctl enable fstrim.timer

# 挂载选项优化 (添加到 /etc/fstab)
# /dev/nvme0n1p1 / ext4 defaults,noatime,nodiratime,discard 0 1

# I/O调度器优化
echo mq-deadline | sudo tee /sys/block/nvme0n1/queue/scheduler
```

### 3. Docker优化
```json
// /etc/docker/daemon.json
{
  "storage-driver": "overlay2",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 64000,
      "Soft": 64000
    }
  },
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 5
}
```

## 📈 性能监控指标

### 关键监控指标
```yaml
系统指标:
  - CPU使用率 (目标: <80%)
  - 内存使用率 (目标: <85%)
  - 磁盘I/O (目标: <80% util)
  - 网络带宽 (目标: <70%)

数据库指标:
  Neo4j:
    - 查询响应时间 (目标: <100ms)
    - 并发连接数 (目标: <200)
    - 内存使用 (目标: <8GB)
  
  MongoDB:
    - 操作延迟 (目标: <50ms)
    - 连接数 (目标: <500)
    - 缓存命中率 (目标: >95%)
  
  PostgreSQL:
    - 查询时间 (目标: <100ms)
    - 连接数 (目标: <100)
    - 缓冲区命中率 (目标: >99%)

应用指标:
  - Streamlit响应时间 (目标: <2s)
  - API响应时间 (目标: <500ms)
  - 错误率 (目标: <1%)
```

## 🚀 性能测试脚本

### 数据库压力测试
```bash
#!/bin/bash
# database-stress-test.sh

echo "开始数据库压力测试..."

# Neo4j压力测试
echo "测试Neo4j性能..."
docker exec sixstars-neo4j cypher-shell -u neo4j -p sixstars123 \
  "MATCH (n) RETURN count(n)" --format verbose

# MongoDB压力测试
echo "测试MongoDB性能..."
docker exec sixstars-mongodb mongosh --eval "
  db.test.drop();
  for(let i=0; i<10000; i++) {
    db.test.insertOne({index: i, data: 'test data ' + i});
  }
  db.test.find().limit(10).explain('executionStats');
"

# PostgreSQL压力测试
echo "测试PostgreSQL性能..."
docker exec sixstars-postgresql psql -U sixstars -d sixstars_analytics -c "
  DROP TABLE IF EXISTS test_table;
  CREATE TABLE test_table (id SERIAL PRIMARY KEY, data TEXT);
  INSERT INTO test_table (data) 
  SELECT 'test data ' || generate_series(1,10000);
  EXPLAIN ANALYZE SELECT * FROM test_table WHERE id < 1000;
"

echo "压力测试完成!"
```

## 📋 硬件验收清单

### 开箱验收
- [ ] 外观检查无损坏
- [ ] 配件齐全 (电源、说明书等)
- [ ] 序列号记录

### 硬件检测
- [ ] CPU型号和频率确认
- [ ] 内存容量和频率检测
- [ ] 存储容量和接口确认
- [ ] 网络接口测试
- [ ] USB接口测试

### 性能验证
- [ ] CPU基准测试通过
- [ ] 内存测试无错误
- [ ] 存储读写性能达标
- [ ] 网络连接稳定
- [ ] 温度控制正常

### 系统安装
- [ ] 操作系统安装完成
- [ ] 驱动程序安装
- [ ] 基础软件配置
- [ ] Docker环境搭建
- [ ] 服务部署测试

---

**准备好释放NUC12的全部潜力了吗？** 💪
