# NUC12 六行星知识图谱服务器配置指南

> **硬件配置**: Intel NUC12 + 64GB RAM + 1TB SSD + 8TB NAS  
> **目标**: 部署完整的二十四史知识图谱系统  
> **方案**: Docker容器化部署，一键启动所有服务  

## 🎯 总体架构

```
NUC12 服务架构
├── 核心数据库
│   ├── Neo4j (图数据库) - 人物关系网络
│   ├── MongoDB (文档数据库) - 原文存储
│   └── PostgreSQL (关系数据库) - 统计分析
├── 搜索引擎
│   └── Elasticsearch - 全文搜索
├── 缓存层
│   └── Redis - 查询缓存
├── 应用服务
│   ├── Streamlit - 排盘应用
│   ├── Jupyter - 数据分析
│   └── API Gateway - 统一接口
└── 监控运维
    ├── Grafana - 可视化监控
    ├── Prometheus - 指标收集
    └── Portainer - Docker管理
```

## 📊 资源分配规划

### 内存分配 (64GB总量)
```
服务名称          内存分配    用途
Neo4j            8GB        图数据库主力
MongoDB          4GB        文档存储
PostgreSQL       2GB        统计分析
Elasticsearch    4GB        全文搜索
Redis            1GB        缓存
Streamlit        2GB        排盘应用
Jupyter          2GB        数据分析
监控服务         2GB        Grafana+Prometheus
系统预留         8GB        操作系统
其他服务         31GB       扩展空间
```

### 存储分配 (1TB SSD + 8TB NAS)
```
SSD (1TB) - 高速存储:
├── 操作系统: 100GB
├── Docker镜像: 50GB
├── 数据库数据: 20GB
│   ├── Neo4j: 5GB
│   ├── MongoDB: 10GB
│   └── PostgreSQL: 5GB
├── 应用代码: 10GB
├── 日志文件: 20GB
└── 缓存空间: 800GB

NAS (8TB) - 大容量存储:
├── 原始数据备份: 100GB
├── 数据库备份: 500GB
├── 历史版本: 1TB
├── 扩展数据: 2TB
└── 其他用途: 4.4TB
```

## 🐳 Docker服务清单

详细的Docker配置请查看各个专门的配置文件：

1. **[docker-compose.yml](./docker-compose.yml)** - 主要服务编排
2. **[neo4j-config/](./neo4j-config/)** - Neo4j配置
3. **[mongodb-config/](./mongodb-config/)** - MongoDB配置
4. **[postgresql-config/](./postgresql-config/)** - PostgreSQL配置
5. **[elasticsearch-config/](./elasticsearch-config/)** - Elasticsearch配置
6. **[monitoring/](./monitoring/)** - 监控服务配置

## 🚀 快速启动指南

### 1. 系统准备
```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 克隆配置
```bash
# 将这个配置目录复制到NUC12
scp -r nuc12-setup/ user@nuc12-ip:/home/<USER>/
```

### 3. 一键启动
```bash
cd nuc12-setup
./start-all-services.sh
```

### 4. 验证服务
```bash
./check-services.sh
```

## 📋 服务访问地址

启动后可通过以下地址访问各服务：

```
核心服务:
- Neo4j Browser: http://nuc12-ip:7474
- MongoDB Express: http://nuc12-ip:8081
- PostgreSQL (pgAdmin): http://nuc12-ip:5050
- Elasticsearch: http://nuc12-ip:9200

应用服务:
- Streamlit排盘: http://nuc12-ip:8501
- Jupyter Lab: http://nuc12-ip:8888
- API Gateway: http://nuc12-ip:8000

监控服务:
- Grafana: http://nuc12-ip:3000
- Prometheus: http://nuc12-ip:9090
- Portainer: http://nuc12-ip:9000
```

## 🔧 配置文件说明

### 环境变量
所有敏感配置都通过环境变量管理，请查看 `.env.example` 并创建你的 `.env` 文件。

### 数据持久化
所有数据都挂载到宿主机，确保容器重启后数据不丢失：
- Neo4j数据: `./data/neo4j`
- MongoDB数据: `./data/mongodb`
- PostgreSQL数据: `./data/postgresql`

### 网络配置
所有服务都在同一个Docker网络中，可以通过服务名互相访问。

## 📈 性能优化建议

### 1. SSD优化
```bash
# 启用SSD TRIM
sudo systemctl enable fstrim.timer

# 调整文件系统参数
echo 'noatime,nodiratime' >> /etc/fstab
```

### 2. 内存优化
```bash
# 调整swappiness
echo 'vm.swappiness=10' >> /etc/sysctl.conf

# 优化内存分配
echo 'vm.overcommit_memory=1' >> /etc/sysctl.conf
```

### 3. 网络优化
```bash
# 增加网络缓冲区
echo 'net.core.rmem_max=134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max=134217728' >> /etc/sysctl.conf
```

## 🛠️ 维护脚本

提供了一系列维护脚本：

- `start-all-services.sh` - 启动所有服务
- `stop-all-services.sh` - 停止所有服务
- `backup-databases.sh` - 备份所有数据库
- `restore-databases.sh` - 恢复数据库
- `check-services.sh` - 检查服务状态
- `update-services.sh` - 更新服务镜像

## 📚 数据导入指南

### 1. 二十四史数据导入
```bash
# 导入脚本位置
./scripts/import-twenty-four-histories.sh

# 分阶段导入
./scripts/import-phase1-nanbeichao.sh  # 南北朝史书
./scripts/import-phase2-tang.sh        # 唐代史书
./scripts/import-phase3-song.sh        # 宋元明史书
```

### 2. 术数典籍导入
```bash
./scripts/import-shushu-books.sh
```

## 🔍 故障排除

### 常见问题
1. **内存不足**: 检查 `docker stats` 确认内存使用
2. **磁盘空间**: 使用 `df -h` 检查磁盘使用
3. **端口冲突**: 检查 `netstat -tulpn` 确认端口占用
4. **服务启动失败**: 查看 `docker-compose logs [service-name]`

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f neo4j
docker-compose logs -f mongodb
```

## 📞 支持联系

如果在配置过程中遇到问题，请查看：
1. 各服务的详细配置文档
2. Docker官方文档
3. 各数据库的官方文档

---

**准备好开始你的历史数据之旅了吗？** 🚀
