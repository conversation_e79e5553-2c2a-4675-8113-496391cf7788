# NUC12 六行星知识图谱环境配置
# 复制此文件为 .env 并修改相应配置

# ==================== 基础配置 ====================
# 服务器信息
NUC12_IP=*************
DOMAIN_NAME=sixstars.local
TIMEZONE=Asia/Shanghai

# ==================== 数据库配置 ====================

# Neo4j 图数据库
NEO4J_AUTH=neo4j/sixstars123
NEO4J_HEAP_INITIAL=4G
NEO4J_HEAP_MAX=8G
NEO4J_PAGECACHE=2G

# MongoDB 文档数据库
MONGODB_ROOT_USERNAME=admin
MONGODB_ROOT_PASSWORD=sixstars123
MONGODB_DATABASE=twentyfour_histories

# PostgreSQL 关系数据库
POSTGRES_DB=sixstars_analytics
POSTGRES_USER=sixstars
POSTGRES_PASSWORD=sixstars123

# Redis 缓存
REDIS_PASSWORD=sixstars123
REDIS_MAXMEMORY=1gb

# ==================== 搜索引擎配置 ====================

# Elasticsearch
ES_JAVA_OPTS=-Xms2g -Xmx4g
ELASTIC_PASSWORD=sixstars123

# ==================== 应用服务配置 ====================

# Streamlit
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=0.0.0.0

# Jupyter
JUPYTER_TOKEN=sixstars123
JUPYTER_ENABLE_LAB=yes

# ==================== 监控配置 ====================

# Grafana
GF_SECURITY_ADMIN_USER=admin
GF_SECURITY_ADMIN_PASSWORD=sixstars123

# Prometheus
PROMETHEUS_RETENTION_TIME=200h

# ==================== AI模型配置 ====================

# Gemini API
GEMINI_API_KEY=AIzaSyCalfmTapVVTYWVAPm923sz7UtitZbmsaM

# OpenAI API (可选)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API (可选)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ==================== 数据路径配置 ====================

# 数据目录
DATA_ROOT=/home/<USER>/sixstars-data
BACKUP_ROOT=/mnt/nas/sixstars-backup

# 原始数据路径
SHUSHU_BOOKS_PATH=/mnt/nas/shushubook
HISTORIES_PATH=/mnt/nas/twenty-four-histories
CLEANED_DATA_PATH=/mnt/nas/cleaned-data

# ==================== 网络配置 ====================

# Docker网络
DOCKER_NETWORK_SUBNET=**********/16

# 端口配置
NEO4J_HTTP_PORT=7474
NEO4J_BOLT_PORT=7687
MONGODB_PORT=27017
POSTGRES_PORT=5432
ELASTICSEARCH_PORT=9200
REDIS_PORT=6379
STREAMLIT_PORT=8501
JUPYTER_PORT=8888
GRAFANA_PORT=3000
PROMETHEUS_PORT=9090
PORTAINER_PORT=9000

# ==================== SSL证书配置 ====================

# SSL证书路径 (如果使用HTTPS)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# ==================== 备份配置 ====================

# 备份设置
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=gzip

# ==================== 日志配置 ====================

# 日志级别
LOG_LEVEL=INFO
LOG_MAX_SIZE=100m
LOG_MAX_FILES=10

# ==================== 性能调优配置 ====================

# 系统优化
SWAPPINESS=10
OVERCOMMIT_MEMORY=1

# 网络优化
NET_CORE_RMEM_MAX=134217728
NET_CORE_WMEM_MAX=134217728

# ==================== 开发环境配置 ====================

# 开发模式
DEBUG_MODE=false
ENABLE_PROFILING=false
ENABLE_QUERY_LOGGING=true

# 热重载
ENABLE_HOT_RELOAD=true
WATCH_FILES=true

# ==================== 安全配置 ====================

# 访问控制
ENABLE_AUTH=true
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5

# 防火墙设置
ALLOWED_IPS=***********/24,10.0.0.0/8
BLOCKED_IPS=

# ==================== 扩展服务配置 ====================

# 邮件通知 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# 钉钉通知 (可选)
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your_token

# 微信通知 (可选)
WECHAT_CORP_ID=your_corp_id
WECHAT_CORP_SECRET=your_corp_secret
WECHAT_AGENT_ID=your_agent_id
