# NUC12 安装指南 - 今晚操作手册

> **时间**: 今晚收到NUC12后  
> **目标**: 一次性部署完整的六行星知识图谱系统  
> **预计时间**: 2-3小时  

## 🎯 今晚的任务清单

### ✅ 第一步：硬件开箱 (15分钟)
```bash
# 1. 开箱验收
- [ ] 检查外观无损坏
- [ ] 确认配件齐全
- [ ] 记录序列号

# 2. 硬件连接
- [ ] 连接电源
- [ ] 连接网络 (有线推荐)
- [ ] 连接显示器和键盘 (初次设置)
- [ ] 开机测试
```

### ✅ 第二步：系统安装 (30分钟)
```bash
# 推荐系统: Ubuntu 22.04 LTS Server
# 下载地址: https://ubuntu.com/download/server

# 安装要点:
- [ ] 选择最小化安装
- [ ] 启用SSH服务
- [ ] 创建用户: sixstars
- [ ] 设置静态IP地址
- [ ] 安装完成后记录IP地址
```

### ✅ 第三步：基础环境配置 (20分钟)
```bash
# SSH连接到NUC12
ssh sixstars@<NUC12-IP>

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git vim htop tree jq

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以应用Docker组权限
exit
ssh sixstars@<NUC12-IP>

# 验证安装
docker --version
docker-compose --version
```

### ✅ 第四步：部署配置文件 (15分钟)
```bash
# 在你的Mac上，将配置文件传输到NUC12
cd /Users/<USER>/5-planets
scp -r nuc12-setup/ sixstars@<NUC12-IP>:/home/<USER>/

# 在NUC12上
cd /home/<USER>/nuc12-setup

# 复制环境配置
cp .env.example .env

# 编辑配置文件 (重要!)
vim .env
# 修改以下关键配置:
# - NUC12_IP=<实际IP地址>
# - 确认所有密码设置
# - 检查路径配置
```

### ✅ 第五步：系统优化 (10分钟)
```bash
# 内核参数优化
sudo tee -a /etc/sysctl.conf << EOF
# 六行星知识图谱优化参数
vm.swappiness=10
vm.dirty_ratio=15
vm.dirty_background_ratio=5
vm.overcommit_memory=1
net.core.rmem_max=134217728
net.core.wmem_max=134217728
fs.file-max=2097152
EOF

# 应用配置
sudo sysctl -p

# 启用SSD优化
sudo systemctl enable fstrim.timer

# Docker优化
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json << EOF
{
  "storage-driver": "overlay2",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 64000,
      "Soft": 64000
    }
  }
}
EOF

# 重启Docker
sudo systemctl restart docker
```

### ✅ 第六步：启动所有服务 (30分钟)
```bash
# 确保在正确目录
cd /home/<USER>/nuc12-setup

# 一键启动所有服务
./start-all-services.sh

# 等待服务启动 (大约5-10分钟)
# 可以观察启动日志
docker-compose logs -f

# 检查服务状态
./check-services.sh
```

### ✅ 第七步：验证部署 (20分钟)
```bash
# 1. 检查所有容器状态
docker-compose ps

# 2. 访问各个服务 (在浏览器中)
# 替换 <NUC12-IP> 为实际IP地址

# 核心服务:
http://<NUC12-IP>:7474    # Neo4j Browser
http://<NUC12-IP>:8081    # MongoDB Express  
http://<NUC12-IP>:5050    # pgAdmin
http://<NUC12-IP>:9200    # Elasticsearch

# 应用服务:
http://<NUC12-IP>:8501    # Streamlit排盘
http://<NUC12-IP>:8888    # Jupyter Lab

# 监控服务:
http://<NUC12-IP>:3000    # Grafana
http://<NUC12-IP>:9090    # Prometheus
http://<NUC12-IP>:9000    # Portainer

# 3. 测试数据库连接
# Neo4j测试
curl -u neo4j:sixstars123 http://<NUC12-IP>:7474/db/data/

# MongoDB测试
docker exec sixstars-mongodb mongosh --eval "db.adminCommand('ping')"

# PostgreSQL测试
docker exec sixstars-postgresql pg_isready -U sixstars
```

## 🚨 常见问题解决

### 问题1: Docker权限错误
```bash
# 症状: permission denied while trying to connect to Docker daemon
# 解决:
sudo usermod -aG docker $USER
# 然后重新登录SSH
```

### 问题2: 端口被占用
```bash
# 症状: port is already allocated
# 检查端口占用:
sudo netstat -tulpn | grep <端口号>
# 停止占用进程或修改docker-compose.yml中的端口
```

### 问题3: 内存不足
```bash
# 症状: container killed (OOMKilled)
# 检查内存使用:
free -h
docker stats
# 调整docker-compose.yml中的内存限制
```

### 问题4: 服务启动失败
```bash
# 查看具体错误:
docker-compose logs <服务名>
# 常见解决方法:
docker-compose down
docker-compose up -d <服务名>
```

## 📱 手机访问配置

### 设置静态IP (推荐)
```bash
# 编辑网络配置
sudo vim /etc/netplan/00-installer-config.yaml

# 示例配置:
network:
  ethernets:
    enp1s0:  # 网卡名称可能不同
      dhcp4: false
      addresses:
        - *************/24  # 设置固定IP
      gateway4: ***********
      nameservers:
        addresses: [*******, *******]
  version: 2

# 应用配置
sudo netplan apply
```

### 防火墙配置
```bash
# 启用防火墙
sudo ufw enable

# 开放必要端口
sudo ufw allow ssh
sudo ufw allow 7474   # Neo4j
sudo ufw allow 8081   # MongoDB Express
sudo ufw allow 8501   # Streamlit
sudo ufw allow 3000   # Grafana
sudo ufw allow 9000   # Portainer

# 查看状态
sudo ufw status
```

## 🎉 完成检查清单

### 部署完成标志
- [ ] 所有Docker容器运行正常
- [ ] 可以通过浏览器访问各个服务
- [ ] 数据库连接测试通过
- [ ] Streamlit应用可以正常打开
- [ ] 监控面板显示正常数据

### 性能验证
- [ ] 系统内存使用 < 50%
- [ ] CPU使用率 < 30%
- [ ] 磁盘使用率 < 20%
- [ ] 所有服务响应时间 < 3秒

### 备份准备
- [ ] 配置文件已备份
- [ ] 数据目录权限正确
- [ ] 自动备份脚本可执行

## 📞 紧急联系

如果遇到无法解决的问题:

1. **保存错误日志**:
```bash
# 收集所有日志
./check-services.sh --report
docker-compose logs > error-logs.txt
```

2. **重启大法**:
```bash
# 重启所有服务
docker-compose down
docker-compose up -d
```

3. **系统重启**:
```bash
sudo reboot
# 重启后重新运行启动脚本
```

---

**今晚就要让你的NUC12变成史上最强的古代知识图谱服务器！** 🚀

**预祝部署成功！** 🎉
