#!/usr/bin/env python3
"""
测试占卜模块API调用
"""

import sys
from pathlib import Path

# 模拟app.py中的路径设置
current_dir = str(Path(__file__).parent.resolve())
local_modules = ["core"]
for module in local_modules:
    module_path = str(Path(current_dir) / module)
    if module_path not in sys.path:
        sys.path.insert(0, module_path)

print("🧪 API调用测试")
print("=" * 50)

# 测试时间参数
year, month, day, hour = 2025, 1, 20, 10

# 测试kinqimen API
print("\n🌟 测试奇门遁甲 API:")
try:
    import kinqimen
    print(f"✅ kinqimen 模块导入成功")
    print(f"   模块内容: {dir(kinqimen)}")
    
    # 尝试不同的API调用方式
    try:
        from kinqimen.kinqimen import Qimen
        result = Qimen(year, month, day, hour).pan()
        print(f"✅ kinqimen.kinqimen.Qimen API 调用成功")
        print(f"   结果类型: {type(result)}")
    except Exception as e:
        print(f"❌ kinqimen.kinqimen.Qimen API 调用失败: {e}")
        
    # 尝试其他可能的API
    try:
        result = kinqimen.qimen(year, month, day, hour, 30)
        print(f"✅ kinqimen.qimen API 调用成功")
        print(f"   结果类型: {type(result)}")
    except Exception as e:
        print(f"❌ kinqimen.qimen API 调用失败: {e}")
        
except ImportError as e:
    print(f"❌ kinqimen 导入失败: {e}")

# 测试kintaiyi API
print("\n🏛️ 测试太乙神数 API:")
try:
    import kintaiyi
    print(f"✅ kintaiyi 模块导入成功")
    print(f"   模块内容: {dir(kintaiyi)}")
    
    # 尝试不同的API调用方式
    try:
        from kintaiyi.kintaiyi import Taiyi
        result = Taiyi(year, month, day, hour).pan()
        print(f"✅ kintaiyi.kintaiyi.Taiyi API 调用成功")
        print(f"   结果类型: {type(result)}")
    except Exception as e:
        print(f"❌ kintaiyi.kintaiyi.Taiyi API 调用失败: {e}")
        
    # 尝试其他可能的API
    try:
        result = kintaiyi.taiyi(year, month, day, hour, 30, "时计太乙", "男")
        print(f"✅ kintaiyi.taiyi API 调用成功")
        print(f"   结果类型: {type(result)}")
    except Exception as e:
        print(f"❌ kintaiyi.taiyi API 调用失败: {e}")
        
except ImportError as e:
    print(f"❌ kintaiyi 导入失败: {e}")

# 测试kinliuren API
print("\n🔮 测试大六壬 API:")
try:
    import kinliuren
    print(f"✅ kinliuren 模块导入成功")
    print(f"   模块内容: {dir(kinliuren)}")
    
    # 尝试不同的API调用方式
    try:
        from kinliuren.kinliuren import Liuren
        result = Liuren("立春", 1, "甲子", "甲子").result(0)
        print(f"✅ kinliuren.kinliuren.Liuren API 调用成功")
        print(f"   结果类型: {type(result)}")
    except Exception as e:
        print(f"❌ kinliuren.kinliuren.Liuren API 调用失败: {e}")
        
except ImportError as e:
    print(f"❌ kinliuren 导入失败: {e}")

print("\n📊 API测试完成")
