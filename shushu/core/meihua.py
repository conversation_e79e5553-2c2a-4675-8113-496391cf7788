# -*- coding: utf-8 -*-
"""
梅花心易核心模块
实现梅花易数的起卦、解卦功能
"""

import datetime
import random
from typing import Dict, List, Tuple, Optional

class MeihuaYiShu:
    """梅花心易类"""
    
    # 八卦基础数据
    BAGUA = {
        1: {"名": "乾", "象": "☰", "五行": "金", "方位": "西北", "属性": "天"},
        2: {"名": "兑", "象": "☱", "五行": "金", "方位": "西", "属性": "泽"},
        3: {"名": "离", "象": "☲", "五行": "火", "方位": "南", "属性": "火"},
        4: {"名": "震", "象": "☳", "五行": "木", "方位": "东", "属性": "雷"},
        5: {"名": "巽", "象": "☴", "五行": "木", "方位": "东南", "属性": "风"},
        6: {"名": "坎", "象": "☵", "五行": "水", "方位": "北", "属性": "水"},
        7: {"名": "艮", "象": "☶", "五行": "土", "方位": "东北", "属性": "山"},
        8: {"名": "坤", "象": "☷", "五行": "土", "方位": "西南", "属性": "地"}
    }
    
    # 六十四卦名称
    LIUSHISIGUA = {
        (1,1): "乾为天", (1,2): "天泽履", (1,3): "天火同人", (1,4): "天雷无妄",
        (1,5): "天风姤", (1,6): "天水讼", (1,7): "天山遁", (1,8): "天地否",
        (2,1): "泽天夬", (2,2): "泽泽兑", (2,3): "泽火革", (2,4): "泽雷随",
        (2,5): "泽风大过", (2,6): "泽水困", (2,7): "泽山咸", (2,8): "泽地萃",
        (3,1): "火天大有", (3,2): "火泽睽", (3,3): "火火离", (3,4): "火雷噬嗑",
        (3,5): "火风鼎", (3,6): "火水未济", (3,7): "火山旅", (3,8): "火地晋",
        (4,1): "雷天大壮", (4,2): "雷泽归妹", (4,3): "雷火丰", (4,4): "雷雷震",
        (4,5): "雷风恒", (4,6): "雷水解", (4,7): "雷山小过", (4,8): "雷地豫",
        (5,1): "风天小畜", (5,2): "风泽中孚", (5,3): "风火家人", (5,4): "风雷益",
        (5,5): "风风巽", (5,6): "风水涣", (5,7): "风山渐", (5,8): "风地观",
        (6,1): "水天需", (6,2): "水泽节", (6,3): "水火既济", (6,4): "水雷屯",
        (6,5): "水风井", (6,6): "水水坎", (6,7): "水山蹇", (6,8): "水地比",
        (7,1): "山天大畜", (7,2): "山泽损", (7,3): "山火贲", (7,4): "山雷颐",
        (7,5): "山风蛊", (7,6): "山水蒙", (7,7): "山山艮", (7,8): "山地剥",
        (8,1): "地天泰", (8,2): "地泽临", (8,3): "地火明夷", (8,4): "地雷复",
        (8,5): "地风升", (8,6): "地水师", (8,7): "地山谦", (8,8): "地地坤"
    }
    
    # 五行生克关系
    WUXING_RELATION = {
        "生": {
            "金": "水", "水": "木", "木": "火", "火": "土", "土": "金"
        },
        "克": {
            "金": "木", "木": "土", "土": "水", "水": "火", "火": "金"
        }
    }
    
    def __init__(self, year: int, month: int, day: int, hour: int, minute: int):
        """初始化梅花心易"""
        self.year = year
        self.month = month
        self.day = day
        self.hour = hour
        self.minute = minute
        self.datetime = datetime.datetime(year, month, day, hour, minute)
    
    def time_qigua(self) -> Dict:
        """时间起卦法"""
        # 计算上卦：年月日之和除以8取余
        upper_num = (self.year + self.month + self.day) % 8
        if upper_num == 0:
            upper_num = 8
        
        # 计算下卦：年月日时之和除以8取余
        lower_num = (self.year + self.month + self.day + self.hour) % 8
        if lower_num == 0:
            lower_num = 8
        
        # 计算动爻：年月日时之和除以6取余
        dong_yao = (self.year + self.month + self.day + self.hour) % 6
        if dong_yao == 0:
            dong_yao = 6
        
        return self._generate_gua_result(upper_num, lower_num, dong_yao, "时间起卦")
    
    def number_qigua(self, num1: int, num2: int) -> Dict:
        """数字起卦法"""
        upper_num = num1 % 8
        if upper_num == 0:
            upper_num = 8
        
        lower_num = num2 % 8
        if lower_num == 0:
            lower_num = 8
        
        dong_yao = (num1 + num2) % 6
        if dong_yao == 0:
            dong_yao = 6
        
        return self._generate_gua_result(upper_num, lower_num, dong_yao, "数字起卦")
    
    def random_qigua(self) -> Dict:
        """随机起卦法"""
        upper_num = random.randint(1, 8)
        lower_num = random.randint(1, 8)
        dong_yao = random.randint(1, 6)
        
        return self._generate_gua_result(upper_num, lower_num, dong_yao, "随机起卦")
    
    def _generate_gua_result(self, upper_num: int, lower_num: int, dong_yao: int, method: str) -> Dict:
        """生成卦象结果"""
        # 获取本卦信息
        upper_gua = self.BAGUA[upper_num]
        lower_gua = self.BAGUA[lower_num]
        ben_gua_name = self.LIUSHISIGUA.get((upper_num, lower_num), "未知卦")
        
        # 计算变卦
        bian_upper_num, bian_lower_num = self._calculate_bian_gua(upper_num, lower_num, dong_yao)
        bian_upper_gua = self.BAGUA[bian_upper_num]
        bian_lower_gua = self.BAGUA[bian_lower_num]
        bian_gua_name = self.LIUSHISIGUA.get((bian_upper_num, bian_lower_num), "未知卦")
        
        # 分析体用关系
        tiyu_analysis = self._analyze_tiyu(upper_num, lower_num, dong_yao)
        
        # 五行分析
        wuxing_analysis = self._analyze_wuxing(upper_gua["五行"], lower_gua["五行"])
        
        return {
            "起卦方法": method,
            "起卦时间": self.datetime.strftime("%Y年%m月%d日%H时%M分"),
            "本卦": {
                "卦名": ben_gua_name,
                "上卦": f"{upper_gua['名']}({upper_gua['象']})",
                "下卦": f"{lower_gua['名']}({lower_gua['象']})",
                "上卦属性": f"{upper_gua['属性']}({upper_gua['五行']})",
                "下卦属性": f"{lower_gua['属性']}({lower_gua['五行']})"
            },
            "变卦": {
                "卦名": bian_gua_name,
                "上卦": f"{bian_upper_gua['名']}({bian_upper_gua['象']})",
                "下卦": f"{bian_lower_gua['名']}({bian_lower_gua['象']})",
                "动爻": f"第{dong_yao}爻"
            },
            "体用分析": tiyu_analysis,
            "五行分析": wuxing_analysis,
            "卦象详解": self._get_gua_explanation(ben_gua_name, bian_gua_name)
        }
    
    def _calculate_bian_gua(self, upper_num: int, lower_num: int, dong_yao: int) -> Tuple[int, int]:
        """计算变卦"""
        if dong_yao <= 3:  # 下卦动
            # 下卦变化
            bian_lower_num = lower_num
            if bian_lower_num % 2 == 1:  # 奇数变偶数
                bian_lower_num = bian_lower_num + 1 if bian_lower_num < 8 else 1
            else:  # 偶数变奇数
                bian_lower_num = bian_lower_num - 1 if bian_lower_num > 1 else 8
            return upper_num, bian_lower_num
        else:  # 上卦动
            # 上卦变化
            bian_upper_num = upper_num
            if bian_upper_num % 2 == 1:  # 奇数变偶数
                bian_upper_num = bian_upper_num + 1 if bian_upper_num < 8 else 1
            else:  # 偶数变奇数
                bian_upper_num = bian_upper_num - 1 if bian_upper_num > 1 else 8
            return bian_upper_num, lower_num
    
    def _analyze_tiyu(self, upper_num: int, lower_num: int, dong_yao: int) -> Dict:
        """分析体用关系"""
        if dong_yao <= 3:
            ti_gua = self.BAGUA[upper_num]  # 上卦为体
            yong_gua = self.BAGUA[lower_num]  # 下卦为用
        else:
            ti_gua = self.BAGUA[lower_num]  # 下卦为体
            yong_gua = self.BAGUA[upper_num]  # 上卦为用
        
        # 分析体用生克关系
        ti_wuxing = ti_gua["五行"]
        yong_wuxing = yong_gua["五行"]
        
        if self.WUXING_RELATION["生"][yong_wuxing] == ti_wuxing:
            relation = "用生体，吉"
        elif self.WUXING_RELATION["克"][yong_wuxing] == ti_wuxing:
            relation = "用克体，凶"
        elif self.WUXING_RELATION["生"][ti_wuxing] == yong_wuxing:
            relation = "体生用，泄气"
        elif self.WUXING_RELATION["克"][ti_wuxing] == yong_wuxing:
            relation = "体克用，吉"
        else:
            relation = "体用比和，平"
        
        return {
            "体卦": f"{ti_gua['名']}({ti_wuxing})",
            "用卦": f"{yong_gua['名']}({yong_wuxing})",
            "体用关系": relation
        }
    
    def _analyze_wuxing(self, upper_wuxing: str, lower_wuxing: str) -> Dict:
        """五行分析"""
        if self.WUXING_RELATION["生"][lower_wuxing] == upper_wuxing:
            relation = "下生上"
        elif self.WUXING_RELATION["克"][lower_wuxing] == upper_wuxing:
            relation = "下克上"
        elif self.WUXING_RELATION["生"][upper_wuxing] == lower_wuxing:
            relation = "上生下"
        elif self.WUXING_RELATION["克"][upper_wuxing] == lower_wuxing:
            relation = "上克下"
        else:
            relation = "比和"
        
        return {
            "上卦五行": upper_wuxing,
            "下卦五行": lower_wuxing,
            "五行关系": relation
        }
    
    def _get_gua_explanation(self, ben_gua: str, bian_gua: str) -> str:
        """获取卦象解释"""
        return f"""
【本卦】{ben_gua}
本卦反映当前的状态和情况。

【变卦】{bian_gua}  
变卦显示事物的发展趋势和结果。

【总体分析】
根据梅花心易的原理，需要综合考虑体用关系、五行生克、卦象含义等因素来判断吉凶。
体卦代表自己或所问之事的主体，用卦代表外界环境或他人。
体用相生则吉，体用相克则需谨慎。
        """.strip()


def meihua_yishu(year: int, month: int, day: int, hour: int, minute: int, 
                method: str = "时间", num1: int = None, num2: int = None) -> Dict:
    """梅花心易主函数"""
    mh = MeihuaYiShu(year, month, day, hour, minute)
    
    if method == "时间":
        return mh.time_qigua()
    elif method == "数字" and num1 is not None and num2 is not None:
        return mh.number_qigua(num1, num2)
    elif method == "随机":
        return mh.random_qigua()
    else:
        return mh.time_qigua()  # 默认使用时间起卦
