"""
六壬核心排盘模块
提取核心计算功能，移除解盘文字
"""
from kinliuren import kinliuren
from jieqi import *
from sxtwl import fromSolar
import config


class LiurenCore:
    """六壬核心排盘计算"""
    
    def __init__(self, year, month, day, hour, minute):
        self.year = year
        self.month = month
        self.day = day
        self.hour = hour
        self.minute = minute

    def lunar_date(self):
        """获取农历日期"""
        try:
            day = fromSolar(self.year, self.month, self.day)
            return {
                "月": str(day.getLunarMonth()) + "月",
                "日": str(day.getLunarDay())
            }
        except:
            return {"月": "未知", "日": "未知"}

    def calculate_month(self):
        """计算月课"""
        try:
            lunar = self.lunar_date()
            cm = dict(zip(list(range(1, 13)), 
                         list("正二三四五六七八九十") + ["十一", "十二"])).get(
                int(lunar.get("月").replace("月", "")))
            qgz = gangzhi(self.year, self.month, self.day, self.hour, self.minute)
            jq_val = jq(self.year, self.month, self.day, self.hour, self.minute)
            
            result = kinliuren.<PERSON>ren(jq_val, cm, qgz[1], qgz[2]).result_d(0)
            return result
        except Exception as e:
            return {"error": str(e)}

    def calculate_day(self):
        """计算日课"""
        try:
            lunar = self.lunar_date()
            cm = dict(zip(list(range(1, 13)), 
                         list("正二三四五六七八九十") + ["十一", "十二"])).get(
                int(lunar.get("月").replace("月", "")))
            qgz = gangzhi(self.year, self.month, self.day, self.hour, self.minute)
            jq_val = jq(self.year, self.month, self.day, self.hour, self.minute)
            
            result = kinliuren.Liuren(jq_val, cm, qgz[2], qgz[3]).result(0)
            return result
        except Exception as e:
            return {"error": str(e)}

    def calculate_hour(self):
        """计算时课"""
        try:
            lunar = self.lunar_date()
            cm = dict(zip(list(range(1, 13)), 
                         list("正二三四五六七八九十") + ["十一", "十二"])).get(
                int(lunar.get("月").replace("月", "")))
            qgz = gangzhi(self.year, self.month, self.day, self.hour, self.minute)
            jq_val = jq(self.year, self.month, self.day, self.hour, self.minute)
            
            result = kinliuren.Liuren(jq_val, cm, qgz[3], qgz[4]).result_m(0)
            return result
        except Exception as e:
            return {"error": str(e)}

    def calculate_all(self):
        """计算完整六壬排盘"""
        try:
            qgz = gangzhi(self.year, self.month, self.day, self.hour, self.minute)
            jq_val = jq(self.year, self.month, self.day, self.hour, self.minute)
            lunar = self.lunar_date()
            
            month_result = self.calculate_month()
            day_result = self.calculate_day()
            hour_result = self.calculate_hour()
            
            result = {
                "基本信息": {
                    "日期": f"{self.year}年{self.month}月{self.day}日{self.hour}时{self.minute}分",
                    "干支": f"{qgz[0]}年 {qgz[1]}月 {qgz[2]}日 {qgz[3]}时 {qgz[4]}分",
                    "节气": jq_val,
                    "农历": lunar,
                    "格局": month_result.get("格局", ["未知"])[0] if month_result.get("格局") else "未知"
                },
                "月课": month_result,
                "日课": day_result,
                "时课": hour_result,
                "日马": {
                    "月": month_result.get("日马", "未知"),
                    "日": day_result.get("日马", "未知"),
                    "时": hour_result.get("日马", "未知")
                },
                "三传": {
                    "月课": month_result.get("三传", {}),
                    "日课": day_result.get("三传", {}),
                    "时课": hour_result.get("三传", {})
                },
                "四课": {
                    "月课": month_result.get("四课", {}),
                    "日课": day_result.get("四课", {}),
                    "时课": hour_result.get("四课", {})
                },
                "地转天盘": {
                    "月课": month_result.get("地转天盘", {}),
                    "日课": day_result.get("地转天盘", {}),
                    "时课": hour_result.get("地转天盘", {})
                },
                "地转天将": {
                    "月课": month_result.get("地转天将", {}),
                    "日课": day_result.get("地转天将", {}),
                    "时课": hour_result.get("地转天将", {})
                }
            }
            
            return result
            
        except Exception as e:
            return {"error": str(e)}


def calculate_liuren(year, month, day, hour, minute):
    """外部调用接口"""
    liuren = LiurenCore(year, month, day, hour, minute)
    return liuren.calculate_all()