# -*- coding: utf-8 -*-
"""
太乙神数核心排盘功能模块
提取自kintaiyi.py，只保留排盘计算，移除解盘文字
"""
import sys
import os

# 添加kintaiyi目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'kintaiyi'))
from kintaiyi import Taiyi

def taiyi(year, month, day, hour, minute, method="年计", formula=0):
    """太乙神数排盘接口函数"""
    # 方法映射
    method_map = {
        "年计": 0,
        "月计": 1, 
        "日计": 2,
        "时计": 3,
        "分计": 4
    }
    
    if method not in method_map:
        raise ValueError("method must be one of: 年计, 月计, 日计, 时计, 分计")
    
    ji_style = method_map[method]
    ty = Taiyi(year, month, day, hour, minute)
    
    return ty.pan(ji_style, formula)

def taiyi_life(year, month, day, hour, minute, sex="男"):
    """太乙命法排盘接口函数"""
    if sex not in ["男", "女"]:
        raise ValueError("sex must be '男' or '女'")
    
    ty = Taiyi(year, month, day, hour, minute)
    return ty.taiyi_life(sex)