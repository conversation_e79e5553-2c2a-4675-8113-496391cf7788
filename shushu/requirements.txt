# 太公三式合并应用依赖包
# 包含三个占卜系统的所有依赖

# 核心框架
streamlit>=1.47.0
pendulum>=3.0.0
pytz==2022.4

# 中国历法和天文计算
sxtwl>=2.0.6
ephem>=4.2

# 数据处理
cn2an==0.5.17
bidict==0.23.1
opencc-python-reimplemented==0.1.7

# 图表和可视化
altair==4.0
drawsvg==2.3.0

# Streamlit 扩展组件
streamlit-aggrid==1.0.5
streamlit-screen-stats==0.0.82
streamlit-local-storage==0.0.25
streamlit-browser-session-storage==0.0.12
streamlit-modal==0.1.0
streamlit-timeline==0.0.2
streamlit-date-picker==0.0.3

# 核心占卜系统包
kinliuren==0.1.2.6
# kinqimen==0.0.6.4  # 需要检查是否可用
# kintaiyi  # 需要检查是否可用

# AI 集成
cerebras-cloud-sdk

# FastAPI 相关
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.11.0

# 其他工具
eacal==0.0.3

# 注意事项：
# 1. 某些占卜系统的包可能需要从源码安装
# 2. 如果包不可用，应用会显示相应错误信息
# 3. 建议在虚拟环境中安装

# Mandelbrot 页面依赖
matplotlib>=3.7.0
qrcode>=7.4.2
Pillow>=10.0.0