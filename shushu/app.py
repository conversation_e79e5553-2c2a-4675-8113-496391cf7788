import streamlit as st
import datetime
import sys
from pathlib import Path

# 安全设置路径
try:
    current_dir = str(Path(__file__).parent.resolve())
except NameError:
    current_dir = str(Path.cwd())

# 添加所有必要的路径
sys.path.insert(0, current_dir)
sys.path.insert(0, str(Path(current_dir) / "kinliuren"))
sys.path.insert(0, str(Path(current_dir) / "kinqimen"))
sys.path.insert(0, str(Path(current_dir) / "kintaiyi"))
sys.path.insert(0, str(Path(current_dir) / "core"))

# 页面配置
st.set_page_config(
    page_title="太公心易兜率宫",
    page_icon="🏛️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 检查模块可用性 - 使用真正的PyPI包
try:
    import kinliuren
    KINLIUREN_AVAILABLE = True
    print("✅ kinliuren PyPI包加载成功")
except ImportError as e:
    KINLIUREN_AVAILABLE = False
    print(f"❌ kinliuren PyPI包加载失败: {e}")

try:
    import kinqimen
    KINQIMEN_AVAILABLE = True
    print("✅ kinqimen PyPI包加载成功")
except ImportError as e:
    KINQIMEN_AVAILABLE = False
    print(f"❌ kinqimen PyPI包加载失败: {e}")

try:
    import kintaiyi
    KINTAIYI_AVAILABLE = True
    print("✅ kintaiyi PyPI包加载成功")
except ImportError as e:
    KINTAIYI_AVAILABLE = False
    print(f"❌ kintaiyi PyPI包加载失败: {e}")

try:
    import ichingshifa
    ICHINGSHIFA_AVAILABLE = True
    print("✅ ichingshifa PyPI包加载成功")
except ImportError as e:
    ICHINGSHIFA_AVAILABLE = False
    print(f"❌ ichingshifa PyPI包加载失败: {e}")

try:
    from core.meihua import meihua_yishu
    MEIHUA_AVAILABLE = True
    print("✅ meihua本地模块加载成功")
except ImportError as e:
    MEIHUA_AVAILABLE = False
    print(f"❌ meihua本地模块加载失败: {e}")

# 工具函数
def safe_gangzhi(y, m, d, h, min):
    """安全获取干支"""
    # 天干地支基础数据
    tian_gan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    di_zhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

    try:
        from sxtwl import fromSolar
        solar = fromSolar(y, m, d)

        # 获取干支对象
        year_gz = solar.getYearGZ()
        month_gz = solar.getMonthGZ()
        day_gz = solar.getDayGZ()
        hour_gz = solar.getHourGZ(h)

        # 安全转换为文字
        try:
            year_str = tian_gan[year_gz.tg] + di_zhi[year_gz.dz]
            month_str = tian_gan[month_gz.tg] + di_zhi[month_gz.dz]
            day_str = tian_gan[day_gz.tg] + di_zhi[day_gz.dz]
            hour_str = tian_gan[hour_gz.tg] + di_zhi[hour_gz.dz]

            return [year_str, month_str, day_str, hour_str, f"{min}分"]
        except (IndexError, AttributeError):
            # 如果sxtwl返回的索引有问题，使用备用计算
            raise Exception("sxtwl索引错误")

    except Exception as e:
        # 使用简化的干支计算
        # 基于公历日期的简化算法

        # 年干支计算（以1984年甲子为基准）
        year_idx = (y - 1984) % 60
        year_gz = tian_gan[year_idx % 10] + di_zhi[year_idx % 12]

        # 月干支计算（简化）
        month_idx = ((y - 1984) * 12 + m - 1) % 60
        month_gz = tian_gan[month_idx % 10] + di_zhi[month_idx % 12]

        # 日干支计算（简化）
        # 使用公历日期计算，以2000年1月1日为基准
        import datetime
        base_date = datetime.date(2000, 1, 1)  # 庚辰日
        current_date = datetime.date(y, m, d)
        days_diff = (current_date - base_date).days
        day_idx = (days_diff + 16) % 60  # 庚辰是第17个干支，索引16
        day_gz = tian_gan[day_idx % 10] + di_zhi[day_idx % 12]

        # 时干支计算
        # 子时=0, 丑时=1, ..., 亥时=11
        hour_zhi_idx = (h + 1) // 2 % 12
        # 时干根据日干推算：甲己日起甲子
        day_gan_idx = day_idx % 10
        hour_gan_base = [0, 2, 4, 6, 8, 0, 2, 4, 6, 8]  # 甲己起甲，乙庚起丙...
        hour_gan_idx = (hour_gan_base[day_gan_idx] + hour_zhi_idx) % 10
        hour_gz = tian_gan[hour_gan_idx] + di_zhi[hour_zhi_idx]

        return [year_gz, month_gz, day_gz, hour_gz, f"{min}分"]

def safe_jieqi(y, m, d, h, min):
    """安全获取节气"""
    try:
        # 简化的节气计算
        month_jieqi = {
            1: "小寒", 2: "立春", 3: "惊蛰", 4: "清明",
            5: "立夏", 6: "芒种", 7: "小暑", 8: "立秋",
            9: "白露", 10: "寒露", 11: "立冬", 12: "大雪"
        }
        return month_jieqi.get(m, "未知节气")
    except:
        return "未知节气"

def format_datetime(year, month, day, hour, minute):
    """格式化日期时间"""
    return f"{year}年{month}月{day}日{hour}时{minute}分"

# 主应用
def main():
    st.title("🏛️ 太公心易兜率宫")
    st.markdown("### 中华传统智慧的现代化实现")

    # 侧边栏
    st.sidebar.title("🔮 占卜系统")

    # 显示模块状态
    st.sidebar.subheader("📦 模块状态")
    st.sidebar.write("✅ 大六壬" if KINLIUREN_AVAILABLE else "❌ 大六壬")
    st.sidebar.write("✅ 奇门遁甲" if KINQIMEN_AVAILABLE else "❌ 奇门遁甲")
    st.sidebar.write("✅ 太乙神数" if KINTAIYI_AVAILABLE else "❌ 太乙神数")
    st.sidebar.write("✅ 梅花心易" if MEIHUA_AVAILABLE else "❌ 梅花心易")

    # 选择占卜系统
    system = st.sidebar.selectbox(
        "选择占卜系统",
        ["大六壬", "奇门遁甲", "太乙神数", "梅花心易"]
    )

    # 时间输入
    st.sidebar.subheader("📅 时间设置")

    col1, col2 = st.sidebar.columns(2)
    with col1:
        year = st.number_input("年", min_value=1900, max_value=2100, value=2025)
        month = st.number_input("月", min_value=1, max_value=12, value=1)
        day = st.number_input("日", min_value=1, max_value=31, value=20)

    with col2:
        hour = st.number_input("时", min_value=0, max_value=23, value=10)
        minute = st.number_input("分", min_value=0, max_value=59, value=30)

    # 主内容区域
    if system == "大六壬":
        render_liuren_section(year, month, day, hour, minute)
    elif system == "奇门遁甲":
        render_qimen_section(year, month, day, hour, minute)
    elif system == "太乙神数":
        render_taiyi_section(year, month, day, hour, minute)
    elif system == "梅花心易":
        render_meihua_section(year, month, day, hour, minute)

def render_liuren_section(year, month, day, hour, minute):
    """渲染大六壬部分"""
    st.header("📊 大六壬占卜")
    st.markdown("大六壬是中国古代占卜术之一，与奇门遁甲、太乙神数并称为'三式'。")

    if st.button("开始六壬排盘", key="liuren_calc"):
        with st.spinner("正在计算六壬排盘..."):
            if KINLIUREN_AVAILABLE:
                try:
                    # 获取干支和节气
                    gz = safe_gangzhi(year, month, day, hour, minute)
                    jq = safe_jieqi(year, month, day, hour, minute)

                    # 使用真正的kinliuren PyPI包
                    # API: from kinliuren import kinliuren; kinliuren.Liuren(...).result(0)
                    from kinliuren import kinliuren as lr
                    result = lr.Liuren(jq, str(month), gz[2], gz[3]).result(0)

                    # 格式化显示结果
                    result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 大六壬

【基本信息】
节气：{result.get('節氣', jq)}
日期：{result.get('日期', f"{gz[2]}日{gz[3]}时")}
格局：{', '.join(result.get('格局', ['未知']))}

【四课】
{result.get('四課', {}).get('一課', ['', ''])[0]} - {result.get('四課', {}).get('一課', ['', ''])[1]}
{result.get('四課', {}).get('二課', ['', ''])[0]} - {result.get('四課', {}).get('二課', ['', ''])[1]}
{result.get('四課', {}).get('三課', ['', ''])[0]} - {result.get('四課', {}).get('三課', ['', ''])[1]}
{result.get('四課', {}).get('四課', ['', ''])[0]} - {result.get('四課', {}).get('四課', ['', ''])[1]}

【三传】
初传：{result.get('三傳', {}).get('初傳', ['', '', '', ''])[0]} - {result.get('三傳', {}).get('初傳', ['', '', '', ''])[1]}
中传：{result.get('三傳', {}).get('中傳', ['', '', '', ''])[0]} - {result.get('三傳', {}).get('中傳', ['', '', '', ''])[1]}
末传：{result.get('三傳', {}).get('末傳', ['', '', '', ''])[0]} - {result.get('三傳', {}).get('末傳', ['', '', '', ''])[1]}

【神煞】
日马：{result.get('神煞', {}).get('日馬', '未知')}
天马：{result.get('神煞', {}).get('天馬', '未知')}
贵人：{result.get('神煞', {}).get('賢貴', '未知')}

注：使用kinliuren官方算法 v0.1.2.8
                    """

                    st.code(result_text, language="text")

                    # 显示详细信息
                    with st.expander("查看完整结果"):
                        st.json(result)

                except Exception as e:
                    st.error(f"六壬计算错误: {str(e)}")
                    st.info("正在使用备用算法...")
                    # 这里可以添加备用算法
            else:
                st.error("kinliuren模块未加载，请检查安装")

def render_qimen_section(year, month, day, hour, minute):
    """渲染奇门遁甲部分"""
    st.header("🌟 奇门遁甲占卜")
    st.markdown("奇门遁甲是中华民族的精典著作，也是奇门、六壬、太乙三大秘宝中的第一大秘术。")

    qimen_type = st.selectbox("选择奇门类型", ["时家奇门", "刻家奇门"])
    pai_type = st.selectbox("选择排盘方式", ["拆补", "置闰"])

    if st.button("开始奇门排盘", key="qimen_calc"):
        with st.spinner("正在计算奇门排盘..."):
            if KINQIMEN_AVAILABLE:
                try:
                    # 使用真正的kinqimen PyPI包
                    result = kinqimen.qimen(year, month, day, hour, minute)

                    # 格式化显示结果
                    result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 奇门遁甲

【占卜结果】
{result}

注：使用kinqimen官方算法
                    """

                    st.code(result_text, language="text")

                    # 显示详细信息
                    with st.expander("查看完整结果"):
                        st.json(result)

                except Exception as e:
                    st.error(f"奇门计算错误: {str(e)}")
                    st.info("正在使用备用算法...")
            else:
                st.error("kinqimen模块未加载，请检查安装")

def render_taiyi_section(year, month, day, hour, minute):
    """渲染太乙神数部分"""
    st.header("🏛️ 太乙神数占卜")
    st.markdown("太乙神数是中国古代占卜术中用于预测国运和个人命运的高级术数。")

    style = st.selectbox("起盘方式", ["时计太乙", "年计太乙", "月计太乙", "日计太乙", "分计太乙", "太乙命法"])
    sex = st.selectbox("性别", ["男", "女"])

    if st.button("开始太乙排盘", key="taiyi_calc"):
        with st.spinner("正在计算太乙排盘..."):
            if KINTAIYI_AVAILABLE:
                try:
                    # 使用真正的kintaiyi PyPI包
                    result = kintaiyi.taiyi(year, month, day, hour, minute, style, sex)

                    # 格式化显示结果
                    result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 太乙神数

【占卜结果】
{result}

注：使用kintaiyi官方算法
                    """

                    st.code(result_text, language="text")

                    # 显示详细信息
                    with st.expander("查看完整结果"):
                        st.json(result)

                except Exception as e:
                    st.error(f"太乙计算错误: {str(e)}")
                    st.info("正在使用备用算法...")
            else:
                st.error("kintaiyi模块未加载，请检查安装")

def render_meihua_section(year, month, day, hour, minute):
    """渲染梅花心易部分"""
    st.header("🌸 梅花心易占卜")
    st.markdown("梅花心易是一种古老的占卜方法，以其简便易学而著称。")

    method = st.selectbox("起卦方法", ["时间起卦", "数字起卦", "随机起卦"])

    num1, num2 = None, None
    if method == "数字起卦":
        col1, col2 = st.columns(2)
        with col1:
            num1 = st.number_input("第一个数字", min_value=1, max_value=999, value=1)
        with col2:
            num2 = st.number_input("第二个数字", min_value=1, max_value=999, value=1)

    if st.button("开始梅花排盘", key="meihua_calc"):
        with st.spinner("正在计算梅花心易..."):
            # 梅花心易基础排盘

            # 八卦基础数据
            bagua = {
                1: {"名": "乾", "象": "☰", "五行": "金", "方位": "西北", "属性": "天"},
                2: {"名": "兑", "象": "☱", "五行": "金", "方位": "西", "属性": "泽"},
                3: {"名": "离", "象": "☲", "五行": "火", "方位": "南", "属性": "火"},
                4: {"名": "震", "象": "☳", "五行": "木", "方位": "东", "属性": "雷"},
                5: {"名": "巽", "象": "☴", "五行": "木", "方位": "东南", "属性": "风"},
                6: {"名": "坎", "象": "☵", "五行": "水", "方位": "北", "属性": "水"},
                7: {"名": "艮", "象": "☶", "五行": "土", "方位": "东北", "属性": "山"},
                8: {"名": "坤", "象": "☷", "五行": "土", "方位": "西南", "属性": "地"}
            }

            # 六十四卦名称（部分）
            liushisigua = {
                (1,1): "乾为天", (1,2): "天泽履", (1,3): "天火同人", (1,4): "天雷无妄",
                (1,5): "天风姤", (1,6): "天水讼", (1,7): "天山遁", (1,8): "天地否",
                (2,1): "泽天夬", (2,2): "泽泽兑", (2,3): "泽火革", (2,4): "泽雷随",
                (3,1): "火天大有", (3,2): "火泽睽", (3,3): "火火离", (3,4): "火雷噬嗑",
                (4,1): "雷天大壮", (4,2): "雷泽归妹", (4,3): "雷火丰", (4,4): "雷雷震",
                (5,1): "风天小畜", (5,2): "风泽中孚", (5,3): "风火家人", (5,4): "风雷益",
                (6,1): "水天需", (6,2): "水泽节", (6,3): "水火既济", (6,4): "水雷屯",
                (7,1): "山天大畜", (7,2): "山泽损", (7,3): "山火贲", (7,4): "山雷颐",
                (8,1): "地天泰", (8,2): "地泽临", (8,3): "地火明夷", (8,4): "地雷复"
            }

            # 起卦计算
            if method == "时间起卦":
                # 时间起卦法
                shang_gua_num = (year + month + day) % 8
                if shang_gua_num == 0: shang_gua_num = 8

                xia_gua_num = (year + month + day + hour) % 8
                if xia_gua_num == 0: xia_gua_num = 8

                dong_yao = (year + month + day + hour + minute) % 6
                if dong_yao == 0: dong_yao = 6

            elif method == "数字起卦" and num1 and num2:
                # 数字起卦法
                shang_gua_num = num1 % 8
                if shang_gua_num == 0: shang_gua_num = 8

                xia_gua_num = num2 % 8
                if xia_gua_num == 0: xia_gua_num = 8

                dong_yao = (num1 + num2) % 6
                if dong_yao == 0: dong_yao = 6

            else:
                # 随机起卦
                import random
                shang_gua_num = random.randint(1, 8)
                xia_gua_num = random.randint(1, 8)
                dong_yao = random.randint(1, 6)

            # 获取卦象信息
            shang_gua = bagua[shang_gua_num]
            xia_gua = bagua[xia_gua_num]

            # 本卦
            ben_gua_name = liushisigua.get((shang_gua_num, xia_gua_num), f"{shang_gua['属性']}{xia_gua['属性']}")

            # 变卦计算（动爻变化）
            if dong_yao <= 3:
                # 下卦变
                bian_xia_num = xia_gua_num % 8 + 1 if xia_gua_num < 8 else 1
                bian_shang_num = shang_gua_num
            else:
                # 上卦变
                bian_shang_num = shang_gua_num % 8 + 1 if shang_gua_num < 8 else 1
                bian_xia_num = xia_gua_num

            bian_shang_gua = bagua[bian_shang_num]
            bian_xia_gua = bagua[bian_xia_num]
            bian_gua_name = liushisigua.get((bian_shang_num, bian_xia_num), f"{bian_shang_gua['属性']}{bian_xia_gua['属性']}")

            # 体用分析
            if dong_yao <= 3:
                ti_gua = shang_gua  # 上卦为体
                yong_gua = xia_gua  # 下卦为用
            else:
                ti_gua = xia_gua   # 下卦为体
                yong_gua = shang_gua  # 上卦为用

            # 五行生克关系
            wuxing_relation = {
                ("金", "木"): "克", ("木", "土"): "克", ("土", "水"): "克",
                ("水", "火"): "克", ("火", "金"): "克",
                ("金", "水"): "生", ("水", "木"): "生", ("木", "火"): "生",
                ("火", "土"): "生", ("土", "金"): "生"
            }

            ti_yong_relation = wuxing_relation.get((ti_gua["五行"], yong_gua["五行"]), "和")

            result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 梅花心易

【{method}】
起卦时间：{format_datetime(year, month, day, hour, minute)}

【本卦】{ben_gua_name}
上卦：{shang_gua['名']} {shang_gua['象']} - {shang_gua['属性']} ({shang_gua['五行']})
下卦：{xia_gua['名']} {xia_gua['象']} - {xia_gua['属性']} ({xia_gua['五行']})

【变卦】{bian_gua_name}
上卦：{bian_shang_gua['名']} {bian_shang_gua['象']} - {bian_shang_gua['属性']}
下卦：{bian_xia_gua['名']} {bian_xia_gua['象']} - {bian_xia_gua['属性']}
动爻：第{dong_yao}爻

【体用分析】
体卦：{ti_gua['名']}卦 ({ti_gua['五行']})
用卦：{yong_gua['名']}卦 ({yong_gua['五行']})
关系：体用相{ti_yong_relation}

【五行分析】
上卦五行：{shang_gua['五行']}
下卦五行：{xia_gua['五行']}
体用关系：{ti_gua['五行']}{ti_yong_relation}{yong_gua['五行']}

【卦象解析】
本卦{ben_gua_name}，{shang_gua['属性']}在上，{xia_gua['属性']}在下。
第{dong_yao}爻发动，变为{bian_gua_name}。
体卦为{ti_gua['名']}，用卦为{yong_gua['名']}，体用相{ti_yong_relation}。

注：此为简化版梅花心易排盘，完整功能需安装梅花心易模块
            """

            st.code(result_text, language="text")

            # 显示详细信息
            with st.expander("查看卦象详情"):
                st.write("**本卦构成**:")
                st.write(f"上卦: {shang_gua['名']} - {shang_gua['属性']} - {shang_gua['五行']} - {shang_gua['方位']}")
                st.write(f"下卦: {xia_gua['名']} - {xia_gua['属性']} - {xia_gua['五行']} - {xia_gua['方位']}")

                st.write("**变卦构成**:")
                st.write(f"上卦: {bian_shang_gua['名']} - {bian_shang_gua['属性']} - {bian_shang_gua['五行']}")
                st.write(f"下卦: {bian_xia_gua['名']} - {bian_xia_gua['属性']} - {bian_xia_gua['五行']}")

                st.write("**起卦数据**:")
                if method == "时间起卦":
                    st.write(f"上卦数: {shang_gua_num} (年月日: {year + month + day})")
                    st.write(f"下卦数: {xia_gua_num} (年月日时: {year + month + day + hour})")
                    st.write(f"动爻: {dong_yao} (年月日时分: {year + month + day + hour + minute})")
                elif method == "数字起卦":
                    st.write(f"上卦数: {shang_gua_num} (数字1: {num1})")
                    st.write(f"下卦数: {xia_gua_num} (数字2: {num2})")
                    st.write(f"动爻: {dong_yao} (数字和: {(num1 or 0) + (num2 or 0)})")

# 运行主应用
if __name__ == "__main__":
    main()