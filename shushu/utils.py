# -*- coding: utf-8 -*-
"""
太公三式统一应用工具函数
提供基本的历法计算和转换功能
"""

import datetime
import pytz
from sxtwl import fromSolar

def safe_lunar_date(year, month, day):
    """安全获取农历日期"""
    try:
        lunar_day = fromSolar(year, month, day)
        return {
            "年": lunar_day.getLunarYear(),
            "月": lunar_day.getLunarMonth(),
            "日": lunar_day.getLunarDay(),
            "农历月": f"{lunar_day.getLunarMonth()}月",
            "农历日": f"{lunar_day.getLunarDay()}日"
        }
    except Exception as e:
        return {
            "年": year,
            "月": month,
            "日": day,
            "农历月": f"{month}月",
            "农历日": f"{day}日"
        }

def safe_gangzhi(year, month, day, hour, minute):
    """安全获取干支信息"""
    try:
        # 基本的干支计算
        tian_gan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        di_zhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        # 简化的干支计算
        year_gan = tian_gan[(year - 4) % 10]
        year_zhi = di_zhi[(year - 4) % 12]
        
        # 月干支计算
        month_gan_base = (year - 4) % 10
        month_gan = tian_gan[(month_gan_base * 2 + month) % 10]
        month_zhi = di_zhi[(month + 1) % 12]
        
        # 日干支计算（简化）
        day_offset = (year - 1900) * 365 + (year - 1900) // 4 + sum([31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][:month-1]) + day
        if year % 4 == 0 and month > 2:
            day_offset += 1
        day_gan = tian_gan[day_offset % 10]
        day_zhi = di_zhi[day_offset % 12]
        
        # 时干支计算
        hour_zhi_index = (hour + 1) // 2 % 12
        hour_zhi = di_zhi[hour_zhi_index]
        hour_gan = tian_gan[(day_offset * 2 + hour_zhi_index) % 10]
        
        # 分干支计算
        minute_gan = tian_gan[minute % 10]
        minute_zhi = di_zhi[minute % 12]
        
        return [
            f"{year_gan}{year_zhi}",
            f"{month_gan}{month_zhi}",
            f"{day_gan}{day_zhi}",
            f"{hour_gan}{hour_zhi}",
            f"{minute_gan}{minute_zhi}"
        ]
    except Exception as e:
        return ["甲子", "甲子", "甲子", "甲子", "甲子"]

def safe_jieqi(year, month, day, hour, minute):
    """安全获取节气信息"""
    try:
        # 简化的节气计算
        jieqi_list = [
            "立春", "雨水", "惊蛰", "春分", "清明", "谷雨",
            "立夏", "小满", "芒种", "夏至", "小暑", "大暑",
            "立秋", "处暑", "白露", "秋分", "寒露", "霜降",
            "立冬", "小雪", "大雪", "冬至", "小寒", "大寒"
        ]
        
        # 根据月份返回大致的节气
        jieqi_index = (month - 1) * 2
        if day > 15:
            jieqi_index += 1
        
        return jieqi_list[jieqi_index % 24]
    except Exception as e:
        return "未知节气"

def format_datetime(year, month, day, hour, minute):
    """格式化日期时间"""
    try:
        dt = datetime.datetime(year, month, day, hour, minute)
        return dt.strftime("%Y年%m月%d日 %H时%M分")
    except Exception as e:
        return f"{year}年{month}月{day}日 {hour}时{minute}分"

def convert_traditional_to_simplified(text):
    """繁体字转简体字"""
    try:
        # 尝试使用opencc
        import opencc
        converter = opencc.OpenCC('t2s')  # 繁体转简体
        return converter.convert(text)
    except ImportError:
        # 如果opencc不可用，使用基本映射
        conversion_map = {
            # 常用繁体字转换
            '農': '农', '曆': '历', '節': '节', '氣': '气', '時': '时', '計': '计',
            '門': '门', '開': '开', '關': '关', '來': '来', '對': '对', '會': '会',
            '學': '学', '業': '业', '專': '专', '個': '个', '們': '们', '這': '这',
            '那': '那', '裡': '里', '頭': '头', '還': '还', '沒': '没', '過': '过',
            '說': '说', '話': '话', '語': '语', '讀': '读', '寫': '写', '聽': '听',
            '見': '见', '覺': '觉', '認': '认', '識': '识', '記': '记', '憶': '忆',
            '愛': '爱', '親': '亲', '關': '关', '係': '系', '連': '连', '運': '运',
            '動': '动', '靜': '静', '聲': '声', '響': '响', '樂': '乐', '歌': '歌',
            '舞': '舞', '戲': '戏', '劇': '剧', '電': '电', '視': '视', '機': '机',
            '車': '车', '馬': '马', '鳥': '鸟', '魚': '鱼', '蟲': '虫', '樹': '树',
            '花': '花', '草': '草', '葉': '叶', '果': '果', '實': '实', '種': '种',
            '類': '类', '樣': '样', '種': '种', '別': '别', '特': '特', '殊': '殊',
            '異': '异', '同': '同', '共': '共', '總': '总', '全': '全', '部': '部',
            '分': '分', '半': '半', '少': '少', '多': '多', '大': '大', '小': '小',
            '長': '长', '短': '短', '高': '高', '低': '低', '深': '深', '淺': '浅',
            '厚': '厚', '薄': '薄', '寬': '宽', '窄': '窄', '粗': '粗', '細': '细',
            '重': '重', '輕': '轻', '快': '快', '慢': '慢', '早': '早', '晚': '晚',
            '新': '新', '舊': '旧', '老': '老', '少': '少', '年': '年', '輕': '轻',
            '強': '强', '弱': '弱', '好': '好', '壞': '坏', '美': '美', '醜': '丑',
            '善': '善', '惡': '恶', '正': '正', '邪': '邪', '真': '真', '假': '假',
            '實': '实', '虛': '虚', '空': '空', '滿': '满', '豐': '丰', '富': '富',
            '貧': '贫', '窮': '穷', '貴': '贵', '賤': '贱', '榮': '荣', '辱': '辱',
            '勝': '胜', '敗': '败', '成': '成', '功': '功', '失': '失', '敗': '败',
            '進': '进', '退': '退', '上': '上', '下': '下', '左': '左', '右': '右',
            '前': '前', '後': '后', '內': '内', '外': '外', '中': '中', '間': '间',
            '東': '东', '西': '西', '南': '南', '北': '北', '方': '方', '向': '向',
            '處': '处', '所': '所', '地': '地', '方': '方', '位': '位', '置': '置',
            '國': '国', '家': '家', '鄉': '乡', '村': '村', '城': '城', '市': '市',
            '縣': '县', '區': '区', '街': '街', '路': '路', '橋': '桥', '河': '河',
            '山': '山', '海': '海', '湖': '湖', '江': '江', '水': '水', '火': '火',
            '土': '土', '木': '木', '金': '金', '石': '石', '玉': '玉', '珠': '珠',
            '寶': '宝', '錢': '钱', '財': '财', '富': '富', '貨': '货', '物': '物',
            '品': '品', '件': '件', '個': '个', '隻': '只', '匹': '匹', '條': '条',
            '根': '根', '支': '支', '枝': '枝', '片': '片', '塊': '块', '顆': '颗',
            '粒': '粒', '滴': '滴', '點': '点', '線': '线', '面': '面', '體': '体',
            '形': '形', '狀': '状', '色': '色', '彩': '彩', '光': '光', '明': '明',
            '暗': '暗', '亮': '亮', '黑': '黑', '白': '白', '紅': '红', '綠': '绿',
            '藍': '蓝', '黃': '黄', '紫': '紫', '灰': '灰', '棕': '棕', '粉': '粉',
            # 占卜相关繁体字
            '壬': '壬', '奇': '奇', '門': '门', '遁': '遁', '甲': '甲', '太': '太',
            '乙': '乙', '神': '神', '數': '数', '盤': '盘', '排': '排', '局': '局',
            '課': '课', '傳': '传', '將': '将', '星': '星', '宮': '宫', '干': '干',
            '支': '支', '陰': '阴', '陽': '阳', '五': '五', '行': '行', '八': '八',
            '卦': '卦', '九': '九', '宮': '宫', '十': '十', '二': '二', '地': '地',
            '天': '天', '人': '人', '鬼': '鬼', '神': '神', '煞': '煞', '吉': '吉',
            '凶': '凶', '旺': '旺', '相': '相', '休': '休', '囚': '囚', '死': '死'
        }

        result = text
        for traditional, simplified in conversion_map.items():
            result = result.replace(traditional, simplified)

        return result

def convert_simplified_to_traditional(text):
    """简体字转繁体字"""
    try:
        # 尝试使用opencc
        import opencc
        converter = opencc.OpenCC('s2t')  # 简体转繁体
        return converter.convert(text)
    except ImportError:
        # 如果opencc不可用，返回原文
        return text

# 基本的五行和干支映射
WUXING_MAP = {
    '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土', 
    '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
    '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
    '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
    '戌': '土', '亥': '水'
}

def get_wuxing(ganzhi_char):
    """获取干支的五行属性"""
    return WUXING_MAP.get(ganzhi_char, '未知')
