#!/bin/bash

# 太公心易兜率宫专用8501端口启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏛️  太公心易兜率宫 - 8501端口专用启动${NC}"
echo -e "${BLUE}===========================================${NC}"

# 停止所有streamlit进程
echo -e "${YELLOW}🔄 清理现有streamlit进程...${NC}"
pkill -f streamlit 2>/dev/null || true
sleep 2

# 检查8501端口
echo -e "${YELLOW}🔍 检查8501端口状态...${NC}"
if lsof -i :8501 > /dev/null 2>&1; then
    echo -e "${RED}❌ 端口8501被占用，正在释放...${NC}"
    lsof -ti:8501 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 验证端口已释放
if lsof -i :8501 > /dev/null 2>&1; then
    echo -e "${RED}❌ 无法释放端口8501，请手动检查${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 端口8501已准备就绪${NC}"
fi

# 检查Python和依赖
echo -e "${YELLOW}🔍 检查环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装${NC}"
    exit 1
fi

# 新增：检查Doppler CLI
if ! command -v doppler &> /dev/null; then
    echo -e "${RED}❌ Doppler CLI 未安装。请根据官方文档安装: https://docs.doppler.com/reference/cli${NC}"
    exit 1
fi

if ! python3 -c "import streamlit" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  安装Streamlit...${NC}"
    pip3 install streamlit
fi

# 检查应用文件
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ app.py 文件不存在${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境检查完成${NC}"

# 启动应用
echo -e "${BLUE}🚀 在8501端口启动太公心易兜率宫...${NC}"
echo ""
echo -e "${GREEN}访问地址: http://localhost:8501${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止应用${NC}"
echo ""

# 启动streamlit
echo -e "${YELLOW}🔐 使用Doppler注入密钥...${NC}"
doppler run -- streamlit run app.py --server.port 8501
