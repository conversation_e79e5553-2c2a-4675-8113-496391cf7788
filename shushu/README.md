# 太公三式 (Taigong Sanshi)

中国传统占卜系统的现代化实现，融合六壬、奇门、太乙三式于一体，提供Streamlit网页应用和FastAPI服务。

## 🌟 项目特色

- **三式合一**: 将大六壬、奇门遁甲、太乙神数三个占卜系统整合到一个应用中
- **双重界面**: 提供Streamlit网页界面和FastAPI REST API服务
- **繁简支持**: 支持繁体中文和简体中文界面切换
- **模块化设计**: 即使某些占卜模块未安装，应用仍可正常运行并显示基本信息
- **现代化技术**: 使用Python、Streamlit、FastAPI等现代技术栈

## 📦 项目结构

```
taigongxinyi/
├── app.py              # 主Streamlit应用
├── api.py              # FastAPI服务
├── utils.py            # 工具函数
├── start.py            # 启动脚本
├── requirements.txt    # 依赖包列表
├── kinliuren/          # 六壬模块
├── kinqimen/           # 奇门模块
├── kintaiyi/           # 太乙模块
└── core/               # 核心模块
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- pip

### 安装依赖

```bash
# 克隆项目
git clone <your-repo-url>
cd taigongxinyi

# 安装依赖
pip install -r requirements.txt
```

### 启动应用

使用启动脚本（推荐）：

```bash
# 检查依赖
python start.py check

# 启动Streamlit网页应用
python start.py streamlit

# 启动FastAPI服务
python start.py api

# 同时启动两个服务
python start.py both
```

或者手动启动：

```bash
# 启动Streamlit应用
streamlit run app.py

# 启动FastAPI服务
uvicorn api:app --host 0.0.0.0 --port 8000 --reload
```

## 项目结构

```
taigong-sanshi/
├── kinliuren/          # 大六壬占卜应用
│   ├── app.py          # 主应用文件
│   ├── kinliuren/      # 核心逻辑包
│   ├── jieqi.py        # 节气计算
│   └── requirements.txt
├── kinqimen/           # 奇門遁甲占卜应用
│   ├── app.py          # 主应用文件
│   ├── kinqimen.py     # 核心逻辑
│   └── requirements.txt
└── kintaiyi/           # 太乙神數占卜应用
    ├── app.py          # 主应用文件
    ├── kintaiyi.py     # 核心逻辑
    ├── cerebras_client.py # AI集成
    ├── chart.py        # 图表生成
    └── requirements.txt
```

## 功能特点

- 🗓️ 完整的中国农历和节气支持
- 📊 传统占卜图表可视化
- 📚 古籍文献参考
- 🌐 响应式Web界面
- 🎯 精确的传统算法实现

## 技术栈

- **前端**: Streamlit
- **后端**: Python
- **日期处理**: pendulum, sxtwl
- **天文计算**: ephem
- **图表**: 自定义SVG生成

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

请查看各个子项目的LICENSE文件了解具体许可证信息。

## 🎯 功能特色

### 📊 坚六壬 (大六壬)
- **完整排盘**: 支持月课、日课、时课排盘
- **四课三传**: 自动计算四课三传格局
- **格局识别**: 智能识别各种六壬格局
- **节气支持**: 精确的二十四节气计算

### 🏠 坚奇门 (奇门遁甲)
- **时家奇门**: 传统时家奇门排盘
- **刻家奇门**: 精密刻家奇门排盘
- **拆补置闰**: 支持两种排盘方式
- **九宫八卦**: 完整的奇门格局展示

### ⚡ 坚太乙 (太乙神数)
- **多种起盘**: 年计、月计、日计、时计、分计太乙
- **太乙命法**: 专门的命理分析功能
- **积年数法**: 支持多种太乙积年数计算方法
- **历史案例**: 丰富的历史事件数据

## 🔧 API接口

### 基础信息
- **基础URL**: `http://localhost:8000`
- **文档地址**: `http://localhost:8000/docs`
- **健康检查**: `GET /health`

### 主要端点

#### 六壬排盘
```http
POST /api/liuren
Content-Type: application/json

{
  "year": 2024,
  "month": 1,
  "day": 1,
  "hour": 12,
  "minute": 0
}
```

#### 奇门遁甲
```http
POST /api/qimen
Content-Type: application/json

{
  "year": 2024,
  "month": 1,
  "day": 1,
  "hour": 12,
  "minute": 0,
  "qimen_type": 1,
  "pai_type": 1
}
```

#### 太乙神数
```http
POST /api/taiyi
Content-Type: application/json

{
  "year": 2024,
  "month": 1,
  "day": 1,
  "hour": 12,
  "minute": 0,
  "style": 3,
  "tn": 0,
  "sex": "男"
}
```

## 📖 使用说明

### Streamlit网页应用
1. 在左侧边栏设置日期和时间
2. 选择界面语言（简体/繁体）
3. 选择要使用的占卜系统标签页
4. 配置相关参数
5. 点击"开始排盘"按钮
6. 查看排盘结果

### API服务
1. 启动API服务
2. 访问 `http://localhost:8000/docs` 查看交互式文档
3. 使用HTTP客户端发送请求
4. 获取JSON格式的排盘结果

## ⚠️ 注意事项

1. **模块依赖**: 某些占卜模块可能需要额外安装，应用会显示相应状态
2. **时间准确性**: 建议使用准确的时间，考虑时区和真太阳时
3. **结果解读**: 排盘结果需要结合传统理论进行专业解读
4. **学习目的**: 本系统主要用于学习研究，不构成决策建议

## 🤝 致谢

感谢所有为中国传统文化传承和现代化做出贡献的开发者和研究者。

## 联系

如有问题或建议，请通过GitHub Issues联系我们。

---

**免责声明**: 本应用仅供学习和研究使用，不构成任何投资建议或决策依据。使用者应当理性对待占卜结果，不应过度依赖。