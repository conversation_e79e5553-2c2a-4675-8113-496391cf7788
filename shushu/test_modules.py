#!/usr/bin/env python3
"""
测试模块导入状态
"""

import sys
from pathlib import Path

# 模拟app.py中的路径设置
current_dir = str(Path(__file__).parent.resolve())
local_modules = ["core"]
for module in local_modules:
    module_path = str(Path(current_dir) / module)
    if module_path not in sys.path:
        sys.path.insert(0, module_path)

print("🔍 模块导入测试")
print("=" * 50)

# 测试kinliuren
try:
    import kinliuren
    print("✅ kinliuren 导入成功")
    print(f"   版本: {getattr(kinliuren, '__version__', '未知')}")
    print(f"   路径: {kinliuren.__file__}")
    KINLIUREN_AVAILABLE = True
except ImportError as e:
    print(f"❌ kinliuren 导入失败: {e}")
    KINLIUREN_AVAILABLE = False

# 测试kinqimen
try:
    import kinqimen
    print("✅ kinqimen 导入成功")
    print(f"   版本: {getattr(kinqimen, '__version__', '未知')}")
    print(f"   路径: {kinqimen.__file__}")
    KINQIMEN_AVAILABLE = True
except ImportError as e:
    print(f"❌ kinqimen 导入失败: {e}")
    KINQIMEN_AVAILABLE = False

# 测试kintaiyi
try:
    import kintaiyi
    print("✅ kintaiyi 导入成功")
    print(f"   版本: {getattr(kintaiyi, '__version__', '未知')}")
    print(f"   路径: {kintaiyi.__file__}")
    KINTAIYI_AVAILABLE = True
except ImportError as e:
    print(f"❌ kintaiyi 导入失败: {e}")
    KINTAIYI_AVAILABLE = False

# 测试core.meihua
try:
    from core.meihua import meihua_yishu
    print("✅ core.meihua 导入成功")
    MEIHUA_AVAILABLE = True
except ImportError as e:
    print(f"❌ core.meihua 导入失败: {e}")
    MEIHUA_AVAILABLE = False

print("\n📊 模块状态总结:")
print("=" * 50)
print(f"大六壬: {'✅ 可用' if KINLIUREN_AVAILABLE else '❌ 不可用'}")
print(f"奇门遁甲: {'✅ 可用' if KINQIMEN_AVAILABLE else '❌ 不可用'}")
print(f"太乙神数: {'✅ 可用' if KINTAIYI_AVAILABLE else '❌ 不可用'}")
print(f"梅花心易: {'✅ 可用' if MEIHUA_AVAILABLE else '❌ 不可用'}")

if KINQIMEN_AVAILABLE and KINTAIYI_AVAILABLE:
    print("\n🎉 所有主要模块都已正常加载！")
else:
    print("\n⚠️  部分模块未能正常加载，请检查安装。")
