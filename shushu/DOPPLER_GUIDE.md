# 使用 Doppler 管理项目密钥

本项目使用 [Doppler](https://www.doppler.com/) 来安全地管理环境变量和密钥。

## 设置步骤

1.  **安装 Doppler CLI**

    根据你的操作系统，按照 [官方文档](https://docs.doppler.com/reference/cli) 安装 Doppler CLI。

2.  **登录 Doppler**

    在你的终端中运行以下命令，并按照提示登录你的 Doppler 账户：

    ```bash
    doppler login
    ```

3.  **项目设置**

    使用以下命令来设置项目。它会将你的本地目录连接到 Doppler 上的相应项目和配置。

    ```bash
    doppler setup
    ```

    你需要从 Doppler 仪表板选择正确的项目和配置（例如，`dev`、`staging` 或 `prd`）。

4.  **添加密钥**

    在 Doppler 仪表板中，为你选择的项目和配置添加以下密钥：

    *   `NEO4J_URI`: Neo4j 数据库的连接 URI (例如, `bolt://localhost:7687`)
    *   `NEO4J_USER`: Neo4j 数据库的用户名
    *   `NEO4J_PASSWORD`: Neo4j 数据库的密码
    *   `SECRET_KEY`: Flask 应用程序的密钥

5.  **运行应用程序**

    完成上述步骤后，你可以使用提供的启动脚本来运行应用程序。该脚本会自动使用 Doppler 注入密钥。

    ```bash
    cd shushu
    ./start_8501.sh
    ```

    `doppler run` 命令会获取最新的密钥，并将它们作为环境变量提供给应用程序进程。