import streamlit as st
from app import safe_gangzhi, safe_jieqi, format_datetime, KINLIUREN_AVAILABLE

st.set_page_config(
    page_title="大六壬",
    page_icon="📊",
    layout="wide"
)

st.title("📊 大六壬占卜")
st.markdown("大六壬是中国古代占卜术之一，与奇门遁甲、太乙神数并称为'三式'。")

# 时间输入
st.sidebar.subheader("📅 时间设置")
col1, col2 = st.sidebar.columns(2)
with col1:
    year = st.number_input("年", min_value=1900, max_value=2100, value=2025)
    month = st.number_input("月", min_value=1, max_value=12, value=1)
    day = st.number_input("日", min_value=1, max_value=31, value=20)
with col2:
    hour = st.number_input("时", min_value=0, max_value=23, value=10)
    minute = st.number_input("分", min_value=0, max_value=59, value=30)

if st.button("开始六壬排盘", key="liuren_calc"):
    with st.spinner("正在计算六壬排盘..."):
        if KINLIUREN_AVAILABLE:
            try:
                # 获取干支和节气
                gz = safe_gangzhi(year, month, day, hour, minute)
                jq = safe_jieqi(year, month, day, hour, minute)

                # 使用真正的kinliuren PyPI包
                from kinliuren import kinliuren as lr
                result = lr.Liuren(jq, str(month), gz[2], gz[3]).result(0)

                # 格式化显示结果
                result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 大六壬

【基本信息】
节气：{result.get('節氣', jq)}
日期：{result.get('日期', f"{gz[2]}日{gz[3]}时")}
格局：{', '.join(result.get('格局', ['未知']))}

【四课】
{result.get('四課', {}).get('一課', ['', ''])[0]} - {result.get('四課', {}).get('一課', ['', ''])[1]}
{result.get('四課', {}).get('二課', ['', ''])[0]} - {result.get('四課', {}).get('二課', ['', ''])[1]}
{result.get('四課', {}).get('三課', ['', ''])[0]} - {result.get('四課', {}).get('三課', ['', ''])[1]}
{result.get('四課', {}).get('四課', ['', ''])[0]} - {result.get('四課', {}).get('四課', ['', ''])[1]}

【三传】
初传：{result.get('三傳', {}).get('初傳', ['', '', '', ''])[0]} - {result.get('三傳', {}).get('初傳', ['', '', '', ''])[1]}
中传：{result.get('三傳', {}).get('中傳', ['', '', '', ''])[0]} - {result.get('三傳', {}).get('中傳', ['', '', '', ''])[1]}
末传：{result.get('三傳', {}).get('末傳', ['', '', '', ''])[0]} - {result.get('三傳', {}).get('末傳', ['', '', '', ''])[1]}

【神煞】
日马：{result.get('神煞', {}).get('日馬', '未知')}
天马：{result.get('神煞', {}).get('天馬', '未知')}
贵人：{result.get('神煞', {}).get('賢貴', '未知')}

注：使用kinliuren官方算法 v0.1.2.8
                """

                st.code(result_text, language="text")

                # 显示详细信息
                with st.expander("查看完整结果"):
                    st.json(result)

            except Exception as e:
                st.error(f"六壬计算错误: {str(e)}")
        else:
            st.error("kinliuren模块未加载，请检查安装")