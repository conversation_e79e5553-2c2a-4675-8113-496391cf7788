import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
import json
import random
import qrcode
from PIL import Image
import io
import base64

st.set_page_config(page_title="太公心易 Avatar 生成器", page_icon="🌀")

st.title("🌀 Avatar 生成器")
st.markdown("""
Welcome Onboard! 
针对付费用户，这是一个登上甲板的步骤————这是一个独特的茱莉亚集合将成为您的匿名身份标识（Avatar）。

曼德勃罗的本质是寻找符合收敛条件的复平面集合，形如 `Z(n+1) = Z² + c`。当 `Z` 从0开始迭代，`c` 为变量时，我们得到的是曼德勃罗集合；当 `c` 为常量，`Z` 为变量时，我们得到的是更古老的茱莉亚集合。

茱莉亚集合并非一定在曼德勃罗集合的内部。这就让我们可以根据茱莉亚集生成头像，并线性转换为一个QR code，满足匿名联系的需求。

**此功能尚处于Alpha，仅对太乙观澜用户开放。**
""")

# --- 核心函数 ---

def mandelbrot(c, max_iter):
    z = 0
    for n in range(max_iter):
        z = z**2 + c
        if abs(z) > 2: return n
    return max_iter

def julia(z, c, max_iter):
    for n in range(max_iter):
        z = z**2 + c
        if abs(z) > 2: return n
    return max_iter

@st.cache_data
def calculate_mandelbrot_set(_c_plane, max_iter):
    return np.vectorize(mandelbrot)(_c_plane, max_iter)

@st.cache_data
def calculate_julia_set(c_real, c_imag, max_iter):
    c = complex(c_real, c_imag)
    x = np.linspace(-1.5, 1.5, 400)
    y = np.linspace(-1.5, 1.5, 400)
    X, Y = np.meshgrid(x, y)
    z = X + 1j * Y
    return np.vectorize(julia)(z, c, max_iter)

def fig_to_bytes(fig):
    """将 matplotlib figure 转换为 bytes"""
    buf = io.BytesIO()
    fig.savefig(buf, format='png', bbox_inches='tight', pad_inches=0.1)
    buf.seek(0)
    return buf.getvalue()

def generate_qr_code(data):
    """根据输入数据生成二维码图像"""
    qr = qrcode.QRCode(version=1, error_correction=qrcode.ERROR_CORRECT_L, box_size=10, border=4)
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    return img

# --- 侧边栏控件 ---
with st.sidebar:
    st.header("🎨 样式参数")
    max_iter = st.slider("迭代次数 (细节)", 50, 1000, 150, 10)
    cmap = st.selectbox("选择色系", ['inferno', 'magma', 'plasma', 'viridis', 'cividis', 'twilight', 'twilight_shifted', 'turbo'])

    st.markdown("---")
    
    st.header("🆔 身份参数 (c 值)")
    if 'c_real' not in st.session_state:
        st.session_state.c_real = -0.8
    if 'c_imag' not in st.session_state:
        st.session_state.c_imag = 0.156

    if st.button("🎲 随机生成 c 值", use_container_width=True):
        st.session_state.c_real = random.uniform(-1.5, 0.5)
        st.session_state.c_imag = random.uniform(-1.0, 1.0)

    c_real = st.slider("c 的实部", -2.0, 1.0, st.session_state.c_real, 0.001, key="c_real_slider")
    c_imag = st.slider("c 的虚部", -1.5, 1.5, st.session_state.c_imag, 0.001, key="c_imag_slider")
    
    st.session_state.c_real = c_real
    st.session_state.c_imag = c_imag

c_julia = complex(st.session_state.c_real, st.session_state.c_imag)

# --- 主布局 ---
col1, col2 = st.columns(2)

# --- 左侧：曼德勃罗集合 ---
with col1:
    st.subheader("曼德勃罗集合 (c 值地图)")
    
    x_min, x_max = -2.0, 1.0
    y_min, y_max = -1.5, 1.5
    
    x = np.linspace(x_min, x_max, 600)
    y = np.linspace(y_min, y_max, 600)
    X, Y = np.meshgrid(x, y)
    c_plane = X + 1j * Y

    mandelbrot_set = calculate_mandelbrot_set(c_plane, max_iter)

    fig_m, ax_m = plt.subplots(figsize=(8, 8))
    ax_m.imshow(mandelbrot_set, cmap=cmap, extent=(x_min, x_max, y_min, y_max), origin='lower')
    ax_m.plot(c_julia.real, c_julia.imag, 'rx', markersize=12, markeredgewidth=2)
    ax_m.set_title(f'Mandelbrot Set (c = {c_julia.real:.4f} + {c_julia.imag:.4f}j)')
    ax_m.set_xlabel("Real Part")
    ax_m.set_ylabel("Imaginary Part")
    ax_m.set_xlim((x_min, x_max))
    ax_m.set_ylim((y_min, y_max))
    
    st.pyplot(fig_m)

# --- 右侧：茱莉亚集合 Avatar ---
with col2:
    st.subheader("您的茱莉亚集合 Avatar")
    
    with st.spinner("正在生成您的专属 Avatar..."):
        julia_set = calculate_julia_set(c_julia.real, c_julia.imag, max_iter)
        
        fig_j, ax_j = plt.subplots(figsize=(8, 8))
        ax_j.imshow(julia_set, cmap=cmap, extent=(-1.5, 1.5, -1.5, 1.5), origin='lower')
        ax_j.set_title(f'Julia Set Avatar (c = {c_julia.real:.4f} + {c_julia.imag:.4f}j)')
        ax_j.axis('off')
        st.pyplot(fig_j)

        # --- 下载 Avatar ---
        st.download_button(
            label="📥 下载 Avatar 图像",
            data=fig_to_bytes(fig_j),
            file_name=f"taigong_avatar_{c_julia.real:.4f}_{c_julia.imag:.4f}.png",
            mime="image/png"
        )

        # --- 二维码生成与下载 (未来功能) ---
        # st.markdown("---")
        # st.subheader("您的身份二维码")

        # # 准备数据 (仅包含ID)
        # user_data = {
        #     "system": "taigong-xinyi",
        #     "unique_id": {"real": c_julia.real, "imag": c_julia.imag}
        # }
        # qr_data_str = json.dumps(user_data)
        
        # # 生成并显示二维码
        # qr_img = generate_qr_code(qr_data_str)
        # st.image(np.array(qr_img), caption="扫描或保存此二维码作为您的唯一ID", width=250)

        # # 提供下载按钮
        # qr_buf = io.BytesIO()
        # qr_img.save(qr_buf, "PNG")
        # st.download_button(
        #     label="📥 下载身份二维码",
        #     data=qr_buf.getvalue(),
        #     file_name=f"taigong_id_qr_{c_julia.real:.4f}_{c_julia.imag:.4f}.png",
        #     mime="image/png"
        # )