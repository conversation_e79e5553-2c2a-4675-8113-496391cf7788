import streamlit as st
from app import format_datetime, KINTAIYI_AVAILABLE

st.set_page_config(
    page_title="太乙神数",
    page_icon="🏛️",
    layout="wide"
)

st.title("🏛️ 太乙神数占卜")
st.markdown("太乙神数是中国古代占卜术中用于预测国运和个人命运的高级术数。")

# 时间输入
st.sidebar.subheader("📅 时间设置")
col1, col2 = st.sidebar.columns(2)
with col1:
    year = st.number_input("年", min_value=1900, max_value=2100, value=2025)
    month = st.number_input("月", min_value=1, max_value=12, value=1)
    day = st.number_input("日", min_value=1, max_value=31, value=20)
with col2:
    hour = st.number_input("时", min_value=0, max_value=23, value=10)
    minute = st.number_input("分", min_value=0, max_value=59, value=30)

style = st.selectbox("起盘方式", ["时计太乙", "年计太乙", "月计太乙", "日计太乙", "分计太乙", "太乙命法"])
sex = st.selectbox("性别", ["男", "女"])

if st.button("开始太乙排盘", key="taiyi_calc"):
    with st.spinner("正在计算太乙排盘..."):
        if KINTAIYI_AVAILABLE:
            try:
                from kintaiyi.kintaiyi import Taiyi
                result = Taiyi(year, month, day, hour).pan()

                result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 太乙神数

【占卜结果】
{result}

注：使用kintaiyi官方算法
                """
                st.code(result_text, language="text")
                with st.expander("查看完整结果"):
                    st.json(result)
            except Exception as e:
                st.error(f"太乙计算错误: {str(e)}")
        else:
            st.error("kintaiyi模块未加载，请检查安装")