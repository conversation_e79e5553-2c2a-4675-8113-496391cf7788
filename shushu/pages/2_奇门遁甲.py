import streamlit as st
from app import format_datetime, KINQIMEN_AVAILABLE

st.set_page_config(
    page_title="奇门遁甲",
    page_icon="🌟",
    layout="wide"
)

st.title("🌟 奇门遁甲占卜")
st.markdown("奇门遁甲是中华民族的精典著作，也是奇门、六壬、太乙三大秘宝中的第一大秘术。")

# 时间输入
st.sidebar.subheader("📅 时间设置")
col1, col2 = st.sidebar.columns(2)
with col1:
    year = st.number_input("年", min_value=1900, max_value=2100, value=2025)
    month = st.number_input("月", min_value=1, max_value=12, value=1)
    day = st.number_input("日", min_value=1, max_value=31, value=20)
with col2:
    hour = st.number_input("时", min_value=0, max_value=23, value=10)
    minute = st.number_input("分", min_value=0, max_value=59, value=30)

qimen_type = st.selectbox("选择奇门类型", ["时家奇门", "刻家奇门"])
pai_type = st.selectbox("选择排盘方式", ["拆补", "置闰"])

if st.button("开始奇门排盘", key="qimen_calc"):
    with st.spinner("正在计算奇门排盘..."):
        if KINQIMEN_AVAILABLE:
            try:
                # 修复kinqimen模块的导入问题
                import sys
                import os
                kinqimen_path = os.path.dirname(__import__('kinqimen').__file__)
                if kinqimen_path not in sys.path:
                    sys.path.insert(0, kinqimen_path)

                from kinqimen.kinqimen import Qimen
                qimen_instance = Qimen(year, month, day, hour, minute)
                result = qimen_instance.pan()

                result_text = f"""
{format_datetime(year, month, day, hour, minute)} | 奇门遁甲

【占卜结果】
{result}

注：使用kinqimen官方算法
                """
                st.code(result_text, language="text")
                with st.expander("查看完整结果"):
                    st.json(result)
            except Exception as e:
                st.error(f"奇门计算错误: {str(e)}")
                # 提供备用算法
                st.info("正在使用备用算法...")
                try:
                    from core.qimen import qimen_basic
                    backup_result = qimen_basic(year, month, day, hour, minute)
                    st.code(f"""
{format_datetime(year, month, day, hour, minute)} | 奇门遁甲 (备用算法)

【占卜结果】
{backup_result}

注：使用本地备用算法
                    """, language="text")
                except Exception as backup_e:
                    st.error(f"备用算法也失败了: {str(backup_e)}")
        else:
            st.error("kinqimen模块未加载，请检查安装")