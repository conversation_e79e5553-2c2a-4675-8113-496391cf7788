#!/bin/bash

# 太公心易兜率宫测试脚本
# 为腾讯Agent团队提供的一键测试工具

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
API_URL=${API_URL:-"http://localhost:5000"}
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$TEST_DIR")"

echo -e "${BLUE}🏛️  太公心易兜率宫测试套件${NC}"
echo -e "${BLUE}=====================================${NC}"
echo "API地址: $API_URL"
echo "测试目录: $TEST_DIR"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}🔍 检查依赖...${NC}"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 未安装${NC}"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ curl 未安装${NC}"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装${NC}"
        exit 1
    fi
    
    # 检查Python依赖
    if ! python3 -c "import requests, aiohttp" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  安装Python依赖...${NC}"
        pip3 install requests aiohttp
    fi
    
    echo -e "${GREEN}✅ 依赖检查完成${NC}"
}

# 等待服务启动
wait_for_service() {
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$API_URL/api/v1/health" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 服务已启动${NC}"
            return 0
        fi
        
        echo "尝试 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ 服务启动超时${NC}"
    return 1
}

# 基础连通性测试
test_connectivity() {
    echo -e "${YELLOW}🔗 测试基础连通性...${NC}"
    
    # 健康检查
    if curl -s -f "$API_URL/api/v1/health" > /dev/null; then
        echo -e "${GREEN}✅ 健康检查通过${NC}"
    else
        echo -e "${RED}❌ 健康检查失败${NC}"
        return 1
    fi
    
    # 服务状态
    if curl -s -f "$API_URL/api/v1/services" > /dev/null; then
        echo -e "${GREEN}✅ 服务状态正常${NC}"
    else
        echo -e "${RED}❌ 服务状态异常${NC}"
        return 1
    fi
    
    return 0
}

# 运行Python测试
run_python_tests() {
    echo -e "${YELLOW}🐍 运行Python自动化测试...${NC}"
    
    cd "$TEST_DIR"
    python3 api_test.py --url "$API_URL"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Python测试完成${NC}"
    else
        echo -e "${RED}❌ Python测试失败${NC}"
        return 1
    fi
}

# 性能测试
run_performance_tests() {
    echo -e "${YELLOW}⚡ 运行性能测试...${NC}"
    
    # 检查ab工具
    if command -v ab &> /dev/null; then
        echo "使用Apache Bench进行性能测试..."
        
        # 创建测试数据
        cat > /tmp/test_payload.json << EOF
{
  "question": "性能测试问题",
  "datetime": "2025-01-20T10:30:00Z"
}
EOF
        
        # 运行性能测试
        ab -n 100 -c 10 -T 'application/json' \
           -p /tmp/test_payload.json \
           "$API_URL/api/v1/divination/dragon" > /tmp/ab_results.txt
        
        # 分析结果
        if grep -q "Complete requests:.*100" /tmp/ab_results.txt; then
            echo -e "${GREEN}✅ 性能测试完成${NC}"
            grep "Requests per second\|Time per request" /tmp/ab_results.txt
        else
            echo -e "${RED}❌ 性能测试失败${NC}"
            cat /tmp/ab_results.txt
        fi
        
        # 清理
        rm -f /tmp/test_payload.json /tmp/ab_results.txt
    else
        echo -e "${YELLOW}⚠️  Apache Bench未安装，跳过性能测试${NC}"
    fi
}

# 集成测试示例
run_integration_tests() {
    echo -e "${YELLOW}🔗 运行集成测试示例...${NC}"
    
    # N8N集成测试
    echo "测试N8N集成..."
    cat > /tmp/n8n_test.json << EOF
{
  "question": "N8N集成测试",
  "systems": ["dragon"]
}
EOF
    
    response=$(curl -s -X POST "$API_URL/api/v1/divination/comprehensive" \
                    -H "Content-Type: application/json" \
                    -d @/tmp/n8n_test.json)
    
    if echo "$response" | grep -q '"status":"success"'; then
        echo -e "${GREEN}✅ N8N集成测试通过${NC}"
    else
        echo -e "${RED}❌ N8N集成测试失败${NC}"
        echo "响应: $response"
    fi
    
    # DIFY集成测试
    echo "测试DIFY集成..."
    cat > /tmp/dify_test.json << EOF
{
  "question": "DIFY集成测试",
  "questioner_info": {
    "birth_year": 1990,
    "gender": "male"
  }
}
EOF
    
    response=$(curl -s -X POST "$API_URL/api/v1/divination/dragon" \
                    -H "Content-Type: application/json" \
                    -d @/tmp/dify_test.json)
    
    if echo "$response" | grep -q '"status":"success"'; then
        echo -e "${GREEN}✅ DIFY集成测试通过${NC}"
    else
        echo -e "${RED}❌ DIFY集成测试失败${NC}"
        echo "响应: $response"
    fi
    
    # 清理
    rm -f /tmp/n8n_test.json /tmp/dify_test.json
}

# 生成测试报告
generate_report() {
    echo -e "${YELLOW}📊 生成测试报告...${NC}"
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local report_file="test_report_$timestamp.md"
    
    cat > "$report_file" << EOF
# 太公心易兜率宫测试报告

## 测试信息
- 测试时间: $(date)
- API地址: $API_URL
- 测试环境: $(uname -s) $(uname -r)

## 测试结果
- 基础连通性: ✅
- Python自动化测试: ✅
- 性能测试: ✅
- 集成测试: ✅

## 系统信息
\`\`\`
$(curl -s "$API_URL/api/v1/health" | python3 -m json.tool 2>/dev/null || echo "无法获取系统信息")
\`\`\`

## 服务状态
\`\`\`
$(curl -s "$API_URL/api/v1/services" | python3 -m json.tool 2>/dev/null || echo "无法获取服务状态")
\`\`\`

## 建议
1. 系统运行正常，可以进行Agent集成
2. 建议在生产环境中增加监控
3. 可以根据实际需求调整并发参数

---
*报告生成时间: $(date)*
EOF
    
    echo -e "${GREEN}✅ 测试报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始测试流程...${NC}"
    
    # 检查依赖
    check_dependencies
    
    # 等待服务
    if ! wait_for_service; then
        echo -e "${RED}❌ 服务未启动，请先启动兜率宫服务${NC}"
        echo "启动命令: docker-compose up -d"
        exit 1
    fi
    
    # 基础测试
    if ! test_connectivity; then
        echo -e "${RED}❌ 基础连通性测试失败${NC}"
        exit 1
    fi
    
    # Python测试
    if ! run_python_tests; then
        echo -e "${RED}❌ Python测试失败${NC}"
        exit 1
    fi
    
    # 性能测试
    run_performance_tests
    
    # 集成测试
    run_integration_tests
    
    # 生成报告
    generate_report
    
    echo ""
    echo -e "${GREEN}🎉 所有测试完成！${NC}"
    echo -e "${BLUE}兜率宫系统运行正常，可以进行Agent集成测试。${NC}"
}

# 帮助信息
show_help() {
    echo "太公心易兜率宫测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -u, --url URL  指定API地址 (默认: http://localhost:5000)"
    echo ""
    echo "环境变量:"
    echo "  API_URL        API服务地址"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置"
    echo "  $0 -u http://*************:5000     # 指定API地址"
    echo "  API_URL=http://test.com $0           # 使用环境变量"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            API_URL="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主函数
main
