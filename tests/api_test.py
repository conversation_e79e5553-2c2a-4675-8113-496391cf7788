#!/usr/bin/env python3
"""
太公心易兜率宫 API 测试脚本
为腾讯Agent团队提供的自动化测试工具
"""

import requests
import json
import time
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, List, Any

class DoushuaiAPITester:
    """兜率宫API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_result(self, test_name: str, success: bool, message: str, response_time: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message} ({response_time:.3f}s)")
    
    def test_health_check(self):
        """测试健康检查接口"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/v1/health")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    self.log_result("健康检查", True, "系统状态正常", response_time)
                    return True
                else:
                    self.log_result("健康检查", False, f"系统状态异常: {data.get('status')}", response_time)
            else:
                self.log_result("健康检查", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_result("健康检查", False, f"请求异常: {str(e)}")
        
        return False
    
    def test_divination_liuren(self):
        """测试大六壬占卜"""
        try:
            start_time = time.time()
            payload = {
                "question": "今日运势如何？",
                "datetime": "2025-01-20T10:30:00Z",
                "options": {
                    "include_interpretation": True,
                    "detail_level": "full"
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/divination/liuren",
                json=payload
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and "result" in data:
                    self.log_result("大六壬占卜", True, "占卜成功", response_time)
                    return True
                else:
                    self.log_result("大六壬占卜", False, f"占卜失败: {data.get('message', '未知错误')}", response_time)
            else:
                self.log_result("大六壬占卜", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_result("大六壬占卜", False, f"请求异常: {str(e)}")
        
        return False
    
    def test_divination_qimen(self):
        """测试奇门遁甲占卜"""
        try:
            start_time = time.time()
            payload = {
                "question": "投资决策建议",
                "datetime": "2025-01-20T14:00:00Z",
                "location": {
                    "latitude": 39.9042,
                    "longitude": 116.4074
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/divination/qimen",
                json=payload
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    self.log_result("奇门遁甲占卜", True, "占卜成功", response_time)
                    return True
                else:
                    self.log_result("奇门遁甲占卜", False, f"占卜失败: {data.get('message', '未知错误')}", response_time)
            else:
                self.log_result("奇门遁甲占卜", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_result("奇门遁甲占卜", False, f"请求异常: {str(e)}")
        
        return False
    
    def test_divination_dragon(self):
        """测试十二龙子占卜"""
        try:
            start_time = time.time()
            payload = {
                "question": "感情运势",
                "questioner_info": {
                    "birth_year": 1990,
                    "gender": "male"
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/divination/dragon",
                json=payload
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    self.log_result("十二龙子占卜", True, "占卜成功", response_time)
                    return True
                else:
                    self.log_result("十二龙子占卜", False, f"占卜失败: {data.get('message', '未知错误')}", response_time)
            else:
                self.log_result("十二龙子占卜", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_result("十二龙子占卜", False, f"请求异常: {str(e)}")
        
        return False
    
    def test_divination_comprehensive(self):
        """测试综合占卜"""
        try:
            start_time = time.time()
            payload = {
                "question": "事业发展方向",
                "datetime": "2025-01-20T09:00:00Z",
                "systems": ["liuren", "qimen", "dragon"],
                "options": {
                    "analysis_depth": "deep"
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/divination/comprehensive",
                json=payload
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    self.log_result("综合占卜", True, "占卜成功", response_time)
                    return True
                else:
                    self.log_result("综合占卜", False, f"占卜失败: {data.get('message', '未知错误')}", response_time)
            else:
                self.log_result("综合占卜", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_result("综合占卜", False, f"请求异常: {str(e)}")
        
        return False
    
    async def test_concurrent_requests(self, num_requests: int = 50):
        """测试并发请求"""
        print(f"\n🚀 开始并发测试 ({num_requests}个请求)...")
        
        async def make_request(session, request_id):
            try:
                start_time = time.time()
                async with session.post(
                    f"{self.base_url}/api/v1/divination/dragon",
                    json={"question": f"并发测试请求{request_id}"}
                ) as response:
                    response_time = time.time() - start_time
                    if response.status == 200:
                        return {"success": True, "response_time": response_time}
                    else:
                        return {"success": False, "response_time": response_time, "status": response.status}
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            tasks = [make_request(session, i) for i in range(num_requests)]
            results = await asyncio.gather(*tasks)
            total_time = time.time() - start_time
        
        # 分析结果
        successful = sum(1 for r in results if r.get("success", False))
        failed = num_requests - successful
        avg_response_time = sum(r.get("response_time", 0) for r in results if "response_time" in r) / len(results)
        qps = num_requests / total_time
        
        success_rate = (successful / num_requests) * 100
        
        self.log_result(
            "并发测试",
            success_rate >= 95,
            f"成功率: {success_rate:.1f}%, QPS: {qps:.1f}, 平均响应时间: {avg_response_time:.3f}s",
            total_time
        )
        
        return success_rate >= 95
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始太公心易兜率宫API测试")
        print("=" * 60)
        
        # 基础功能测试
        print("\n📋 基础功能测试:")
        tests = [
            self.test_health_check,
            self.test_divination_liuren,
            self.test_divination_qimen,
            self.test_divination_dragon,
            self.test_divination_comprehensive
        ]
        
        basic_results = []
        for test in tests:
            basic_results.append(test())
        
        # 并发测试
        print("\n⚡ 性能测试:")
        concurrent_result = asyncio.run(self.test_concurrent_requests(50))
        
        # 生成测试报告
        self.generate_report(basic_results + [concurrent_result])
    
    def generate_report(self, test_results: List[bool]):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        # 响应时间统计
        response_times = [r["response_time"] for r in self.test_results if r["response_time"] > 0]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            print(f"平均响应时间: {avg_response_time:.3f}s")
            print(f"最大响应时间: {max_response_time:.3f}s")
        
        # 失败测试详情
        if failed_tests > 0:
            print("\n❌ 失败测试详情:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 保存详细报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 总体评价
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！系统状态良好。")
        elif passed_tests >= total_tests * 0.8:
            print("\n⚠️  大部分测试通过，但存在一些问题需要关注。")
        else:
            print("\n🚨 多个测试失败，系统存在严重问题。")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="太公心易兜率宫API测试工具")
    parser.add_argument("--url", default="http://localhost:5000", help="API服务地址")
    parser.add_argument("--concurrent", type=int, default=50, help="并发请求数量")
    
    args = parser.parse_args()
    
    tester = DoushuaiAPITester(args.url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
