#!/bin/bash
# 六行星项目架构清理脚本
# 删除不必要的Flask组件，专注于Streamlit + Neo4j

set -e

echo "🧹 开始清理六行星项目架构..."

# 备份重要文件
echo "📦 创建备份..."
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"

# 备份可能有用的配置
if [ -d "src/web" ]; then
    cp -r src/web "$BACKUP_DIR/"
    echo "  ✅ 已备份 src/web/"
fi

if [ -d "templates" ]; then
    cp -r templates "$BACKUP_DIR/"
    echo "  ✅ 已备份 templates/"
fi

# 删除不需要的组件
echo ""
echo "🗑️ 删除不必要的组件..."

if [ -d "src/web" ]; then
    rm -rf src/web
    echo "  ✅ 删除 src/web/ (Flask应用)"
fi

if [ -d "templates" ]; then
    rm -rf templates  
    echo "  ✅ 删除 templates/ (HTML模板)"
fi

if [ -d "scripts/rovodev" ]; then
    rm -rf scripts/rovodev
    echo "  ✅ 删除 scripts/rovodev/ (复杂的迁移脚本)"
fi

# 清理环境变量文件
echo ""
echo "🧽 清理环境变量..."
if [ -f ".env" ]; then
    # 只保留必要的环境变量
    cat > .env.new << EOF
# 六行星项目核心配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=sixstars123

# Gemini API (可选)
GEMINI_API_KEY=AIzaSyCalfmTapVVTYWVAPm923sz7UtitZbmsaM
EOF
    
    mv .env.new .env
    echo "  ✅ 简化 .env 文件"
fi

echo ""
echo "🎯 清理完成! 项目架构已简化为:"
echo "  📱 Streamlit应用: cd shushu && streamlit run app.py"
echo "  🗄️ Neo4j数据库: 200+术数书 + 前四史"
echo "  🤖 Gemini AI: tianquan-haystack/"
echo ""
echo "🚀 启动命令: cd shushu && streamlit run app.py"
