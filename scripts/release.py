#!/usr/bin/env python3
"""
六行星知识图谱发布脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json
from datetime import datetime

def run_command(cmd, cwd=None):
    """运行命令"""
    print(f"🔧 执行: {cmd}")
    result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 命令失败: {result.stderr}")
        return False
    print(f"✅ 命令成功")
    return True

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8+")
        return False
    
    # 检查必要文件
    required_files = [
        "main.py",
        "requirements.txt",
        "setup.py",
        "README.md",
        "src/__init__.py"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    print("✅ 依赖检查通过")
    return True

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")

    # 简单的导入测试
    try:
        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        sys.path.insert(0, str(project_root / "src"))

        # 测试核心模块导入
        import src.core.neo4j_shiji_processor
        import src.analyzers.historical_dark_matter_ai_predictor
        print("✅ 模块导入测试通过")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

    return True

def create_distribution():
    """创建分发包"""
    print("📦 创建分发包...")

    # 清理旧的构建文件
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)

    # 创建dist目录
    Path("dist").mkdir(exist_ok=True)

    # 创建简单的tar.gz包
    print("🔧 创建源码包...")
    if not run_command("tar -czf dist/sixstars-knowledge-graph-0.1.0.tar.gz --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' --exclude='.DS_Store' ."):
        print("⚠️ tar命令失败，跳过源码包创建")

    print("✅ 分发包创建成功")
    return True

def create_release_info():
    """创建发布信息"""
    print("📋 创建发布信息...")
    
    release_info = {
        "version": "0.1.0",
        "release_date": datetime.now().isoformat(),
        "description": "六行星知识图谱首个Alpha版本",
        "features": [
            "Neo4j知识图谱核心处理器",
            "历史暗物质AI预测系统", 
            "十二龙子心易射覆系统",
            "Flask Web界面",
            "前四史完整数据支持",
            "512条天象记录分析",
            "200+本术数典籍集成"
        ],
        "requirements": {
            "python": ">=3.8",
            "neo4j": ">=5.0",
            "memory": "8GB+ (推荐)"
        },
        "data_stats": {
            "nodes": 1486,
            "relationships": 516,
            "historical_records": 512,
            "text_data_size": "18MB"
        }
    }
    
    with open("RELEASE_INFO.json", "w", encoding="utf-8") as f:
        json.dump(release_info, f, ensure_ascii=False, indent=2)
    
    print("✅ 发布信息创建成功")
    return True

def main():
    """主函数"""
    print("🌟 六行星知识图谱 v0.1.0 发布流程")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 运行测试
    if not run_tests():
        sys.exit(1)
    
    # 创建分发包
    if not create_distribution():
        sys.exit(1)
    
    # 创建发布信息
    if not create_release_info():
        sys.exit(1)
    
    print("\n🎉 发布准备完成！")
    print("\n📦 发布包位置:")
    print("  - dist/sixstars-knowledge-graph-0.1.0.tar.gz")
    print("  - dist/sixstars_knowledge_graph-0.1.0-py3-none-any.whl")
    print("\n📋 发布信息:")
    print("  - RELEASE_INFO.json")
    print("\n🚀 下一步:")
    print("  1. 测试安装: pip install dist/sixstars_knowledge_graph-0.1.0-py3-none-any.whl")
    print("  2. 创建Git标签: git tag v0.1.0")
    print("  3. 推送到仓库: git push origin v0.1.0")
    print("  4. 创建GitHub Release")

if __name__ == "__main__":
    main()
