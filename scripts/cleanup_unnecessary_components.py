#!/usr/bin/env python3
"""
清理六行星项目中不必要的组件
专注于 Streamlit + Neo4j + MCP 的核心架构
"""

import os
import shutil
from pathlib import Path

def analyze_current_structure():
    """分析当前项目结构"""
    print("🔍 分析当前项目结构...")
    
    # 核心组件 (需要保留)
    core_components = {
        'shushu/': 'Streamlit排盘应用 - 核心应用',
        'src/analyzers/': '数据分析器 - 处理Neo4j数据',
        'src/processors/': '数据处理器 - 处理古籍数据', 
        'shushubook/': '200+本术数典籍 - 核心数据',
        'cleaned_shishu/': '前四史数据 - 核心数据',
        'tianquan-haystack/': 'Gemini+Haystack集成 - 新功能'
    }
    
    # 可能不需要的组件
    questionable_components = {
        'src/web/': 'Flask Web应用 - 可能不需要',
        'templates/': 'HTML模板 - 可能不需要',
        'static/': '静态文件 - 可能不需要',
        'scripts/rovodev/': 'Doppler迁移脚本 - 过度复杂'
    }
    
    print("\n✅ 核心组件 (保留):")
    for path, desc in core_components.items():
        if os.path.exists(path):
            print(f"  📁 {path} - {desc}")
        else:
            print(f"  ❌ {path} - {desc} (不存在)")
    
    print("\n❓ 可疑组件 (可能删除):")
    for path, desc in questionable_components.items():
        if os.path.exists(path):
            print(f"  📁 {path} - {desc}")
            if path == 'src/web/':
                # 检查Flask应用的使用情况
                check_flask_usage(path)
        else:
            print(f"  ✅ {path} - {desc} (已不存在)")

def check_flask_usage(web_dir):
    """检查Flask应用的实际使用情况"""
    print(f"    🔍 检查Flask使用情况:")
    
    app_py = Path(web_dir) / 'app.py'
    if app_py.exists():
        with open(app_py, 'r') as f:
            content = f.read()
            
        # 检查路由数量
        routes = content.count('@app.route')
        print(f"      📊 定义的路由数: {routes}")
        
        # 检查是否有实际业务逻辑
        if 'render_template' in content:
            print(f"      📄 使用HTML模板")
        if 'jsonify' in content:
            print(f"      🔌 提供JSON API")
        
        # 检查模板文件
        templates_dir = Path('templates')
        if templates_dir.exists():
            template_files = list(templates_dir.glob('*.html'))
            print(f"      📋 模板文件数: {len(template_files)}")
            for template in template_files:
                print(f"        - {template.name}")

def check_streamlit_completeness():
    """检查Streamlit应用的完整性"""
    print("\n🎯 检查Streamlit应用完整性...")
    
    streamlit_app = Path('shushu/app.py')
    if not streamlit_app.exists():
        print("  ❌ Streamlit应用不存在!")
        return False
    
    with open(streamlit_app, 'r') as f:
        content = f.read()
    
    # 检查核心功能
    features = {
        '六壬': 'liuren' in content.lower(),
        '奇门': 'qimen' in content.lower(), 
        '太乙': 'taiyi' in content.lower(),
        'Neo4j': 'neo4j' in content.lower(),
        'Streamlit': 'streamlit' in content.lower()
    }
    
    print("  📊 Streamlit应用功能:")
    for feature, exists in features.items():
        status = "✅" if exists else "❌"
        print(f"    {status} {feature}")
    
    return all(features.values())

def suggest_cleanup_plan():
    """建议清理方案"""
    print("\n💡 建议的清理方案:")
    
    print("\n1. 🗑️ 可以安全删除的组件:")
    deletable = [
        ('src/web/', 'Flask Web应用 - Streamlit已足够'),
        ('templates/', 'HTML模板 - Streamlit不需要'),
        ('scripts/rovodev/', 'Doppler迁移 - 过度复杂'),
    ]
    
    for path, reason in deletable:
        if os.path.exists(path):
            print(f"  📁 {path} - {reason}")
    
    print("\n2. ✅ 保留的核心架构:")
    print("  📱 shushu/ - Streamlit排盘应用")
    print("  🗄️ Neo4j - 知识图谱数据库") 
    print("  📚 shushubook/ - 术数典籍")
    print("  📖 cleaned_shishu/ - 史书数据")
    print("  🤖 tianquan-haystack/ - Gemini AI集成")
    
    print("\n3. 🎯 简化后的启动方式:")
    print("  cd shushu && streamlit run app.py")

def create_cleanup_script():
    """创建清理脚本"""
    print("\n🛠️ 创建清理脚本...")
    
    cleanup_script = '''#!/bin/bash
# 六行星项目架构清理脚本
# 删除不必要的Flask组件，专注于Streamlit + Neo4j

set -e

echo "🧹 开始清理六行星项目架构..."

# 备份重要文件
echo "📦 创建备份..."
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"

# 备份可能有用的配置
if [ -d "src/web" ]; then
    cp -r src/web "$BACKUP_DIR/"
    echo "  ✅ 已备份 src/web/"
fi

if [ -d "templates" ]; then
    cp -r templates "$BACKUP_DIR/"
    echo "  ✅ 已备份 templates/"
fi

# 删除不需要的组件
echo ""
echo "🗑️ 删除不必要的组件..."

if [ -d "src/web" ]; then
    rm -rf src/web
    echo "  ✅ 删除 src/web/ (Flask应用)"
fi

if [ -d "templates" ]; then
    rm -rf templates  
    echo "  ✅ 删除 templates/ (HTML模板)"
fi

if [ -d "scripts/rovodev" ]; then
    rm -rf scripts/rovodev
    echo "  ✅ 删除 scripts/rovodev/ (复杂的迁移脚本)"
fi

# 清理环境变量文件
echo ""
echo "🧽 清理环境变量..."
if [ -f ".env" ]; then
    # 只保留必要的环境变量
    cat > .env.new << EOF
# 六行星项目核心配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=sixstars123

# Gemini API (可选)
GEMINI_API_KEY=AIzaSyCalfmTapVVTYWVAPm923sz7UtitZbmsaM
EOF
    
    mv .env.new .env
    echo "  ✅ 简化 .env 文件"
fi

echo ""
echo "🎯 清理完成! 项目架构已简化为:"
echo "  📱 Streamlit应用: cd shushu && streamlit run app.py"
echo "  🗄️ Neo4j数据库: 200+术数书 + 前四史"
echo "  🤖 Gemini AI: tianquan-haystack/"
echo ""
echo "🚀 启动命令: cd shushu && streamlit run app.py"
'''
    
    script_path = Path('scripts/cleanup_architecture.sh')
    with open(script_path, 'w') as f:
        f.write(cleanup_script)
    
    os.chmod(script_path, 0o755)
    print(f"  ✅ 创建清理脚本: {script_path}")

def main():
    """主函数"""
    print("🧹 六行星项目架构清理工具")
    print("=" * 50)
    
    # 分析当前结构
    analyze_current_structure()
    
    # 检查Streamlit完整性
    streamlit_ok = check_streamlit_completeness()
    
    # 建议清理方案
    suggest_cleanup_plan()
    
    # 创建清理脚本
    create_cleanup_script()
    
    print("\n" + "=" * 50)
    print("🎯 总结:")
    print("你的项目确实应该是 Streamlit + Neo4j + MCP 的简单架构。")
    print("Flask组件是不必要的复杂性。")
    print("")
    print("🚀 建议执行:")
    print("  ./scripts/cleanup_architecture.sh")
    print("  cd shushu && streamlit run app.py")

if __name__ == "__main__":
    main()
