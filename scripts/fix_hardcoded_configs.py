#!/usr/bin/env python3
"""
修复项目中硬编码的配置
将硬编码的Neo4j连接信息改为使用环境变量
"""

import os
import re
from pathlib import Path

def find_hardcoded_configs():
    """查找硬编码的配置"""
    print("🔍 查找硬编码的配置...")
    
    hardcoded_patterns = [
        (r'uri=os.getenv("NEO4J_URI", "bolt://localhost:7687")', 'NEO4J_URI'),
        (r'user=os.getenv("NEO4J_USER", "neo4j")', 'NEO4J_USER'),
        (r'password=os.getenv("NEO4J_PASSWORD", "sixstars123")', 'NEO4J_PASSWORD'),
        (r"uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687')", 'NEO4J_URI'),
        (r"user=os.getenv('NEO4J_USER', 'neo4j')", 'NEO4J_USER'),
        (r"password=os.getenv('NEO4J_PASSWORD', 'sixstars123')", 'NEO4J_PASSWORD'),
    ]
    
    python_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', '.venv']]
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    found_files = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            has_hardcoded = False
            for pattern, env_var in hardcoded_patterns:
                if re.search(pattern, content):
                    has_hardcoded = True
                    break
            
            if has_hardcoded:
                found_files.append(file_path)
                print(f"  📄 {file_path}")
        
        except Exception as e:
            print(f"  ❌ 读取文件失败 {file_path}: {e}")
    
    return found_files

def fix_file(file_path):
    """修复单个文件中的硬编码配置"""
    print(f"🔧 修复文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 添加必要的导入
        if 'import os' not in content and 'from os import' not in content:
            # 在第一个import之后添加os导入
            import_match = re.search(r'^(import .+|from .+ import .+)$', content, re.MULTILINE)
            if import_match:
                insert_pos = import_match.end()
                content = content[:insert_pos] + '\nimport os' + content[insert_pos:]
            else:
                # 如果没有找到import，在文件开头添加
                content = 'import os\n' + content
        
        # 替换硬编码的配置
        replacements = [
            # Neo4j URI
            (r'uri=os.getenv("NEO4J_URI", "bolt://localhost:7687")', 'uri=os.getenv("NEO4J_URI", "bolt://localhost:7687")'),
            (r"uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687')", "uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687')"),
            
            # Neo4j User
            (r'user=os.getenv("NEO4J_USER", "neo4j")', 'user=os.getenv("NEO4J_USER", "neo4j")'),
            (r"user=os.getenv('NEO4J_USER', 'neo4j')", "user=os.getenv('NEO4J_USER', 'neo4j')"),
            
            # Neo4j Password
            (r'password=os.getenv("NEO4J_PASSWORD", "sixstars123")', 'password=os.getenv("NEO4J_PASSWORD", "sixstars123")'),
            (r"password=os.getenv('NEO4J_PASSWORD', 'sixstars123')", "password=os.getenv('NEO4J_PASSWORD', 'sixstars123')"),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 已更新")
            return True
        else:
            print(f"  ⏭️ 无需更新")
            return False
    
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def create_env_loader():
    """创建环境变量加载器"""
    print("📝 创建环境变量加载器...")
    
    loader_content = '''#!/usr/bin/env python3
"""
环境变量加载器
确保项目中的环境变量正确加载
"""

import os
from pathlib import Path
from dotenv import load_dotenv

def load_project_env():
    """加载项目环境变量"""
    # 查找.env文件
    env_file = Path('.env')
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量: {env_file}")
    else:
        print(f"⚠️  .env文件不存在: {env_file}")
    
    # 验证关键环境变量
    required_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 所有必需的环境变量都已设置")
    return True

def get_neo4j_config():
    """获取Neo4j配置"""
    return {
        'uri': os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
        'user': os.getenv('NEO4J_USER', 'neo4j'),
        'password': os.getenv('NEO4J_PASSWORD', 'sixstars123')
    }

def get_api_config():
    """获取API配置"""
    return {
        'gemini_api_key': os.getenv('GEMINI_API_KEY'),
        'openai_api_key': os.getenv('OPENAI_API_KEY'),
        'anthropic_api_key': os.getenv('ANTHROPIC_API_KEY')
    }

if __name__ == "__main__":
    load_project_env()
'''
    
    loader_path = Path('src/utils/env_loader.py')
    loader_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(loader_path, 'w') as f:
        f.write(loader_content)
    
    print(f"  ✅ 创建环境变量加载器: {loader_path}")

def main():
    """主函数"""
    print("🔧 六行星项目配置修复工具")
    print("=" * 50)
    
    # 查找硬编码配置
    hardcoded_files = find_hardcoded_configs()
    
    if not hardcoded_files:
        print("✅ 没有发现硬编码的配置")
        return
    
    print(f"\n📊 发现 {len(hardcoded_files)} 个文件包含硬编码配置")
    
    # 修复文件
    print("\n🔧 开始修复...")
    fixed_count = 0
    
    for file_path in hardcoded_files:
        if fix_file(file_path):
            fixed_count += 1
    
    print(f"\n📊 修复完成: {fixed_count}/{len(hardcoded_files)} 个文件已更新")
    
    # 创建环境变量加载器
    create_env_loader()
    
    print("\n" + "=" * 50)
    print("🎯 修复总结:")
    print("1. ✅ 更新了.env文件，包含完整的配置")
    print("2. 🔧 修复了硬编码的Neo4j连接")
    print("3. 📝 创建了环境变量加载器")
    print("")
    print("🚀 现在你的项目有完整的环境变量配置了！")
    print("💡 建议: 在需要的地方导入并使用 src.utils.env_loader")

if __name__ == "__main__":
    main()
