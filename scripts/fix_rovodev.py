#!/usr/bin/env python3
"""
修复rovodev脚本问题
分析并修复Doppler迁移计划的问题
"""

import os
import sys
from pathlib import Path
import re

def analyze_current_env_usage():
    """分析当前项目中环境变量的使用情况"""
    print("🔍 分析当前环境变量使用情况...")
    
    # 扫描所有Python文件
    python_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', '.venv']]
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    env_vars_found = set()
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 查找 os.environ.get() 和 os.getenv() 调用
                patterns = [
                    r'os\.environ\.get\(["\']([^"\']+)["\']',
                    r'os\.getenv\(["\']([^"\']+)["\']',
                    r'environ\.get\(["\']([^"\']+)["\']',
                    r'environ\[["\']([^"\']+)["\']\]'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        env_vars_found.add(match)
                        print(f"  📄 {file_path}: {match}")
        
        except Exception as e:
            print(f"  ❌ 读取文件失败 {file_path}: {e}")
    
    print(f"\n📊 发现的环境变量: {len(env_vars_found)}")
    for var in sorted(env_vars_found):
        print(f"  - {var}")
    
    return env_vars_found

def check_env_files():
    """检查环境变量文件"""
    print("\n📁 检查环境变量文件...")
    
    env_files = ['.env', '.env.example', '.env.local', '.env.development']
    
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"  ✅ 找到: {env_file}")
            with open(env_file, 'r') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:10], 1):  # 只显示前10行
                    if line.strip() and not line.startswith('#'):
                        print(f"    {i}: {line.strip()}")
                if len(lines) > 10:
                    print(f"    ... 还有 {len(lines) - 10} 行")
        else:
            print(f"  ❌ 未找到: {env_file}")

def analyze_rovodev_scripts():
    """分析rovodev脚本"""
    print("\n🔧 分析rovodev脚本...")
    
    rovodev_dir = Path('scripts/rovodev')
    if not rovodev_dir.exists():
        print("  ❌ scripts/rovodev 目录不存在")
        return
    
    print(f"  📁 rovodev目录内容:")
    for item in rovodev_dir.iterdir():
        if item.is_file():
            print(f"    📄 {item.name}")
            if item.name.endswith('.sh'):
                # 检查脚本是否可执行
                if os.access(item, os.X_OK):
                    print(f"      ✅ 可执行")
                else:
                    print(f"      ❌ 不可执行 - 需要: chmod +x {item}")
        else:
            print(f"    📁 {item.name}/")

def create_simple_migration_script():
    """创建简化的迁移脚本"""
    print("\n🛠️ 创建简化的迁移脚本...")
    
    script_content = '''#!/bin/bash
# 简化的环境变量迁移脚本
# 修复rovodev问题

set -e

echo "🚀 开始六行星项目环境变量迁移..."

# 检查当前.env文件
if [ ! -f ".env" ]; then
    echo "📝 创建.env文件..."
    cp .env.example .env
    echo "✅ 已从.env.example创建.env文件"
    echo "⚠️  请编辑.env文件，填入实际配置"
else
    echo "✅ .env文件已存在"
fi

# 显示当前环境变量
echo ""
echo "📊 当前环境变量:"
if [ -f ".env" ]; then
    grep -v "^#" .env | grep -v "^$" | head -10
else
    echo "  无.env文件"
fi

echo ""
echo "🎯 建议的操作:"
echo "1. 检查.env文件是否包含所需的环境变量"
echo "2. 确保Neo4j密码正确: sixstars123"
echo "3. 如需Doppler迁移，请先完成代码更新"
echo ""
echo "✅ 迁移检查完成"
'''
    
    script_path = Path('scripts/simple_migration.sh')
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 设置可执行权限
    os.chmod(script_path, 0o755)
    
    print(f"  ✅ 创建脚本: {script_path}")
    print(f"  🚀 运行方式: ./scripts/simple_migration.sh")

def suggest_fixes():
    """建议修复方案"""
    print("\n💡 修复建议:")
    print("1. **立即修复**: 使用简化迁移脚本")
    print("   ./scripts/simple_migration.sh")
    print("")
    print("2. **如果要使用Doppler**:")
    print("   a. 先更新代码中的环境变量引用")
    print("   b. 运行: python scripts/rovodev/code_migration.py")
    print("   c. 再运行: ./scripts/rovodev/doppler_migration.sh")
    print("")
    print("3. **推荐方案**: 暂时不迁移到Doppler")
    print("   - 当前项目规模较小，.env文件足够")
    print("   - 专注于核心功能开发")
    print("   - 等项目成熟后再考虑Doppler")

def main():
    """主函数"""
    print("🔧 六行星项目 - rovodev问题诊断工具")
    print("=" * 50)
    
    # 分析当前状态
    env_vars = analyze_current_env_usage()
    check_env_files()
    analyze_rovodev_scripts()
    
    # 创建简化脚本
    create_simple_migration_script()
    
    # 给出建议
    suggest_fixes()
    
    print("\n" + "=" * 50)
    print("🎯 总结:")
    print("rovodev脚本的问题是过度复杂化了环境变量管理。")
    print("建议先使用简化方案，专注于核心功能开发。")

if __name__ == "__main__":
    main()
