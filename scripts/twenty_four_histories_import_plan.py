#!/usr/bin/env python3
"""
二十四史数据导入方案
多数据库架构设计和实施计划
"""

import os
import json
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Any

@dataclass
class DatabaseConfig:
    """数据库配置"""
    name: str
    purpose: str
    priority: str
    storage_content: List[str]
    advantages: List[str]

class TwentyFourHistoriesImportPlan:
    """二十四史导入计划"""
    
    def __init__(self):
        self.databases = self._define_database_architecture()
        self.histories = self._list_twenty_four_histories()
    
    def _define_database_architecture(self) -> Dict[str, DatabaseConfig]:
        """定义数据库架构"""
        return {
            "neo4j": DatabaseConfig(
                name="Neo4j",
                purpose="关系图谱和知识网络",
                priority="必须 (主库)",
                storage_content=[
                    "人物节点 (姓名、字号、籍贯、生卒年、官职)",
                    "事件节点 (时间、地点、性质、影响)",
                    "地理节点 (州郡、城市、山川、边界)",
                    "关系边 (血缘、政治、军事、师友、敌对)",
                    "时间关系 (前后、同时、因果)",
                    "空间关系 (地理位置、行政隶属)"
                ],
                advantages=[
                    "你已有前四史基础，可直接扩展",
                    "擅长复杂关系查询和图算法",
                    "支持路径分析和社区发现",
                    "与Gemini AI集成效果好",
                    "适合术数中的人物关系分析"
                ]
            ),
            
            "mongodb": DatabaseConfig(
                name="MongoDB",
                purpose="文档存储和全文搜索",
                priority="强烈推荐 (辅库)",
                storage_content=[
                    "原文文档 (按卷、章、节、段分层存储)",
                    "白话文对照 (与原文段落对应)",
                    "注释和考证 (历代学者注解)",
                    "元数据 (朝代、作者、版本、来源)",
                    "索引信息 (人名、地名、官名、年号)",
                    "交叉引用 (史书间的互文关系)"
                ],
                advantages=[
                    "灵活的文档结构，适合古籍特点",
                    "强大的全文搜索能力",
                    "支持复杂的嵌套查询",
                    "易于扩展和修改",
                    "与现代应用集成方便"
                ]
            ),
            
            "postgresql": DatabaseConfig(
                name="PostgreSQL",
                purpose="结构化分析和统计",
                priority="可选 (分析库)",
                storage_content=[
                    "人物统计表 (按朝代、籍贯、官职统计)",
                    "事件时间线 (标准化的时间序列)",
                    "地理信息 (坐标、行政区划变迁)",
                    "统计分析结果 (人物网络指标)",
                    "数据质量报告 (完整性、一致性)",
                    "分析缓存 (复杂查询结果)"
                ],
                advantages=[
                    "强大的SQL分析能力",
                    "支持复杂的统计查询",
                    "GIS扩展支持地理分析",
                    "与BI工具集成良好",
                    "数据一致性保证"
                ]
            )
        }
    
    def _list_twenty_four_histories(self) -> List[Dict[str, Any]]:
        """列出二十四史"""
        return [
            {"name": "史记", "dynasty": "西汉", "author": "司马迁", "period": "先秦-西汉", "status": "已有"},
            {"name": "汉书", "dynasty": "东汉", "author": "班固", "period": "西汉", "status": "已有"},
            {"name": "后汉书", "dynasty": "南朝宋", "author": "范晔", "period": "东汉", "status": "已有"},
            {"name": "三国志", "dynasty": "西晋", "author": "陈寿", "period": "三国", "status": "已有"},
            {"name": "晋书", "dynasty": "唐", "author": "房玄龄等", "period": "西晋东晋", "status": "待导入"},
            {"name": "宋书", "dynasty": "南朝梁", "author": "沈约", "period": "南朝宋", "status": "待导入"},
            {"name": "南齐书", "dynasty": "南朝梁", "author": "萧子显", "period": "南朝齐", "status": "待导入"},
            {"name": "梁书", "dynasty": "唐", "author": "姚思廉", "period": "南朝梁", "status": "待导入"},
            {"name": "陈书", "dynasty": "唐", "author": "姚思廉", "period": "南朝陈", "status": "待导入"},
            {"name": "魏书", "dynasty": "北齐", "author": "魏收", "period": "北魏", "status": "待导入"},
            {"name": "北齐书", "dynasty": "唐", "author": "李百药", "period": "北齐", "status": "待导入"},
            {"name": "周书", "dynasty": "唐", "author": "令狐德棻等", "period": "北周", "status": "待导入"},
            {"name": "隋书", "dynasty": "唐", "author": "魏征等", "period": "隋", "status": "待导入"},
            {"name": "南史", "dynasty": "唐", "author": "李延寿", "period": "南朝", "status": "待导入"},
            {"name": "北史", "dynasty": "唐", "author": "李延寿", "period": "北朝", "status": "待导入"},
            {"name": "旧唐书", "dynasty": "后晋", "author": "刘昫等", "period": "唐", "status": "待导入"},
            {"name": "新唐书", "dynasty": "北宋", "author": "欧阳修等", "period": "唐", "status": "待导入"},
            {"name": "旧五代史", "dynasty": "北宋", "author": "薛居正等", "period": "五代", "status": "待导入"},
            {"name": "新五代史", "dynasty": "北宋", "author": "欧阳修", "period": "五代", "status": "待导入"},
            {"name": "宋史", "dynasty": "元", "author": "脱脱等", "period": "北宋南宋", "status": "待导入"},
            {"name": "辽史", "dynasty": "元", "author": "脱脱等", "period": "辽", "status": "待导入"},
            {"name": "金史", "dynasty": "元", "author": "脱脱等", "period": "金", "status": "待导入"},
            {"name": "元史", "dynasty": "明", "author": "宋濂等", "period": "元", "status": "待导入"},
            {"name": "明史", "dynasty": "清", "author": "张廷玉等", "period": "明", "status": "待导入"}
        ]
    
    def analyze_data_characteristics(self):
        """分析数据特点"""
        print("📊 二十四史数据特点分析")
        print("=" * 50)
        
        total_histories = len(self.histories)
        existing_histories = len([h for h in self.histories if h["status"] == "已有"])
        pending_histories = total_histories - existing_histories
        
        print(f"📚 史书总数: {total_histories}")
        print(f"✅ 已导入: {existing_histories} (前四史)")
        print(f"⏳ 待导入: {pending_histories}")
        print()
        
        print("📈 数据规模估算:")
        print("  - 总字数: 约4000万字")
        print("  - 人物数量: 约10万+")
        print("  - 事件数量: 约50万+")
        print("  - 关系数量: 约100万+")
        print("  - 时间跨度: 2000+年")
        print()
        
        print("🎯 数据特点:")
        characteristics = [
            "结构化文本 (卷、章、传、表、志)",
            "复杂人物关系网络",
            "时间序列数据",
            "地理信息丰富",
            "史书间互文引用",
            "原文+白话文对照",
            "多版本和注释"
        ]
        
        for char in characteristics:
            print(f"  • {char}")
    
    def recommend_database_architecture(self):
        """推荐数据库架构"""
        print("\n🏗️ 推荐数据库架构")
        print("=" * 50)
        
        for db_key, db_config in self.databases.items():
            print(f"\n{db_config.priority} - {db_config.name}")
            print(f"用途: {db_config.purpose}")
            print("存储内容:")
            for content in db_config.storage_content:
                print(f"  • {content}")
            print("优势:")
            for advantage in db_config.advantages:
                print(f"  ✅ {advantage}")
    
    def create_implementation_phases(self):
        """创建实施阶段"""
        print("\n🚀 实施阶段规划")
        print("=" * 50)
        
        phases = [
            {
                "phase": "第一阶段 (立即开始)",
                "duration": "1-2周",
                "tasks": [
                    "设置MongoDB环境",
                    "设计文档Schema",
                    "导入晋书、宋书等南北朝史书",
                    "建立基础的全文搜索索引"
                ],
                "goal": "建立文档存储基础"
            },
            {
                "phase": "第二阶段 (2-4周)",
                "duration": "2-3周", 
                "tasks": [
                    "扩展Neo4j图谱到南北朝",
                    "建立人物关系网络",
                    "实现MongoDB与Neo4j的数据同步",
                    "开发基础的查询API"
                ],
                "goal": "完善关系图谱"
            },
            {
                "phase": "第三阶段 (1-2个月)",
                "duration": "3-4周",
                "tasks": [
                    "导入唐宋元明史书",
                    "建立完整的时间线",
                    "实现跨史书的关联查询",
                    "集成Gemini AI进行智能问答"
                ],
                "goal": "完成主要史书导入"
            },
            {
                "phase": "第四阶段 (可选)",
                "duration": "2-3周",
                "tasks": [
                    "设置PostgreSQL分析库",
                    "实现复杂统计分析",
                    "建立数据可视化",
                    "性能优化和缓存"
                ],
                "goal": "增强分析能力"
            }
        ]
        
        for phase in phases:
            print(f"\n📅 {phase['phase']}")
            print(f"⏱️ 预计时间: {phase['duration']}")
            print(f"🎯 目标: {phase['goal']}")
            print("任务:")
            for task in phase['tasks']:
                print(f"  • {task}")
    
    def estimate_resources(self):
        """资源需求估算"""
        print("\n💾 资源需求估算")
        print("=" * 50)
        
        resources = {
            "存储空间": {
                "MongoDB": "50-100GB (原文+白话文+索引)",
                "Neo4j": "20-50GB (图谱数据+索引)",
                "PostgreSQL": "10-20GB (统计数据+缓存)"
            },
            "内存需求": {
                "MongoDB": "8-16GB (推荐16GB)",
                "Neo4j": "8-16GB (推荐16GB)", 
                "PostgreSQL": "4-8GB (可选)"
            },
            "开发时间": {
                "数据清洗和预处理": "2-3周",
                "数据库设计和导入": "3-4周",
                "API开发和集成": "2-3周",
                "测试和优化": "1-2周"
            }
        }
        
        for category, items in resources.items():
            print(f"\n{category}:")
            for item, requirement in items.items():
                print(f"  • {item}: {requirement}")

def main():
    """主函数"""
    plan = TwentyFourHistoriesImportPlan()
    
    print("🏛️ 二十四史数据导入方案")
    print("=" * 60)
    
    # 分析数据特点
    plan.analyze_data_characteristics()
    
    # 推荐架构
    plan.recommend_database_architecture()
    
    # 实施阶段
    plan.create_implementation_phases()
    
    # 资源估算
    plan.estimate_resources()
    
    print("\n" + "=" * 60)
    print("🎯 总结建议:")
    print("1. 🔥 立即开始: Neo4j + MongoDB 双库架构")
    print("2. 📚 优先导入: 南北朝史书 (与现有前四史衔接)")
    print("3. 🤖 AI集成: 基于真实历史数据的Gemini对话")
    print("4. 📊 可选扩展: PostgreSQL用于复杂分析")
    print("5. 🚀 预期效果: 世界级的中国历史知识图谱!")

if __name__ == "__main__":
    main()
