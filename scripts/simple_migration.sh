#!/bin/bash
# 简化的环境变量迁移脚本
# 修复rovodev问题

set -e

echo "🚀 开始六行星项目环境变量迁移..."

# 检查当前.env文件
if [ ! -f ".env" ]; then
    echo "📝 创建.env文件..."
    cp .env.example .env
    echo "✅ 已从.env.example创建.env文件"
    echo "⚠️  请编辑.env文件，填入实际配置"
else
    echo "✅ .env文件已存在"
fi

# 显示当前环境变量
echo ""
echo "📊 当前环境变量:"
if [ -f ".env" ]; then
    grep -v "^#" .env | grep -v "^$" | head -10
else
    echo "  无.env文件"
fi

echo ""
echo "🎯 建议的操作:"
echo "1. 检查.env文件是否包含所需的环境变量"
echo "2. 确保Neo4j密码正确: sixstars123"
echo "3. 如需Doppler迁移，请先完成代码更新"
echo ""
echo "✅ 迁移检查完成"
