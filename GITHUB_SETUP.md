# GitHub设置和Hugging Face部署指南

## 🎯 快速设置步骤

### 1. 创建GitHub仓库

```bash
# 在GitHub上创建新仓库
# 仓库名: 5-planets
# 描述: 六行星知识图谱 - 中华传统术数数字化平台
# 设为Public (这样Hugging Face可以访问)
```

### 2. 推送代码到GitHub

```bash
# 设置正确的远程仓库地址 (替换your-username)
git remote remove origin  # 如果已存在
git remote add origin https://github.com/your-username/5-planets.git

# 推送代码
git push -u origin main
```

### 3. 设置Hugging Face

#### 创建Hugging Face账号
- 访问: https://huggingface.co/join
- 注册账号

#### 创建API Token
- 访问: https://huggingface.co/settings/tokens
- 点击"New token"
- 名称: `5-planets-deployment`
- 权限: `Write`
- 复制生成的token (格式: `hf_xxxxxxxxxx`)

#### 创建两个Spaces
1. **Streamlit排盘应用**
   - 名称: `sixstars-streamlit`
   - SDK: `Streamlit`
   - 可见性: `Public`

2. **Gemini AI演示**
   - 名称: `sixstars-demo`
   - SDK: `Streamlit`
   - 可见性: `Public`

### 4. 配置GitHub Secrets

在GitHub仓库设置中添加Secrets:

1. 进入仓库页面
2. 点击 `Settings` → `Secrets and variables` → `Actions`
3. 点击 `New repository secret`
4. 添加以下Secrets:

```
HF_TOKEN = hf_xxxxxxxxxx (你的Hugging Face Token)
HF_SPACE_NAME = your-username/sixstars-streamlit
HF_DEMO_SPACE_NAME = your-username/sixstars-demo
GEMINI_API_KEY = AIzaSyCalfmTapVVTYWVAPm923sz7UtitZbmsaM (可选)
```

### 5. 触发自动部署

```bash
# 方法1: 推送代码触发
git add .
git commit -m "Trigger HF deployment"
git push origin main

# 方法2: 手动触发
# 在GitHub仓库页面 → Actions → Deploy to Hugging Face Spaces → Run workflow
```

## 🔍 验证部署

### 检查GitHub Actions
1. 在GitHub仓库页面点击"Actions"
2. 查看"Deploy to Hugging Face Spaces"工作流
3. 确认所有步骤都成功执行

### 访问部署的应用
- **Streamlit排盘**: `https://huggingface.co/spaces/your-username/sixstars-streamlit`
- **Gemini演示**: `https://huggingface.co/spaces/your-username/sixstars-demo`

## 🛠️ 自定义配置

### 修改应用信息

编辑 `.github/workflows/deploy-to-huggingface.yml`:

```yaml
# 修改Streamlit应用标题
title: 你的应用标题
emoji: 🏛️
colorFrom: gold
colorTo: red

# 修改演示应用标题  
title: 你的演示标题
emoji: 🤖
colorFrom: blue
colorTo: purple
```

### 添加更多环境变量

在Hugging Face Spaces设置中:
1. 进入Space页面
2. 点击"Settings"
3. 在"Repository secrets"中添加环境变量

## 📊 项目结构说明

```
5-planets/
├── .github/workflows/          # GitHub Actions配置
│   ├── deploy-to-huggingface.yml  # 自动部署工作流
│   └── README.md              # 工作流说明
├── shushu/                    # Streamlit排盘应用
├── tianquan-haystack/         # Gemini AI集成
├── nuc12-setup/              # NUC12服务器配置
├── README.md                 # 项目主文档
├── DEPLOYMENT.md             # 部署详细指南
└── GITHUB_SETUP.md           # 本文件
```

## 🎉 成功标志

部署成功后你将拥有:

✅ **GitHub仓库**: 代码版本管理和协作  
✅ **自动化CI/CD**: 代码推送自动部署  
✅ **在线排盘系统**: 全球可访问的术数排盘  
✅ **AI古代大师**: 基于真实历史数据的智能对话  
✅ **NUC12服务器**: 本地高性能知识图谱服务  

## 🔗 相关链接

- [GitHub仓库模板](https://github.com/your-username/5-planets)
- [Hugging Face Spaces文档](https://huggingface.co/docs/hub/spaces)
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [Streamlit部署指南](https://docs.streamlit.io/streamlit-community-cloud/deploy-your-app)

---

**准备好让你的六行星知识图谱项目走向世界了吗？** 🌍✨
