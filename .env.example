# 六行星知识图谱项目环境配置示例
# 复制此文件为.env并填入实际配置

# Neo4j数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=sixstars123

# Web服务配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here

# 数据文件路径
DATA_DIR=./data
SHUSHU_DIR=./shushubook
CLEANED_DATA_DIR=./cleaned_shishu

# API配置
API_VERSION=v1
API_BASE_URL=http://localhost:5000/api

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/sixstars.log
