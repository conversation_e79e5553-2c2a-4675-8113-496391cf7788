import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider

def mandelbrot(c, max_iter):
    z = 0
    for n in range(max_iter):
        z = z**2 + c
        if abs(z) > 2:
            return n  # 发散，返回迭代次数
    return max_iter  # 不发散，属于集合

# 生成复平面上的点（x轴为实部，y轴为虚部）
x_min, x_max = -2.0, 1.0
y_min, y_max = -1.5, 1.5
width, height = 600, 600  # 适当减小尺寸提高交互速度

x = np.linspace(x_min, x_max, width)
y = np.linspace(y_min, y_max, height)
X, Y = np.meshgrid(x, y)
c = X + 1j * Y  # 复平面上的点

# 创建图形和轴
fig, ax = plt.subplots(figsize=(10, 8))
plt.subplots_adjust(left=0.1, bottom=0.25)  # 为滑块留出空间

# 初始参数
initial_max_iter = 100

# 计算并绘制初始图像
mandelbrot_set = np.vectorize(mandelbrot)(c, initial_max_iter)
im = ax.imshow(mandelbrot_set, cmap='inferno', extent=[x_min, x_max, y_min, y_max], origin='lower')
fig.colorbar(im, ax=ax, label='迭代次数')
ax.set_title(f'曼德勃罗集合 (迭代次数: {initial_max_iter})')

# 添加滑块控件
ax_slider = plt.axes([0.1, 0.1, 0.8, 0.03])
slider = Slider(
    ax=ax_slider,
    label='迭代次数',
    valmin=10,
    valmax=2000,
    valinit=initial_max_iter,
    valstep=10
)

# 滑块更新函数
def update(val):
    max_iter = int(slider.val)
    # 重新计算曼德勃罗集合
    mandelbrot_set = np.vectorize(mandelbrot)(c, max_iter)
    # 更新图像数据
    im.set_data(mandelbrot_set)
    # 更新标题
    ax.set_title(f'曼德勃罗集合 (迭代次数: {max_iter})')
    # 刷新图像
    fig.canvas.draw_idle()

# 绑定滑块事件
slider.on_changed(update)

plt.show()
